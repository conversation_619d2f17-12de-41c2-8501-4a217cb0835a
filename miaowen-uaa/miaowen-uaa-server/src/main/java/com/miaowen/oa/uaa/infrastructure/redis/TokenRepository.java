package com.miaowen.oa.uaa.infrastructure.redis;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.miaowen.oa.framework.common.util.json.JsonUtils;
import com.miaowen.oa.uaa.infrastructure.redis.entity.RefreshTokenEntity;
import com.miaowen.oa.uaa.infrastructure.redis.entity.TokenEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Token Redis 存储库
 *
 * 负责 AccessToken 和 RefreshToken 的 Redis 存储操作
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TokenRepository {

    // Redis Key 模板
    private static final String ACCESS_TOKEN_KEY = "uaa:access_token:%s";
    private static final String REFRESH_TOKEN_KEY = "uaa:refresh_token:%s";
    private static final String USER_TOKEN_KEY = "uaa:user_token:%s";

    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 存储访问令牌
     *
     * @param accessToken 访问令牌
     * @param refreshToken 关联的刷新令牌
     * @param userId 用户ID
     * @param expiresTime 过期时间
     * @param expireSeconds 过期秒数
     */
    public void storeAccessToken(String accessToken, String refreshToken, Long userId,
                               LocalDateTime expiresTime, long expireSeconds) {
        // 构建令牌信息（包含关联的刷新令牌）
        TokenEntity tokenEntity = new TokenEntity(userId, refreshToken,
            expiresTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        String tokenInfoJson = JsonUtils.toJsonString(tokenEntity);
        String key = String.format(ACCESS_TOKEN_KEY, accessToken);

        // 存储到 Redis，设置过期时间
        stringRedisTemplate.opsForValue().set(key, tokenInfoJson, expireSeconds, TimeUnit.SECONDS);

        // 存储用户与令牌的映射关系（用于单点登录控制）
        String userTokenKey = String.format(USER_TOKEN_KEY, userId);
        stringRedisTemplate.opsForValue().set(userTokenKey, accessToken, expireSeconds, TimeUnit.SECONDS);

        log.debug("存储访问令牌成功: userId={}, token={}", userId, accessToken);
    }

    /**
     * 存储刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @param accessToken 关联的访问令牌
     * @param userId 用户ID
     * @param expireSeconds 过期秒数
     */
    public void storeRefreshToken(String refreshToken, String accessToken, Long userId, long expireSeconds) {
        RefreshTokenEntity refreshTokenEntity = new RefreshTokenEntity(userId, accessToken,
            LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        String refreshInfoJson = JsonUtils.toJsonString(refreshTokenEntity);
        String key = String.format(REFRESH_TOKEN_KEY, refreshToken);

        // 存储到 Redis，设置过期时间
        stringRedisTemplate.opsForValue().set(key, refreshInfoJson, expireSeconds, TimeUnit.SECONDS);
        log.debug("存储刷新令牌成功: userId={}, refreshToken={}", userId, refreshToken);
    }

    /**
     * 获取访问令牌信息
     *
     * @param accessToken 访问令牌
     * @return 令牌信息，如果不存在返回 null
     */
    public TokenEntity getAccessTokenInfo(String accessToken) {
        String key = String.format(ACCESS_TOKEN_KEY, accessToken);
        String tokenInfoJson = stringRedisTemplate.opsForValue().get(key);

        if (!StringUtils.hasText(tokenInfoJson)) {
            log.debug("访问令牌不存在: token={}", accessToken);
            return null;
        }

        return JsonUtils.parseObject(tokenInfoJson, TokenEntity.class);
    }

    /**
     * 获取刷新令牌信息
     *
     * @param refreshToken 刷新令牌
     * @return 刷新令牌信息，如果不存在返回 null
     */
    public RefreshTokenEntity getRefreshTokenInfo(String refreshToken) {
        String key = String.format(REFRESH_TOKEN_KEY, refreshToken);
        String refreshInfoJson = stringRedisTemplate.opsForValue().get(key);

        if (!StringUtils.hasText(refreshInfoJson)) {
            log.debug("刷新令牌不存在或已过期: refreshToken={}", refreshToken);
            return null;
        }

        return JsonUtils.parseObject(refreshInfoJson, RefreshTokenEntity.class);
    }

    /**
     * 删除访问令牌
     *
     * @param accessToken 访问令牌
     */
    public void deleteAccessToken(String accessToken) {
        String key = String.format(ACCESS_TOKEN_KEY, accessToken);
        Boolean deleted = stringRedisTemplate.delete(key);
        log.debug("删除访问令牌: token={}, deleted={}", accessToken, deleted);
    }

    /**
     * 删除刷新令牌
     *
     * @param refreshToken 刷新令牌
     */
    public void deleteRefreshToken(String refreshToken) {
        String key = String.format(REFRESH_TOKEN_KEY, refreshToken);
        Boolean deleted = stringRedisTemplate.delete(key);
        log.debug("删除刷新令牌: refreshToken={}, deleted={}", refreshToken, deleted);
    }

    /**
     * 删除用户的所有令牌（用于登出或踢出用户）
     *
     * @param userId 用户ID
     */
    public void deleteUserTokens(Long userId) {
        try {
            // 获取用户当前的访问令牌
            String userTokenKey = String.format(USER_TOKEN_KEY, userId);
            String accessToken = stringRedisTemplate.opsForValue().get(userTokenKey);

            if (StringUtils.hasText(accessToken)) {
                // 删除访问令牌
                deleteAccessToken(accessToken);

                // 删除用户令牌映射
                stringRedisTemplate.delete(userTokenKey);

                log.debug("删除用户所有令牌: userId={}, accessToken={}", userId, accessToken);
            }
        } catch (Exception e) {
            log.error("删除用户令牌失败: userId={}", userId, e);
        }
    }

    /**
     * 检查访问令牌是否存在且有效
     *
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    public boolean isAccessTokenValid(String accessToken) {
        String key = String.format(ACCESS_TOKEN_KEY, accessToken);
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(key));
    }

    /**
     * 获取访问令牌关联的刷新令牌
     *
     * @param accessToken 访问令牌
     * @return 关联的刷新令牌，如果不存在返回 null
     */
    public String getRefreshTokenByAccessToken(String accessToken) {
        try {
            TokenEntity accessTokenInfo = getAccessTokenInfo(accessToken);
            if (Objects.nonNull(accessTokenInfo)) {
                return accessTokenInfo.getRefreshToken();
            }
            return null;
        } catch (Exception e) {
            log.error("获取关联刷新令牌失败: accessToken={}", accessToken, e);
            return null;
        }
    }
}

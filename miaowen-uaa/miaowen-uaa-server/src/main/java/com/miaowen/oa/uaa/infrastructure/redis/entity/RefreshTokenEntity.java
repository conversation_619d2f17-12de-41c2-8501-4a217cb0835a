package com.miaowen.oa.uaa.infrastructure.redis.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * RefreshTokenEntity :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefreshTokenEntity {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 刷新令牌
     */
    private String accessToken;

    /**
     * 创建时间（时间戳）
     */
    private String createTime;
}

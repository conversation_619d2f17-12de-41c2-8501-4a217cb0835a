package com.miaowen.oa.uaa.framework.security.config;

import com.miaowen.oa.framework.security.config.AuthorizeRequestsCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * UAA 模块的 Security 配置
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false, value = "uaaSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("uaaAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                log.debug("配置UAA模块的Security权限...");

                // Swagger 接口文档
                registry.requestMatchers("/v3/api-docs/**").permitAll()
                        .requestMatchers("/webjars/**").permitAll()
                        .requestMatchers("/swagger-ui").permitAll()
                        .requestMatchers("/swagger-ui/**").permitAll();

                // Druid 监控
                registry.requestMatchers("/druid/**").permitAll();

                // Spring Boot Actuator 的安全配置
                registry.requestMatchers("/actuator").permitAll()
                        .requestMatchers("/actuator/**").permitAll();
                // RPC 服务的安全配置
                registry.requestMatchers("/rpc-api/uaa" + "/**").permitAll();
//                registry.requestMatchers(RpcConstants.RPC_API_PREFIX + "/**").permitAll();
            }

        };
    }

}

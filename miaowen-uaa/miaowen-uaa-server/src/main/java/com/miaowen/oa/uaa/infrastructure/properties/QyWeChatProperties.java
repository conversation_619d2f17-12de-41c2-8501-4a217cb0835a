package com.miaowen.oa.uaa.infrastructure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "system.qy-wechat")
public class QyWeChatProperties {
    //以下为企业微信权限相关参数
    private String corpId;
    private String redirectUri;
    private String corpSecret;
    private String agentId;
    private String qyWechatHost = "https://qyapi.weixin.qq.com/cgi-bin";

    //以下为业务参数
    private Boolean pullQyStaff = true;
    private String token;
    private String encodingAesKey;
    private String oaWebHost;

}
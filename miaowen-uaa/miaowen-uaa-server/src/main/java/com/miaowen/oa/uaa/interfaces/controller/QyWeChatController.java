package com.miaowen.oa.uaa.interfaces.controller;

import com.miaowen.oa.uaa.application.service.LoginApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-18
 */
@Slf4j
@Controller
@RequestMapping("/v1/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "认证中心-企业微信登录二维码")
public class QyWeChatController {

    private final LoginApplicationService loginApplicationService;

    @PermitAll
    @GetMapping("/qy-wechat/auth-url")
    @Operation(summary = "获取企业微信授权URL")
    public String getWechatEnterpriseAuthUrl(@Parameter(description = "状态参数", required = true)
                                             @RequestParam("state") String state) {
        return "redirect:" + loginApplicationService.getWechatEnterpriseAuthUrl(state);
    }

    @PermitAll
    @GetMapping("/qy-wechat/oauth2")
    @Operation(summary = "获取企业微信授权URL")
    public String getWechatOAuthUrl(@Parameter(description = "状态参数", required = true)
                                             @RequestParam("state") String state) {
        return "redirect:" + loginApplicationService.getOAuthUrl(state);
    }
}

package com.miaowen.oa.uaa.domain.gateway;

import com.miaowen.oa.uaa.domain.model.WechatUserInfo;

/**
 * 企业微信服务接口
 *
 * <AUTHOR>
 */
public interface QyWechatGateway {

    /**
     * 获取企业微信授权URL
     *
     * @param redirectUri 回调地址
     * @param state 状态参数
     * @return 授权URL
     */
    String getAuthUrl(String state);

    /**
     * 通过授权码获取用户信息
     *
     * @param code 授权码
     * @param state 状态参数
     * @return 用户信息
     */
    WechatUserInfo getUserInfo(String code);


    String getOAuthUrl(String state);

}

package com.miaowen.oa.uaa.infrastructure.gateway;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.api.user.AdminUserApi;
import com.miaowen.oa.system.api.user.dto.UserInfoDTO;
import com.miaowen.oa.uaa.domain.gateway.AuthUserGateway;
import com.miaowen.oa.uaa.domain.model.AuthUser;
import com.miaowen.oa.uaa.infrastructure.convertor.UserConvertor;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * AuthUserGatewayImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Service
@AllArgsConstructor
public class AuthUserGatewayImpl implements AuthUserGateway {
    private final AdminUserApi adminUserApi;
    @Override
    public AuthUser findByUsername(String username) {
        CommonResult<UserInfoDTO> userByUsernameOrPhone = adminUserApi.getUserByUsernameOrPhone(username);
        if (Objects.isNull(userByUsernameOrPhone)){
            return null;
        }
        if (userByUsernameOrPhone.isError()){
            return null;
        }
        return UserConvertor.toAuthUser(userByUsernameOrPhone.getData());
    }

    @Override
    public AuthUser findByQyWechatUserId(String wechatUserId) {
        CommonResult<UserInfoDTO> userByUsernameOrPhone = adminUserApi.getUserByQyWechatUserId(wechatUserId);
        if (Objects.isNull(userByUsernameOrPhone)){
            return null;
        }
        if (userByUsernameOrPhone.isError()){
            return null;
        }
        return UserConvertor.toAuthUser(userByUsernameOrPhone.getData());
    }

}

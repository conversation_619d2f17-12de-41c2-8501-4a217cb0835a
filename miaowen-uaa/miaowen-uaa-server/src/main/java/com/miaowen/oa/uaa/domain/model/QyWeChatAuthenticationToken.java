package com.miaowen.oa.uaa.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.util.Collections;

/**
 * 企业微信登录token
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Setter
@Getter
@ToString
public class QyWeChatAuthenticationToken extends AbstractAuthenticationToken {
    /**
     * 授权码
     */
    private String userId;
    private String code;


    public QyWeChatAuthenticationToken() {
        super(Collections.emptySet());
    }

    public QyWeChatAuthenticationToken(String code) {
        super(Collections.emptySet());
        this.code = code;
    }

    public static QyWeChatAuthenticationToken createDetail(AuthUser authUser) {
        QyWeChatAuthenticationToken qyWeChatAuthenticationToken = new QyWeChatAuthenticationToken();
        qyWeChatAuthenticationToken.setDetails(authUser);
        return qyWeChatAuthenticationToken;
    }

    public static QyWeChatAuthenticationToken create(String code) {
        return new QyWeChatAuthenticationToken(code);
    }
    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return null;
    }
}

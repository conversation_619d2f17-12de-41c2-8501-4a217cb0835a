package com.miaowen.oa.uaa.infrastructure.convertor;

import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.uaa.domain.model.CaptchaInfo;
import com.miaowen.oa.uaa.interfaces.res.CaptchaResponse;

/**
 * 验证码相关对象转换器
 * 
 * 负责验证码相关的对象转换
 *
 * <AUTHOR>
 */
public class CaptchaConvertor {

    /**
     * 将第三方验证码库的响应转换为领域对象
     */
    public static CaptchaInfo toCaptchaInfo(String captchaId, String captcha, String captchaImage) {
        return new CaptchaInfo(captchaId, captcha, captchaImage);
    }

    public static CaptchaInfo toCaptchaInfo(String captchaId, String captcha) {
        return new CaptchaInfo(captchaId, captcha, null);
    }

    public static CaptchaResponse toResponse(CaptchaInfo captchaInfo) {
        return BeanUtils.toBean(captchaInfo, CaptchaResponse.class);
    }


}

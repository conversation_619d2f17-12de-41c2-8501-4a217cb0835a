package com.miaowen.oa.uaa.infrastructure.gateway;

import com.miaowen.oa.framework.common.exception.ServiceException;
import com.miaowen.oa.uaa.domain.gateway.TokenGateway;
import com.miaowen.oa.uaa.domain.model.AccessToken;
import com.miaowen.oa.uaa.infrastructure.convertor.AccessTokenConvertor;
import com.miaowen.oa.uaa.infrastructure.redis.TokenRepository;
import com.miaowen.oa.uaa.infrastructure.redis.entity.RefreshTokenEntity;
import com.miaowen.oa.uaa.infrastructure.redis.entity.TokenEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Token 网关实现
 *
 * 基于 Redis 的令牌存储实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TokenGatewayImpl implements TokenGateway {

    private final TokenRepository tokenRepository;

    /**
     * 访问令牌过期时间（小时）
     */
    @Value("${uaa.token.access-token-expire-hours:2}")
    private int accessTokenExpireHours;

    /**
     * 刷新令牌过期时间（天）
     */
    @Value("${uaa.token.refresh-token-expire-days:7}")
    private int refreshTokenExpireDays;

    @Override
    public AccessToken createAccessToken(Long userId) {
        try {
            // 1. 生成访问令牌和刷新令牌
            String accessToken = generateAccessToken();
            String refreshToken = generateRefreshToken();

            // 2. 计算过期时间
            LocalDateTime accessTokenExpiresTime = LocalDateTime.now().plusHours(accessTokenExpireHours);
            long accessTokenExpireSeconds = accessTokenExpireHours * 3600L;
            long refreshTokenExpireSeconds = refreshTokenExpireDays * 24 * 3600L;

            // 3. 删除用户之前的令牌（实现单点登录）
            tokenRepository.deleteUserTokens(userId);

            // 4. 存储新的访问令牌（包含关联的刷新令牌）
            tokenRepository.storeAccessToken(accessToken, refreshToken, userId, accessTokenExpiresTime, accessTokenExpireSeconds);

            // 5. 存储刷新令牌
            tokenRepository.storeRefreshToken(refreshToken, accessToken, userId, refreshTokenExpireSeconds);

            // 6. 创建返回对象
            AccessToken result = AccessToken.create(accessToken, refreshToken, accessTokenExpiresTime);
            result.setUserId(userId);

            log.info("创建访问令牌成功: userId={}, accessToken={}", userId, accessToken);
            return result;

        } catch (Exception e) {
            log.error("创建访问令牌失败: userId={}", userId, e);
            throw new ServiceException("创建访问令牌失败");
        }
    }

    @Override
    public AccessToken refreshAccessToken(String refreshToken) {
        try {
            // 验证刷新令牌
            if (!StringUtils.hasText(refreshToken)) {
                throw new ServiceException("刷新令牌不能为空");
            }

            // 获取刷新令牌信息（使用TokenEntity）
            RefreshTokenEntity refreshTokenInfo = tokenRepository.getRefreshTokenInfo(refreshToken);
            if (Objects.isNull(refreshTokenInfo) || Objects.isNull(refreshTokenInfo.getUserId())) {
                throw new ServiceException("刷新令牌无效或已过期");
            }

            // 获取关联的访问令牌信息
            String oldAccessToken = refreshTokenInfo.getAccessToken();

            // 删除旧的令牌
            if (StringUtils.hasText(oldAccessToken)) {
                tokenRepository.deleteAccessToken(oldAccessToken);
            }
            tokenRepository.deleteRefreshToken(refreshToken);

            // 创建新的访问令牌
            AccessToken newToken = createAccessToken(refreshTokenInfo.getUserId());

            log.info("刷新访问令牌成功: userId={}, oldAccessToken={}, newAccessToken={}",
                refreshTokenInfo.getUserId(), oldAccessToken, newToken.getAccessToken());

            return newToken;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("刷新访问令牌失败: refreshToken={}", refreshToken, e);
            throw new ServiceException("刷新访问令牌失败");
        }
    }

    @Override
    public void invalidAccessToken(String accessToken) {
        try {
            if (!StringUtils.hasText(accessToken)) {
                return;
            }

            // 1. 获取访问令牌信息（使用TokenEntity）
            TokenEntity tokenEntity = tokenRepository.getAccessTokenInfo(accessToken);
            if (Objects.nonNull(tokenEntity) && Objects.nonNull(tokenEntity.getUserId())) {
                // 2. 删除用户的所有令牌（包括访问令牌和刷新令牌）
                deleteAllUserTokens(tokenEntity.getUserId(), accessToken);

                log.info("注销访问令牌成功: userId={}, accessToken={}", tokenEntity.getUserId(), accessToken);
            } else {
                log.debug("访问令牌不存在或已过期: accessToken={}", accessToken);
            }

        } catch (Exception e) {
            log.error("注销访问令牌失败: accessToken={}", accessToken, e);
            // 注销失败不抛异常，避免影响用户体验
        }
    }

    @Override
    public boolean validateAccessToken(String accessToken) {
        try {
            if (!StringUtils.hasText(accessToken)) {
                return false;
            }

            return tokenRepository.isAccessTokenValid(accessToken);

        } catch (Exception e) {
            log.error("验证访问令牌失败: accessToken={}", accessToken, e);
            return false;
        }
    }

    @Override
    public AccessToken getFromAccessToken(String accessToken) {
        try {
            TokenEntity tokenEntity = tokenRepository.getAccessTokenInfo(accessToken);
            if (Objects.nonNull(tokenEntity)) {
                return AccessTokenConvertor.toAccessToken(tokenEntity);
            }
            return null;

        } catch (Exception e) {
            log.error("从访问令牌获取用户ID失败: accessToken={}", accessToken, e);
            return null;
        }
    }

    /**
     * 生成访问令牌
     */
    private String generateAccessToken() {
        return "access_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成刷新令牌
     */
    private String generateRefreshToken() {
        return "refresh_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 删除用户的所有令牌（包括访问令牌和刷新令牌）
     */
    private void deleteAllUserTokens(Long userId, String accessToken) {
        try {
            // 1. 查找与该访问令牌关联的刷新令牌
            String refreshToken = tokenRepository.getRefreshTokenByAccessToken(accessToken);

            // 2. 删除访问令牌
            tokenRepository.deleteAccessToken(accessToken);

            // 3. 删除刷新令牌
            if (StringUtils.hasText(refreshToken)) {
                tokenRepository.deleteRefreshToken(refreshToken);
                log.debug("删除关联的刷新令牌: refreshToken={}", refreshToken);
            }

            // 4. 删除用户令牌映射
            tokenRepository.deleteUserTokens(userId);

            log.debug("删除用户所有令牌完成: userId={}, accessToken={}, refreshToken={}",
                userId, accessToken, refreshToken);
        } catch (Exception e) {
            log.error("删除用户所有令牌失败: userId={}", userId, e);
        }
    }

}

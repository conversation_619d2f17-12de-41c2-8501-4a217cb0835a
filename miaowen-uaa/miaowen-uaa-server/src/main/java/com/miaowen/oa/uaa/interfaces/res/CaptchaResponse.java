package com.miaowen.oa.uaa.interfaces.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 验证码响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "验证码响应")
public class CaptchaResponse {

    @Schema(description = "验证码标识", example = "uuid-1234-5678")
    private String captchaId;

    @Schema(description = "验证码图片Base64编码", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    private String captchaImage;

    @Schema(description = "验证码类型", example = "login")
    private String captchaType;

    @Schema(description = "过期时间", example = "2024-12-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresTime;
}

package com.miaowen.oa.uaa.infrastructure.config;

import com.miaowen.oa.uaa.domain.service.auth.PasswordAuthenticationProvider;
import com.miaowen.oa.uaa.domain.service.auth.QyWechatAuthenticationProvider;
import com.miaowen.oa.uaa.infrastructure.gateway.AuthUserGatewayImpl;
import com.miaowen.oa.uaa.infrastructure.gateway.PasswordGatewayImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * UAA认证管理器配置
 * 
 * 专门配置AuthenticationManager和相关的AuthenticationProvider
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class UaaAuthenticationManagerConfig {

    private final AuthUserGatewayImpl authUserGateway;
    private final PasswordGatewayImpl passwordGateway;

    /**
     * 配置UAA专用的AuthenticationManager
     * 
     * @return AuthenticationManager实例
     */
    @Primary
    @Bean("uaaAuthenticationManager")
    public AuthenticationManager uaaAuthenticationManager() {
        log.debug("创建UAA专用AuthenticationManager...");
        
        // 创建密码认证提供者
        PasswordAuthenticationProvider passwordAuthenticationProvider = 
            new PasswordAuthenticationProvider(authUserGateway, passwordGateway);
        log.debug("创建密码认证提供者: {}", passwordAuthenticationProvider.getClass().getSimpleName());
        
        // 创建企业微信认证提供者
        QyWechatAuthenticationProvider qyWechatAuthenticationProvider = 
            new QyWechatAuthenticationProvider(authUserGateway);
        log.debug("创建企业微信认证提供者: {}", qyWechatAuthenticationProvider.getClass().getSimpleName());
        
        // 创建认证提供者列表
        List<org.springframework.security.authentication.AuthenticationProvider> providers = Arrays.asList(
            passwordAuthenticationProvider,
            qyWechatAuthenticationProvider
        );
        
        // 创建ProviderManager
        ProviderManager authenticationManager = new ProviderManager(providers);
        
        log.debug("UAA AuthenticationManager创建完成，包含 {} 个认证提供者", providers.size());
        
        return authenticationManager;
    }
}

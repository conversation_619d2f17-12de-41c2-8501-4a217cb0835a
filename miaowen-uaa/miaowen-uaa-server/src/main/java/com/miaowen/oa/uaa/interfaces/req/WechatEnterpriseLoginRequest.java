package com.miaowen.oa.uaa.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 企业微信登录请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "企业微信登录请求")
public class WechatEnterpriseLoginRequest {

    @Schema(description = "企业微信授权码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "授权码不能为空")
    private String code;
}

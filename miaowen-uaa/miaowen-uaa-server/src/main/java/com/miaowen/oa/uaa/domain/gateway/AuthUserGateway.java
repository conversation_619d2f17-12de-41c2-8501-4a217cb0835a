package com.miaowen.oa.uaa.domain.gateway;

import com.miaowen.oa.uaa.domain.model.AuthUser;

/**
 * 认证用户网关接口
 * 
 * 抽象认证相关的用户数据访问，隔离对系统域的依赖
 * 这是认证域访问系统域用户数据的防腐层
 *
 * <AUTHOR>
 */
public interface AuthUserGateway {

    /**
     * 根据用户名查找认证用户
     *
     * @param username 用户名
     * @return 认证用户对象
     */
    AuthUser findByUsername(String username);

    /**
     * 根据企业微信用户ID查找认证用户
     *
     * @param wechatUserId 企业微信用户ID
     * @return 认证用户对象
     */
    AuthUser findByQyWechatUserId(String wechatUserId);

}

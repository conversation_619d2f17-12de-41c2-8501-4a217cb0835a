package com.miaowen.oa.uaa.infrastructure.redis.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TokenEntity - 令牌实体对象
 * 用于封装从Redis中获取的令牌信息
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TokenEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 过期时间（字符串格式）
     */
    private String expiresTime;

    /**
     * 创建时间（时间戳）
     */
    private String createTime;


}

package com.miaowen.oa.uaa.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.authentication.AbstractAuthenticationToken;

import java.util.Collections;

/**
 * 密码登录token
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Setter
@Getter
@ToString
public class PasswordAuthenticationToken extends AbstractAuthenticationToken {
    /**
     * 手机号或者用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;


    public PasswordAuthenticationToken() {
        super(Collections.emptySet());
    }

    public PasswordAuthenticationToken(String username, String password) {
        super(Collections.emptySet());
        this.username = username;
        this.password = password;
    }

    public static PasswordAuthenticationToken createDetail(AuthUser authUser) {
        PasswordAuthenticationToken passwordAuthenticationToken = new PasswordAuthenticationToken();
        passwordAuthenticationToken.setDetails(authUser);
        return passwordAuthenticationToken;
    }

    public static PasswordAuthenticationToken create(String username, String password) {
        return new PasswordAuthenticationToken(username, password);
    }

    @Override
    public Object getCredentials() {
        return password;
    }

    @Override
    public Object getPrincipal() {
        return username;
    }


}

package com.miaowen.oa.uaa.infrastructure.convertor;


import com.miaowen.oa.framework.common.biz.system.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.uaa.domain.model.AccessToken;
import com.miaowen.oa.uaa.infrastructure.redis.entity.TokenEntity;
import com.miaowen.oa.uaa.interfaces.res.AccessTokenResponse;

import java.time.LocalDateTime;

/**
 * 登录相关对象转换器
 * 负责登录相关的对象转换（如果有 API 层的 DTO 的话）
 *
 * <AUTHOR>
 */
public class AccessTokenConvertor {

    public static AccessTokenResponse toResponse(AccessToken accessToken) {
        return BeanUtils.toBean(accessToken, AccessTokenResponse.class);
    }

    public static OAuth2AccessTokenCheckRespDTO toOAuthDTO(AccessToken accessToken) {
        return BeanUtils.toBean(accessToken, OAuth2AccessTokenCheckRespDTO.class);
    }

    public static AccessToken toAccessToken(TokenEntity tokenEntity) {
        AccessToken bean = BeanUtils.toBean(tokenEntity, AccessToken.class);
        bean.setExpiresTime(LocalDateTime.parse(tokenEntity.getExpiresTime()));
        return bean;
    }
}

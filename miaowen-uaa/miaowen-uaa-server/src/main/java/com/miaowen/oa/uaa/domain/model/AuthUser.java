package com.miaowen.oa.uaa.domain.model;

import lombok.Data;

import java.util.Objects;

/**
 * 认证用户
 * 
 * 认证域的用户概念，只包含认证相关的属性
 * 这是系统域 User 实体在认证域的防腐层对象
 *
 * <AUTHOR>
 */
@Data
public class AuthUser {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 密码（加密后）
     */
    private String password;

    /**
     * 企业微信用户ID
     */
    private String qyWechatUserId;

    /**
     * 用户状态（1:启用 0:禁用）
     */
    private Integer state;

    /**
     *加入时间
     */
    private Integer joinTime;
    /**
     *离职时间
     */
    private Integer leaveTime;
    /**
     * 是否可以密码模式登录
     */
    private Integer enablePasswordLogin;


    /**
     * 检查用户是否激活
     */
    public boolean isActive() {
        return state != null && state == 1;
    }

    /**
     * 检查用户是否激活
     */
    public boolean isJob() {
        boolean join = Objects.nonNull(joinTime) && joinTime > 0;
        boolean notLeave = Objects.isNull(leaveTime) || leaveTime ==0;
        return join && notLeave;
    }

    /**
     * 检查是否有密码
     */
    public boolean hasPassword() {
        return password != null && !password.trim().isEmpty();
    }

    /**
     * 检查是否绑定了企业微信
     */
    public boolean hasWechatBinding() {
        return qyWechatUserId != null && !qyWechatUserId.trim().isEmpty();
    }

}

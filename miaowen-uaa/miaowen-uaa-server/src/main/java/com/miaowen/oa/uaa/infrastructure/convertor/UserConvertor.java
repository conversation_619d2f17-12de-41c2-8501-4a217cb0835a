package com.miaowen.oa.uaa.infrastructure.convertor;


import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.system.api.user.dto.UserInfoDTO;
import com.miaowen.oa.uaa.domain.model.AuthUser;

import java.util.Objects;

/**
 * 用户对象转换器
 * 负责领域对象与基础设施层实体对象之间的转换
 *
 * <AUTHOR>
 */
public class UserConvertor {

    public static AuthUser toAuthUser(UserInfoDTO userInfoDTO) {
        AuthUser bean = BeanUtils.toBean(userInfoDTO, AuthUser.class);
        if (Objects.isNull(bean)){
            return null;
        }
        bean.setState(userInfoDTO.getUserState());
        return bean;
    }
}

package com.miaowen.oa.uaa.infrastructure.config;

import com.miaowen.oa.framework.security.config.AuthenticationSecurityConfig;
import com.miaowen.oa.uaa.domain.service.auth.PasswordAuthenticationProvider;
import com.miaowen.oa.uaa.domain.service.auth.QyWechatAuthenticationProvider;
import com.miaowen.oa.uaa.infrastructure.gateway.AuthUserGatewayImpl;
import com.miaowen.oa.uaa.infrastructure.gateway.PasswordGatewayImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;

/**
 * UaaAuthenticationSecurityConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Slf4j
@AllArgsConstructor
public class UaaAuthenticationSecurityConfig implements AuthenticationSecurityConfig {
    private final PasswordGatewayImpl passwordGateway;
    private final AuthUserGatewayImpl authUserGateway;

    @Override
    public void init(HttpSecurity http) {

    }

    @Override
    public void configure(HttpSecurity http) {
        log.debug("配置UAA认证提供者...");

        // 配置密码认证提供者
        PasswordAuthenticationProvider passwordAuthenticationProvider = new PasswordAuthenticationProvider(authUserGateway, passwordGateway);
        log.debug("创建密码认证提供者: {}", passwordAuthenticationProvider.getClass().getSimpleName());

        // 配置微信认证提供者
        QyWechatAuthenticationProvider qyWechatAuthenticationProvider = new QyWechatAuthenticationProvider(authUserGateway);
        log.debug("创建企业微信认证提供者: {}", qyWechatAuthenticationProvider.getClass().getSimpleName());

        // 注册认证提供者
        http.authenticationProvider(passwordAuthenticationProvider)
            .authenticationProvider(qyWechatAuthenticationProvider);

        log.debug("UAA认证提供者配置完成");
    }
}

package com.miaowen.oa.uaa.interfaces.controller;

import cn.hutool.http.server.HttpServerRequest;
import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.uaa.application.service.LoginApplicationService;
import com.miaowen.oa.uaa.domain.model.AccessToken;
import com.miaowen.oa.uaa.interfaces.req.UsernamePasswordLoginRequest;
import com.miaowen.oa.uaa.interfaces.req.WechatEnterpriseLoginRequest;
import com.miaowen.oa.uaa.interfaces.res.AccessTokenResponse;
import com.miaowen.oa.uaa.interfaces.res.CaptchaResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.miaowen.oa.framework.common.pojo.CommonResult.success;

/**
 * 登录控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "认证中心-登录认证")
public class LoginController {

    private final LoginApplicationService loginApplicationService;

    @PermitAll
    @PostMapping("/tokens/username-password")
    @Operation(summary = "账号密码登录")
    public CommonResult<AccessTokenResponse> createTokens(@Valid @RequestBody UsernamePasswordLoginRequest request) {
        AccessTokenResponse accessToken = loginApplicationService.usernamePasswordLogin(request);
        return success(accessToken);
    }

    @PermitAll
    @PostMapping("/tokens/qy-wechat")
    @Operation(summary = "企业微信登录")
    public CommonResult<AccessTokenResponse> wechatEnterpriseLogin(@Valid @RequestBody WechatEnterpriseLoginRequest request) {
        AccessTokenResponse accessToken = loginApplicationService.wechatEnterpriseLogin(request);
        return success(accessToken);
    }

    @PermitAll
    @PutMapping("/tokens")
    @Operation(summary = "刷新令牌")
    public CommonResult<AccessTokenResponse> refreshToken(@Parameter(description = "刷新令牌", required = true)
                                                  @RequestParam("refreshToken") String refreshToken, HttpServerRequest request) {
        AccessTokenResponse accessToken = loginApplicationService.refreshToken(refreshToken);
        return success(accessToken);
    }

    @DeleteMapping("/tokens")
    @Operation(summary = "登出")
    public CommonResult<Boolean> logout(@Parameter(description = "访问令牌", required = true)
                                       @RequestParam("accessToken") String accessToken) {
        loginApplicationService.logout(accessToken);
        return success(true);
    }

    @PermitAll
    @GetMapping("/captcha")
    @Operation(summary = "生成登录图形验证码")
    public CommonResult<CaptchaResponse> generateLoginCaptcha() {
        CaptchaResponse response = loginApplicationService.generateLoginCaptcha();
        return success(response);
    }




}

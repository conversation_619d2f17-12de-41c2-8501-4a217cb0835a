package com.miaowen.oa.uaa.domain.gateway;

/**
 * 密码网关接口
 * 
 * 抽象密码加密和验证的技术实现，隔离对Spring Security等外部框架的依赖
 *
 * <AUTHOR>
 */
public interface PasswordGateway {

    /**
     * 加密密码
     *
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    String encode(String rawPassword);

    /**
     * 验证密码
     *
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean matches(String rawPassword, String encodedPassword);
}

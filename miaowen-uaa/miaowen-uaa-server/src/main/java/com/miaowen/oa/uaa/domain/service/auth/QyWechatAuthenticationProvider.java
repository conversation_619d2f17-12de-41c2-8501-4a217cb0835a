package com.miaowen.oa.uaa.domain.service.auth;

import com.miaowen.oa.framework.common.exception.ServiceException;
import com.miaowen.oa.uaa.domain.gateway.AuthUserGateway;
import com.miaowen.oa.uaa.domain.model.*;
import com.miaowen.oa.uaa.domain.gateway.QyWechatGateway;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * WechatAuthenticationProvider :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Slf4j
@AllArgsConstructor
public class QyWechatAuthenticationProvider implements AuthenticationProvider {
    private final AuthUserGateway authUserGateway;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        QyWeChatAuthenticationToken qyWeChatAuthenticationToken = (QyWeChatAuthenticationToken) authentication;

        // 验证参数
        validateCredential(qyWeChatAuthenticationToken);

        // 查找绑定的用户
        AuthUser authUser = authUserGateway.findByQyWechatUserId(qyWeChatAuthenticationToken.getUserId());
        if (Objects.isNull(authUser)) {
            log.debug("企业微信用户未绑定系统用户: wechatUserId={}", qyWeChatAuthenticationToken.getUserId());
            return null;
        }

        // 验证用户状态
        if (!authUser.isActive()) {
            log.debug("用户已被禁用: wechatUserId={}", qyWeChatAuthenticationToken.getUserId());
            return null;
        }

        // 验证用户状态
        if (!authUser.isJob()) {
            log.debug("用户未入职: wechatUserId={}", qyWeChatAuthenticationToken.getUserId());
            return null;
        }
        log.debug("企业微信认证成功: wechatUserId={}, userId={}", qyWeChatAuthenticationToken.getUserId(), authUser.getUserId());
        return QyWeChatAuthenticationToken.createDetail(authUser);
    }
    /**
     * 验证登录凭证
     */
    private void validateCredential(QyWeChatAuthenticationToken credential) {
        if (!StringUtils.hasText(credential.getCode())) {
            throw new ServiceException("企业微信授权码不能为空");
        }
    }
    @Override
    public boolean supports(Class<?> authentication) {
        return QyWeChatAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

package com.miaowen.oa.uaa.infrastructure.gateway;

import com.miaowen.oa.uaa.domain.gateway.PasswordGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 密码网关实现（基于框架）
 *
 * 基于Spring Security的密码加密实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PasswordGatewayImpl implements PasswordGateway {

    private final PasswordEncoder passwordEncoder;

    @Override
    public String encode(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    @Override
    public boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}

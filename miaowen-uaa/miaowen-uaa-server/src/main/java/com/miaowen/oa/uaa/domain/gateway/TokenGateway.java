package com.miaowen.oa.uaa.domain.gateway;

import com.miaowen.oa.uaa.domain.model.AccessToken;

/**
 * 令牌领域服务接口
 * 负责令牌的业务逻辑，通过Gateway隔离技术实现
 *
 * <AUTHOR>
 */
public interface TokenGateway {

    /**
     * 创建登录令牌
     * 包含业务逻辑：
     * 1. 生成访问令牌和刷新令牌
     * 2. 设置令牌过期时间
     * 3. 记录登录信息
     *
     * @param userId 用户ID
     * @return 访问令牌对象
     */
    AccessToken createAccessToken(Long userId);

    /**
     * 刷新访问令牌
     * 包含业务逻辑：
     * 1. 验证刷新令牌有效性
     * 2. 生成新的访问令牌
     * 3. 可选地生成新的刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AccessToken refreshAccessToken(String refreshToken);

    /**
     * 登出（撤销令牌）
     * 包含业务逻辑：
     * 1. 撤销访问令牌
     * 2. 撤销关联的刷新令牌
     * 3. 记录登出信息
     *
     * @param accessToken 访问令牌
     */
    void invalidAccessToken(String accessToken);

    /**
     * 验证访问令牌是否有效
     *
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    boolean validateAccessToken(String accessToken);

    /**
     * 从访问令牌中获取用户ID
     *
     * @param accessToken 访问令牌
     * @return 用户ID，如果令牌无效返回null
     */
    AccessToken getFromAccessToken(String accessToken);
}

package com.miaowen.oa.uaa.domain.service.auth;

import com.miaowen.oa.framework.common.exception.ServiceException;
import com.miaowen.oa.uaa.domain.gateway.AuthUserGateway;
import com.miaowen.oa.uaa.domain.gateway.PasswordGateway;
import com.miaowen.oa.uaa.domain.model.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import java.util.Objects;

/**
 * 密码模式认证,会查询账号和手机号
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Slf4j
@AllArgsConstructor
public class PasswordAuthenticationProvider implements AuthenticationProvider {
    private final AuthUserGateway authUserGateway;
    private final PasswordGateway passwordService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        PasswordAuthenticationToken passwordAuthenticationToken = (PasswordAuthenticationToken) authentication;
        // 查找用户
        AuthUser authUser = authUserGateway.findByUsername(passwordAuthenticationToken.getUsername());
        if (Objects.isNull(authUser)) {
            log.debug("用户名或手机号不存在: username={}", passwordAuthenticationToken.getUsername());
            throw new ServiceException("用户名或密码错误");
        }

        // 验证密码
        if (!passwordService.matches(passwordAuthenticationToken.getPassword(), authUser.getPassword())) {
            log.debug("密码验证失败: username={}", passwordAuthenticationToken.getUsername());
            throw new ServiceException("用户名或密码错误");
        }

        //验证用户状态
        if (!authUser.isActive()) {
            log.debug("用户已被禁用: username={}", passwordAuthenticationToken.getUsername());
            throw new ServiceException("用户已被禁用");
        }

        log.debug("用户名密码认证成功: username={}, userId={}", passwordAuthenticationToken.getUsername(), authUser.getUserId());
        return PasswordAuthenticationToken.createDetail(authUser);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return PasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

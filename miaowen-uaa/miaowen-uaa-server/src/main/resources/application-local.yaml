server:
  port: 48083
spring:
  cloud:
    nacos:
      server-addr: 127.0.0.1:8848 # Nacos 服务器地址
      username: nacos
      password: nacos
      discovery: # 【配置中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
      config: # 【注册中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
--- #################### 数据库相关配置 ####################
spring:
  autoconfigure:
    # noinspection SpringBootApplicationYaml
    exclude:
      - de.codecentric.boot.admin.server.config.AdminServerAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
      - de.codecentric.boot.admin.server.ui.config.AdminServerUiAutoConfiguration # 禁用 Spring Boot Admin 的 Server UI 的自动配置
      - de.codecentric.boot.admin.server.cloud.config.AdminServerDiscoveryAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration # 禁用 Spring Boot Admin 的 Client 的自动配置
      - org.springframework.ai.autoconfigure.vectorstore.qdrant.QdrantVectorStoreAutoConfiguration # 禁用 AI 模块的 Qdrant，手动创建
      - org.springframework.ai.autoconfigure.vectorstore.milvus.MilvusVectorStoreAutoConfiguration # 禁用 AI 模块的 Milvus，手动创建
  # 数据源配置项
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 1 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: *********************************************************************************************************
          username: chenweihao
          password: tMWDChnQ

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6379 # 端口
      database: 0 # 数据库索引
  #    password: dev # 密码，建议生产环境开启

--- #################### 定时任务相关配置 ####################

xxl:
  job:
    enabled: false # 是否开启调度中心，默认为 true 开启
    admin:
      addresses: http://127.0.0.1:9090/xxl-job-admin # 调度中心部署跟地址

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  name-server: 127.0.0.1:9876 # RocketMQ Namesrv

spring:
  # RabbitMQ 配置项，对应 RabbitProperties 配置类
  rabbitmq:
    # 临时禁用RabbitMQ连接，避免启动错误
    # 如需启用，请确保RabbitMQ服务正常运行并配置正确的用户名密码
    host: 127.0.0.1        # RabbitMQ 服务器地址
    port: 5672              # 默认端口
    username: rabbitmq         # 使用默认用户名
    password: rabbitmq         # 使用默认密码
    virtual-host: /         # 使用默认虚拟主机
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000



--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.miaowen.oa.bpm.dal.mysql: debug
    com.miaowen.oa.infra.dal.mysql: debug
    com.miaowen.oa.infra.dal.mysql.logger.ApiErrorLogMapper: INFO # 配置 ApiErrorLogMapper 的日志级别为 info，避免和 GlobalExceptionHandler 重复打印
    com.miaowen.oa.infra.dal.mysql.job.JobLogMapper: INFO # 配置 JobLogMapper 的日志级别为 info
    com.miaowen.oa.infra.dal.mysql.file.FileConfigMapper: INFO # 配置 FileConfigMapper 的日志级别为 info
    com.miaowen.oa.pay.dal.mysql: debug
    com.miaowen.oa.pay.dal.mysql.notify.PayNotifyTaskMapper: INFO # 配置 PayNotifyTaskMapper 的日志级别为 info
    com.miaowen.oa.system.dal.mysql: debug
    com.miaowen.oa.system.dal.mysql.sms.SmsChannelMapper: INFO # 配置 SmsChannelMapper 的日志级别为 info
    com.miaowen.oa.tool.dal.mysql: debug
    com.miaowen.oa.member.dal.mysql: debug
    com.miaowen.oa.trade.dal.mysql: debug
    com.miaowen.oa.promotion.dal.mysql: debug
    com.miaowen.oa.statistics.dal.mysql: debug
    com.miaowen.oa.crm.dal.mysql: debug
    com.miaowen.oa.erp.dal.mysql: debug
    com.miaowen.oa.iot.dal.mysql: debug
    com.miaowen.oa.iot.dal.tdengine: DEBUG
    com.miaowen.oa.ai.dal.mysql: debug
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR # TODO 芋艿：先禁用，Spring Boot 3.X 存在部分错误的 WARN 提示

debug: false
# 秒闻配置项，设置当前项目所有自定义的配置
system:
  qy-wechat:
    corp-id: wwfc7e4cbb39cb5e55
    corp-secret: Ae2593JIBp1BoZB5AJp2KVPzkOk5BNaat5ECoYp99_w
    agent-id: 1000007
    qy-wechat-host: https://qyapi.weixin.qq.com/cgi-bin
    pull-qy-staff: true
    token:
    encoding-aes-key:
    oa-webHost: https://csoa.1cece.top
  security:
    mock-enable: true
  access-log: # 访问日志的配置项
    enable: false
  demo: false # 关闭演示模式

# 企业微信集成配置示例
miaowen:
  # 企业微信配置
  qy-wechat:
    corp-id: wwfc7e4cbb39cb5e55
    corp-secret: Ae2593JIBp1BoZB5AJp2KVPzkOk5BNaat5ECoYp99_w
    agent-id: 1000007
    qy-wechat-host: https://qyapi.weixin.qq.com/cgi-bin
    pull-qy-staff: true
    token:
    encoding-aes-key:
    oa-webHost: https://csoa.1cece.top
    # HTTP客户端配置
    http-client:
      connect-timeout: 10      # 连接超时10秒
      read-timeout: 30         # 读取超时30秒
      max-retries: 3           # 最大重试3次
      enable-request-logging: false  # 是否启用请求日志

    # Token缓存配置
    token-cache:
      key-prefix: qy_wechat_token_
      expire-offset: 60



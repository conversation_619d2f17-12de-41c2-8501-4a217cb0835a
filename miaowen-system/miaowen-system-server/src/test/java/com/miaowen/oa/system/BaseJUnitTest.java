package com.miaowen.oa.system;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 纯JUnit 5测试基类
 * 
 * 不依赖任何外部框架，专注于业务逻辑测试
 *
 * <AUTHOR>
 */
public abstract class BaseJUnitTest {
    
    /**
     * 测试用的企业微信用户ID前缀
     */
    protected static final String TEST_QY_WECHAT_USER_PREFIX = "test_wechat_user_";
    
    /**
     * 原子计数器，确保生成唯一的测试ID
     */
    private static final AtomicLong COUNTER = new AtomicLong(0);
    
    /**
     * 生成测试用的企业微信用户ID
     */
    protected String generateTestQyWechatUserId() {
        return TEST_QY_WECHAT_USER_PREFIX + System.currentTimeMillis() + "_" + COUNTER.incrementAndGet();
    }
    
    /**
     * 生成指定后缀的测试企业微信用户ID
     */
    protected String generateTestQyWechatUserId(String suffix) {
        return TEST_QY_WECHAT_USER_PREFIX + suffix + "_" + COUNTER.incrementAndGet();
    }
    
    /**
     * 生成批量测试企业微信用户ID
     */
    protected String[] generateBatchTestQyWechatUserIds(int count) {
        String[] qyWechatUserIds = new String[count];
        for (int i = 0; i < count; i++) {
            qyWechatUserIds[i] = generateTestQyWechatUserId("batch_" + i);
        }
        return qyWechatUserIds;
    }
    
    /**
     * 简单的断言方法
     */
    protected void assertNotNull(Object obj, String message) {
        if (obj == null) {
            throw new AssertionError(message);
        }
    }
    
    protected void assertEquals(Object expected, Object actual, String message) {
        if (!java.util.Objects.equals(expected, actual)) {
            throw new AssertionError(message + " - Expected: " + expected + ", Actual: " + actual);
        }
    }
    
    protected void assertNull(Object obj, String message) {
        if (obj != null) {
            throw new AssertionError(message + " - Expected null but was: " + obj);
        }
    }
    
    protected void assertTrue(boolean condition, String message) {
        if (!condition) {
            throw new AssertionError(message);
        }
    }
    
    protected void assertFalse(boolean condition, String message) {
        if (condition) {
            throw new AssertionError(message);
        }
    }
}

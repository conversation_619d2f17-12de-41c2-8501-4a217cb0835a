package com.miaowen.oa.system.application.repository;

import com.miaowen.oa.framework.qywechat.core.QyWechatOpenClient;
import com.miaowen.oa.framework.qywechat.dto.QyUserInfoDTO;
import com.miaowen.oa.framework.qywechat.dto.UserDetailDTO;
import com.miaowen.oa.system.infrastructure.entity.OaUserEntity;
import com.miaowen.oa.system.infrastructure.mapper.OaUserMapper;
import com.miaowen.oa.system.interfaces.req.user.UserCreateRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * OaUserService.createUserIfNotExists 方法测试
 * 所有企业微信数据都使用Mock
 *
 * <AUTHOR>
 */
@DisplayName("用户创建服务测试")
class OaUserServiceCreateUserTest {

    private OaUserMapper oaUserMapper;
    private QyWechatOpenClient qyWechatOpenClient;
    private OaUserService oaUserService;

    @BeforeEach
    void setUp() {
        // 创建Mock对象
        oaUserMapper = mock(OaUserMapper.class);
        qyWechatOpenClient = mock(QyWechatOpenClient.class);
        
        // 创建服务实例并注入Mock依赖
        oaUserService = new OaUserService();
        ReflectionTestUtils.setField(oaUserService, "oaUserMapper", oaUserMapper);
        ReflectionTestUtils.setField(oaUserService, "qyWechatOpenClient", qyWechatOpenClient);
    }

    @Test
    @DisplayName("创建用户 - 完整信息（qyWechatUserId + userTicket）")
    void createUserIfNotExists_WithFullInfo_Success() {
        // Given: 准备测试数据
        UserCreateRequest request = new UserCreateRequest();
        request.setQyWechatUserId("zhangsan001");
        request.setUserTicket("ticket_12345");

        // Mock企业微信用户基本信息
        QyUserInfoDTO qyUserInfo = new QyUserInfoDTO();
        qyUserInfo.setUserId("zhangsan001");
        qyUserInfo.setName("张三");

        // Mock企业微信用户详细信息
        UserDetailDTO userDetail = new UserDetailDTO();
        userDetail.setUserId("zhangsan001");
        userDetail.setMobile("13800138000");
        userDetail.setGender("1"); // 1-男性
        userDetail.setEmail("<EMAIL>");
        userDetail.setAvatar("http://avatar.url/zhangsan.jpg");

        // Mock数据库操作
        when(oaUserMapper.selectOne(any())).thenReturn(null); // 用户不存在
        when(qyWechatOpenClient.getUser("zhangsan001")).thenReturn(qyUserInfo);
        when(qyWechatOpenClient.getUserDetail("ticket_12345")).thenReturn(userDetail);
        when(oaUserMapper.insert(any(OaUserEntity.class))).thenReturn(1);

        // When: 执行创建用户
        assertDoesNotThrow(() -> oaUserService.createUserIfNotExists(request));

        // Then: 验证企业微信API调用
        verify(qyWechatOpenClient, times(1)).getUser("zhangsan001");
        verify(qyWechatOpenClient, times(1)).getUserDetail("ticket_12345");

        // 验证用户创建
        verify(oaUserMapper, times(1)).insert(argThat((OaUserEntity user) -> {
            // 验证从getUser获取的信息
            assertEquals("zhangsan001", user.getQyWechatUserId());
            assertEquals("张三", user.getUsername());
            assertEquals("张三", user.getRealName());

            // 验证从getUserDetail获取的信息
            assertEquals("13800138000", user.getPhone());
            assertEquals(1, user.getGender());
            assertEquals("<EMAIL>", user.getEmail());
            assertEquals("http://avatar.url/zhangsan.jpg", user.getAvatarUrl());

            // 验证核心业务规则
            assertNull(user.getUserCode()); // 没有工号
            assertNull(user.getJoinTime()); // 没有入职时间
            assertEquals(0, user.getEnablePasswordLogin()); // 不允许密码登录
            assertEquals(1, user.getState()); // 正常状态

            return true;
        }));
    }

    @Test
    @DisplayName("创建用户 - 仅基本信息（只有qyWechatUserId）")
    void createUserIfNotExists_OnlyBasicInfo_Success() {
        // Given: 只有qyWechatUserId
        UserCreateRequest request = new UserCreateRequest();
        request.setQyWechatUserId("lisi002");
        // userTicket为null

        // Mock企业微信用户基本信息
        QyUserInfoDTO qyUserInfo = new QyUserInfoDTO();
        qyUserInfo.setUserId("lisi002");
        qyUserInfo.setName("李四");

        // Mock数据库操作
        when(oaUserMapper.selectOne(any())).thenReturn(null);
        when(qyWechatOpenClient.getUser("lisi002")).thenReturn(qyUserInfo);
        when(oaUserMapper.insert(any(OaUserEntity.class))).thenReturn(1);

        // When: 执行创建用户
        assertDoesNotThrow(() -> oaUserService.createUserIfNotExists(request));

        // Then: 验证API调用
        verify(qyWechatOpenClient, times(1)).getUser("lisi002");
        verify(qyWechatOpenClient, never()).getUserDetail(any(String.class)); // 不调用getUserDetail

        // 验证用户创建
        verify(oaUserMapper, times(1)).insert(argThat((OaUserEntity user) -> {
            // 验证基本信息
            assertEquals("lisi002", user.getQyWechatUserId());
            assertEquals("李四", user.getUsername());
            assertEquals("李四", user.getRealName());

            // 验证详细信息使用默认值
            assertEquals("", user.getPhone());
            assertEquals(0, user.getGender());
            assertEquals("", user.getEmail());
            assertEquals("", user.getAvatarUrl());

            return true;
        }));
    }

    @Test
    @DisplayName("用户已存在 - 有userTicket时更新详细信息")
    void createUserIfNotExists_ExistingUserWithTicket_UpdateDetails() {
        // Given: 用户已存在
        OaUserEntity existingUser = new OaUserEntity();
        existingUser.setId(100L);
        existingUser.setQyWechatUserId("wangwu003");
        existingUser.setUsername("王五");
        existingUser.setRealName("王五");
        existingUser.setPhone("13900139000");
        existingUser.setGender(2); // 2-女性
        existingUser.setEmail("<EMAIL>");
        existingUser.setAvatarUrl("http://old.avatar.url");

        UserCreateRequest request = new UserCreateRequest();
        request.setQyWechatUserId("wangwu003");
        request.setUserTicket("new_ticket_67890");

        // Mock企业微信用户详细信息（新的）
        UserDetailDTO newUserDetail = new UserDetailDTO();
        newUserDetail.setUserId("wangwu003");
        newUserDetail.setMobile("13800138888"); // 新手机号
        newUserDetail.setGender("1"); // 改为男性
        newUserDetail.setEmail("<EMAIL>"); // 新邮箱
        newUserDetail.setAvatar("http://new.avatar.url"); // 新头像

        // Mock数据库操作
        when(oaUserMapper.selectOne(any())).thenReturn(existingUser);
        when(qyWechatOpenClient.getUserDetail("new_ticket_67890")).thenReturn(newUserDetail);
        when(oaUserMapper.updateById(any(OaUserEntity.class))).thenReturn(1);

        // When: 执行创建用户（实际是更新）
        assertDoesNotThrow(() -> oaUserService.createUserIfNotExists(request));

        // Then: 验证API调用
        verify(qyWechatOpenClient, never()).getUser(any(String.class)); // 不调用getUser
        verify(qyWechatOpenClient, times(1)).getUserDetail("new_ticket_67890");

        // 验证不创建新用户
        verify(oaUserMapper, never()).insert(any(OaUserEntity.class));

        // 验证更新用户
        verify(oaUserMapper, times(1)).updateById(argThat((OaUserEntity user) -> {
            // 验证四个字段被更新
            assertEquals("13800138888", user.getPhone());
            assertEquals(1, user.getGender());
            assertEquals("<EMAIL>", user.getEmail());
            assertEquals("http://new.avatar.url", user.getAvatarUrl());

            // 验证其他字段不变
            assertEquals("wangwu003", user.getQyWechatUserId());
            assertEquals("王五", user.getRealName());

            return true;
        }));
    }

    @Test
    @DisplayName("用户已存在 - 没有userTicket时不更新")
    void createUserIfNotExists_ExistingUserWithoutTicket_NoUpdate() {
        // Given: 用户已存在，没有userTicket
        OaUserEntity existingUser = new OaUserEntity();
        existingUser.setId(200L);
        existingUser.setQyWechatUserId("existing_user");

        UserCreateRequest request = new UserCreateRequest();
        request.setQyWechatUserId("existing_user");
        // userTicket为null

        when(oaUserMapper.selectOne(any())).thenReturn(existingUser);

        // When: 执行创建用户
        assertDoesNotThrow(() -> oaUserService.createUserIfNotExists(request));

        // Then: 验证不调用任何企业微信API
        verify(qyWechatOpenClient, never()).getUser(any(String.class));
        verify(qyWechatOpenClient, never()).getUserDetail(any(String.class));

        // 验证不进行数据库操作
        verify(oaUserMapper, never()).insert(any(OaUserEntity.class));
        verify(oaUserMapper, never()).updateById(any(OaUserEntity.class));
    }

    @Test
    @DisplayName("企业微信API失败 - 使用默认值创建用户")
    void createUserIfNotExists_QyWechatApiFailed_UseDefaultValues() {
        // Given: 企业微信API调用失败
        UserCreateRequest request = new UserCreateRequest();
        request.setQyWechatUserId("failed_user");
        request.setUserTicket("failed_ticket");

        // Mock数据库操作
        when(oaUserMapper.selectOne(any())).thenReturn(null);
        when(qyWechatOpenClient.getUser("failed_user")).thenThrow(new RuntimeException("getUser API失败"));
        when(qyWechatOpenClient.getUserDetail("failed_ticket")).thenThrow(new RuntimeException("getUserDetail API失败"));
        when(oaUserMapper.insert(any(OaUserEntity.class))).thenReturn(1);

        // When: 执行创建用户
        assertDoesNotThrow(() -> oaUserService.createUserIfNotExists(request));

        // Then: 验证API被调用但失败
        verify(qyWechatOpenClient, times(1)).getUser("failed_user");
        verify(qyWechatOpenClient, times(1)).getUserDetail("failed_ticket");

        // 验证使用默认值创建用户
        verify(oaUserMapper, times(1)).insert(argThat((OaUserEntity user) -> {
            assertEquals("failed_user", user.getQyWechatUserId());
            assertEquals("failed_user", user.getUsername()); // 使用企业微信ID作为用户名
            assertEquals("", user.getRealName()); // 默认值
            assertEquals("", user.getPhone()); // 默认值
            assertEquals(0, user.getGender()); // 默认值
            assertEquals("", user.getEmail()); // 默认值
            assertEquals("", user.getAvatarUrl()); // 默认值

            return true;
        }));
    }

    @Test
    @DisplayName("参数校验 - 请求为null")
    void createUserIfNotExists_NullRequest_ThrowException() {
        // When & Then: 传入null请求应该抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> oaUserService.createUserIfNotExists(null)
        );
        
        assertEquals("企业微信用户ID不能为空", exception.getMessage());
        
        // 验证没有调用任何方法
        verify(oaUserMapper, never()).selectOne(any());
        verify(qyWechatOpenClient, never()).getUser(any(String.class));
        verify(qyWechatOpenClient, never()).getUserDetail(any(String.class));
    }

    @Test
    @DisplayName("参数校验 - qyWechatUserId为空")
    void createUserIfNotExists_EmptyQyWechatUserId_ThrowException() {
        // Given: qyWechatUserId为空的请求
        UserCreateRequest request = new UserCreateRequest();
        request.setQyWechatUserId("");
        request.setUserTicket("ticket_123");

        // When & Then: 应该抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> oaUserService.createUserIfNotExists(request)
        );
        
        assertEquals("企业微信用户ID不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("性别字段解析测试")
    void createUserIfNotExists_GenderParsing_Success() {
        // Given: 测试不同的性别值
        String[] genderValues = {"1", "2", "", "invalid", null};
        int[] expectedGenders = {1, 2, 0, 0, 0}; // 对应的期望值

        for (int i = 0; i < genderValues.length; i++) {
            String genderValue = genderValues[i];
            int expectedGender = expectedGenders[i];

            // 重置Mock
            reset(oaUserMapper, qyWechatOpenClient);

            UserCreateRequest request = new UserCreateRequest();
            request.setQyWechatUserId("test_user_" + i);
            request.setUserTicket("test_ticket_" + i);

            // Mock企业微信返回数据
            QyUserInfoDTO qyUserInfo = new QyUserInfoDTO();
            qyUserInfo.setName("测试用户" + i);

            UserDetailDTO userDetail = new UserDetailDTO();
            userDetail.setGender(genderValue);
            userDetail.setMobile("1380013800" + i);
            userDetail.setEmail("test" + i + "@company.com");
            userDetail.setAvatar("http://avatar" + i + ".url");

            when(oaUserMapper.selectOne(any())).thenReturn(null);
            when(qyWechatOpenClient.getUser(any(String.class))).thenReturn(qyUserInfo);
            when(qyWechatOpenClient.getUserDetail(any(String.class))).thenReturn(userDetail);
            when(oaUserMapper.insert(any(OaUserEntity.class))).thenReturn(1);

            // When: 创建用户
            assertDoesNotThrow(() -> oaUserService.createUserIfNotExists(request));

            // Then: 验证性别值正确解析
            final int finalExpectedGender = expectedGender;
            verify(oaUserMapper, times(1)).insert(argThat((OaUserEntity user) -> {
                assertEquals(finalExpectedGender, user.getGender());
                return true;
            }));
        }
    }

    @Test
    @DisplayName("验证所有默认值设置")
    void createUserIfNotExists_VerifyAllDefaultValues() {
        // Given: 企业微信API返回null
        UserCreateRequest request = new UserCreateRequest();
        request.setQyWechatUserId("default_test_user");

        when(oaUserMapper.selectOne(any())).thenReturn(null);
        when(qyWechatOpenClient.getUser(any(String.class))).thenReturn(null);
        when(oaUserMapper.insert(any(OaUserEntity.class))).thenReturn(1);

        // When: 创建用户
        assertDoesNotThrow(() -> oaUserService.createUserIfNotExists(request));

        // Then: 验证所有字段的默认值
        verify(oaUserMapper, times(1)).insert(argThat((OaUserEntity user) -> {
            // 基本信息
            assertEquals("default_test_user", user.getQyWechatUserId());
            assertEquals("default_test_user", user.getUsername());
            assertEquals("", user.getRealName());
            assertEquals(1, user.getState());
            assertEquals(0, user.getGender());

            // 安全信息
            assertEquals("", user.getPassword());
            assertEquals(0, user.getEnablePasswordLogin());
            assertEquals(0, user.getLoginNumber());

            // 职业信息（核心：没有工号、没有入职、没有部门）
            assertNull(user.getUserCode());
            assertEquals("", user.getLevel());
            assertEquals(0, user.getDataLimitRank());
            assertEquals(0, user.getIsDepartmentManage());
            assertNull(user.getJoinTime());
            assertEquals(0L, user.getLeaveTime());
            assertEquals("", user.getLeaveNotes());

            // 联系信息
            assertEquals("", user.getEmail());
            assertEquals("", user.getPhone());
            assertEquals(0, user.getProvince());
            assertEquals("", user.getAddress());
            assertEquals("", user.getIdentity());

            // 其他信息
            assertEquals("", user.getAvatarUrl());
            assertEquals(999, user.getSort());
            assertEquals("", user.getLastLoginIp());
            assertEquals(0, user.getLastLoginTime());

            return true;
        }));
    }
}

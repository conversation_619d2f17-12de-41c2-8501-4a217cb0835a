package com.miaowen.oa.system;

import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.atomic.AtomicLong;

/**
 * System模块JUnit 5测试基类
 *
 * 使用现有的MySQL数据库配置进行测试
 * 测试完成后自动回滚，避免污染数据库
 *
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@Transactional // 测试后自动回滚，避免污染数据库
@Rollback // 确保测试后回滚
public abstract class BaseSystemTest {

    /**
     * 测试用的企业微信用户ID前缀
     */
    protected static final String TEST_QY_WECHAT_USER_PREFIX = "test_wechat_user_";

    /**
     * 原子计数器，确保生成唯一的测试ID
     */
    private static final AtomicLong COUNTER = new AtomicLong(0);

    /**
     * 生成测试用的企业微信用户ID
     */
    protected String generateTestQyWechatUserId() {
        return TEST_QY_WECHAT_USER_PREFIX + System.currentTimeMillis() + "_" + COUNTER.incrementAndGet();
    }

    /**
     * 生成指定后缀的测试用户ID
     */
    protected String generateTestQyWechatUserId(String suffix) {
        return TEST_QY_WECHAT_USER_PREFIX + suffix + "_" + COUNTER.incrementAndGet();
    }

    /**
     * 生成批量测试用户ID
     */
    protected String[] generateBatchTestQyWechatUserIds(int count) {
        String[] userIds = new String[count];
        for (int i = 0; i < count; i++) {
            userIds[i] = generateTestQyWechatUserId("batch_" + i);
        }
        return userIds;
    }

    /**
     * 验证用户实体的基本字段
     */
    protected void assertBasicUserFields(com.miaowen.oa.system.infrastructure.entity.OaUserEntity user, String expectedQyWechatUserId) {
        assertNotNull(user, "用户实体不应该为空");
        assertEquals(expectedQyWechatUserId, user.getQyWechatUserId(), "企业微信用户ID应该匹配");
        assertEquals(expectedQyWechatUserId, user.getUsername(), "用户名应该等于企业微信用户ID");
        assertEquals(1, user.getState(), "用户状态应该为正常");
        assertNull(user.getUserCode(), "工号应该为空");

        assertNull(user.getJoinTime(), "入职时间应该为空");
        assertEquals(0, user.getEnablePasswordLogin(), "不应该允许密码登录");
    }

    private void assertNotNull(Object obj, String message) {
        if (obj == null) {
            throw new AssertionError(message);
        }
    }

    private void assertEquals(Object expected, Object actual, String message) {
        if (!java.util.Objects.equals(expected, actual)) {
            throw new AssertionError(message + " - Expected: " + expected + ", Actual: " + actual);
        }
    }

    private void assertNull(Object obj, String message) {
        if (obj != null) {
            throw new AssertionError(message + " - Expected null but was: " + obj);
        }
    }
}

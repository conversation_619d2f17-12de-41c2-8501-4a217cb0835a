package com.miaowen.oa.system.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.oa.system.infrastructure.entity.OaDictItemEntity;
import com.miaowen.oa.system.interfaces.req.dict.DictItemBatchSaveRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictItemUpdateRequest;
import com.miaowen.oa.system.interfaces.res.dict.DictItemDetailResp;
import com.miaowen.oa.system.interfaces.res.dict.DictItemResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/15 14:54
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
public interface OaDictItemService extends IService<OaDictItemEntity> {
    void logicDeleteByDictId(Long dictId);

    void batchCreateDictItem(DictItemBatchSaveRequest dictItemBatchSaveRequest);

    void updateDictItem(DictItemUpdateRequest dictItemUpdateRequest);

    void deleteDictItem(Long id);

    List<DictItemResp> getDictItemList(Long dictId);

    DictItemDetailResp getDictItemDetail(Long id);
}

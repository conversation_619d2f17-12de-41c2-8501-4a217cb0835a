package com.miaowen.oa.system.application.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import com.miaowen.oa.system.domain.service.OaUserDomainService;
import com.miaowen.oa.system.infrastructure.entity.*;
import com.miaowen.oa.system.infrastructure.mapper.*;
import com.miaowen.oa.system.interfaces.req.user.*;
import com.miaowen.oa.system.interfaces.res.dept.UserDepartmentResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobResp;
import com.miaowen.oa.system.interfaces.res.project.OaProjectGroupResp;
import com.miaowen.oa.system.interfaces.res.role.OaRoleResp;
import com.miaowen.oa.system.interfaces.res.user.OaLeaderResp;
import com.miaowen.oa.system.interfaces.res.user.OaUserDetailResp;
import com.miaowen.oa.system.interfaces.res.user.OaUserInfoResp;
import com.miaowen.oa.system.interfaces.res.user.OaUserPageResp;
import com.miaowen.oa.framework.qywechat.core.QyWechatOpenClient;
import com.miaowen.oa.framework.qywechat.dto.QyUserInfoDTO;
import com.miaowen.oa.framework.qywechat.dto.UserDetailDTO;
import com.miaowen.oa.framework.qywechat.dto.QyUserInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.miaowen.oa.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.*;

/**
 * 用户服务层
 *
 * 职责说明：
 * 1. 用户的CRUD操作
 * 2. 用户关联数据的批量查询和组装
 * 3. 用户权限相关操作（冻结、解冻、重置密码）
 * 4. 用户详情信息的聚合查询
 *
 * 性能优化策略：
 * - 批量查询：使用IN查询减少数据库访问次数
 * - 关联数据预加载：一次性查询所有关联数据
 * - 分页查询：避免大数据量查询影响性能
 * - 索引优化：基于常用查询字段建立索引
 *
 * 安全措施：
 * - 参数校验：所有输入参数进行空值和有效性校验
 * - 权限控制：基于数据权限等级控制数据访问范围
 * - 异常处理：统一异常处理和日志记录
 * - 数据脱敏：敏感信息在返回前进行脱敏处理
 *
 * SQL优化要点：
 * - 使用批量查询替代循环查询
 * - 合理使用索引提升查询性能
 * - 避免N+1查询问题
 * - 使用合适的JOIN策略
 *
 * <AUTHOR>
 * @Date 2025/7/10 19:50
 * @Company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Repository
public class OaUserService {


    @Resource
    private OaUserDomainService oaUserDomainService;

    @Autowired
    private OaUserMapper oaUserMapper;


    @Resource
    private OaDepartmentMapper oaDepartmentMapper;
    @Resource
    private OaDepartmentUserRelationMapper oaDepartmentUserRelationMapper;
    @Resource
    private OaUserLeaderRelationMapper oaUserLeaderRelationMapper;

    @Resource
    private OaDepartmentLeaderRelationMapper oaDepartmentLeaderRelationMapper;

    @Resource
    private OaProjectGroupMapper oaProjectGroupMapper;
    @Resource
    private OaProjectGroupUserRelationMapper oaProjectGroupUserRelationMapper;

    @Resource
    private OaRoleMapper oaRoleMapper;
    @Resource
    private OaUserRoleRelationMapper oaUserRoleRelationMapper;

    @Resource
    private OaJobMapper oaJobMapper;
    @Resource
    private OaUserJobRelationMapper oaUserJobRelationMapper;

    @Resource
    private QyWechatOpenClient qyWechatOpenClient;
    /**
     * 如果用户不存在则创建用户
     * 创建一个基础用户（没有工号、没有入职、没有部门）
     * 在创建前先查询企业微信获取用户详细信息
     *
     * @param request 用户创建请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void createUserIfNotExists(UserCreateRequest request) {
        // 1. 参数校验
        if (Objects.isNull(request) || !StringUtils.hasText(request.getQyWechatUserId())) {
            throw new IllegalArgumentException("企业微信用户ID不能为空");
        }

        String qyWechatUserId = request.getQyWechatUserId();

        // 2. 检查用户是否已存在
        LambdaQueryWrapper<OaUserEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OaUserEntity::getQyWechatUserId, qyWechatUserId);
        OaUserEntity existingUser = oaUserMapper.selectOne(queryWrapper);

        if (Objects.nonNull(existingUser)) {
            // 用户已存在，检查是否需要更新详细信息
            if (StringUtils.hasText(request.getUserTicket())) {
                log.debug("用户已存在，但有userTicket，尝试更新详细信息: qyWechatUserId={}, userId={}",
                    qyWechatUserId, existingUser.getId());
                updateUserDetailIfNeeded(existingUser, request.getUserTicket());
            } else {
                log.debug("用户已存在，无需创建: qyWechatUserId={}, userId={}", qyWechatUserId, existingUser.getId());
            }
            return;
        }

        // 3. 查询企业微信用户信息
        QyUserInfoDTO qyUserInfo = queryQyWechatUserInfo(qyWechatUserId);
        UserDetailDTO userDetail = queryQyWechatUserDetail(request.getUserTicket());

        // 4. 创建基础用户实体（包含企业微信信息）
        OaUserEntity newUser = createBasicUserEntity(qyWechatUserId, qyUserInfo, userDetail);

        // 5. 保存用户到数据库
        int insertResult = oaUserMapper.insert(newUser);
        if (insertResult <= 0) {
            throw new RuntimeException("创建用户失败");
        }

        // 6. 记录日志
        log.info("成功创建基础用户: qyWechatUserId={}, userId={}, name={}",
            qyWechatUserId, newUser.getId(), newUser.getRealName());
    }


    /**
     * 查询企业微信用户信息（只获取name字段）
     *
     * @param qyWechatUserId 企业微信用户ID
     * @return 企业微信用户信息，查询失败时返回null
     */
    private QyUserInfoDTO queryQyWechatUserInfo(String qyWechatUserId) {
        try {
            return qyWechatOpenClient.getUser(qyWechatUserId);
        } catch (Exception e) {
            log.error("查询企业微信用户基本信息失败: qyWechatUserId={}", qyWechatUserId, e);
            return null;
        }
    }

    /**
     * 查询企业微信用户详细信息（获取mobile、gender、email、avatar字段）
     *
     * @param userTicket 用户票据
     * @return 企业微信用户详细信息，查询失败或userTicket为空时返回null
     */
    private UserDetailDTO queryQyWechatUserDetail(String userTicket) {
        if (!StringUtils.hasText(userTicket)) {
            return null;
        }

        try {
            return qyWechatOpenClient.getUserDetail(userTicket);
        } catch (Exception e) {
            log.error("查询企业微信用户详细信息失败: userTicket={}", userTicket, e);
            return null;
        }
    }

    /**
     * 创建基础用户实体
     * 设置最基本的用户信息，其他字段使用默认值
     * 如果有企业微信用户信息，则填充相关字段
     *
     * @param qyWechatUserId 企业微信用户ID
     * @param qyUserInfo 企业微信用户基本信息（可为null，主要获取name字段）
     * @param userDetail 企业微信用户详细信息（可为null，主要获取mobile、gender、email、avatar字段）
     * @return 用户实体
     */
    private OaUserEntity createBasicUserEntity(String qyWechatUserId, QyUserInfoDTO qyUserInfo, UserDetailDTO userDetail) {
        OaUserEntity user = new OaUserEntity();

        // 基本信息
        user.setQyWechatUserId(qyWechatUserId);

        // 1. 处理基本信息（从qyUserInfo获取name字段）
        if (Objects.nonNull(qyUserInfo) && StringUtils.hasText(qyUserInfo.getName())) {
            // 使用企业微信的姓名
            user.setUsername(qyUserInfo.getName());
            user.setRealName(qyUserInfo.getName());
            log.debug("从企业微信基本信息获取姓名: name={}", qyUserInfo.getName());
        } else {
            // 没有企业微信姓名时使用默认值
            user.setUsername(qyWechatUserId); // 使用企业微信ID作为用户名
            user.setRealName(""); // 真实姓名为空，后续可以更新
            log.debug("未获取到企业微信姓名，使用默认值");
        }

        // 2. 处理详细信息（从userDetail获取mobile、gender、email、avatar字段）
        if (Objects.nonNull(userDetail)) {
            user.setPhone(StringUtils.hasText(userDetail.getMobile()) ? userDetail.getMobile() : ""); // 手机号

            // 性别信息（企业微信：1-男，2-女；系统：1-男，2-女，0-未知）
            if (StringUtils.hasText(userDetail.getGender())) {
                try {
                    int gender = Integer.parseInt(userDetail.getGender());
                    user.setGender(gender); // 直接使用企业微信的性别值
                } catch (NumberFormatException e) {
                    user.setGender(0); // 解析失败时设为未知
                }
            } else {
                user.setGender(0); // 未知性别
            }

            user.setEmail(StringUtils.hasText(userDetail.getEmail()) ? userDetail.getEmail() : ""); // 邮箱
            user.setAvatarUrl(StringUtils.hasText(userDetail.getAvatar()) ? userDetail.getAvatar() : ""); // 头像

            log.debug("从企业微信详细信息获取字段: mobile={}, gender={}, email={}, avatar={}",
                userDetail.getMobile(), userDetail.getGender(), userDetail.getEmail(), userDetail.getAvatar());
        } else {
            // 没有企业微信详细信息时使用默认值
            user.setPhone(""); // 手机号为空
            user.setGender(0); // 0-未知性别
            user.setEmail(""); // 邮箱为空
            user.setAvatarUrl(""); // 头像为空
            log.debug("未获取到企业微信详细信息，使用默认值");
        }

        // 3. 其他固定的默认值
        user.setAddress(""); // 地址为空（不从企业微信获取）

        // 状态信息
        user.setState(1); // 1-正常状态

        // 安全信息
        user.setPassword(""); // 密码为空
        user.setEnablePasswordLogin(0); // 0-不允许密码登录，只能通过企业微信登录
        user.setLoginNumber(0); // 登录次数初始化为0

        // 职业信息（都设置为空或默认值，表示没有工号、没有入职、没有部门）
        user.setUserCode(null); // 工号为空
        user.setLevel(""); // 职级为空
        user.setDataLimitRank(0); // 数据权限：0-自己
        user.setIsDepartmentManage(0); // 0-不是部门管理员
        user.setJoinTime(null); // 入职时间为空
        user.setLeaveTime(0L); // 离职时间为0
        user.setLeaveNotes(""); // 离职备注为空

        // 其他联系信息
        user.setProvince(0); // 省份为0
        user.setIdentity(""); // 身份证号为空

        // 其他信息
        user.setSort(999); // 排序设置为较大值
        user.setLastLoginIp(""); // 最后登录IP为空
        user.setLastLoginTime(0); // 最后登录时间为0

        return user;
    }

    /**
     * 用户分页查询
     *
     * 业务逻辑：
     * 1. 基础查询：根据关键字查询用户基础信息
     * 2. 关联数据查询：批量查询用户的部门、角色、岗位、项目组、领导等关联信息
     * 3. 数据组装：将关联数据组装到用户信息中
     * 4. 条件过滤：根据部门、岗位、项目组、角色等条件进行二次过滤
     * 5. 内存分页：对过滤后的结果进行内存分页处理
     *
     * 性能优化：
     * - 使用批量查询避免N+1问题
     * - 使用Map进行数据关联，时间复杂度O(1)
     * - 关联查询使用IN语句，减少数据库访问次数
     * - 内存分页避免复杂的SQL JOIN查询
     *
     * SQL优化要点：
     * - 基础查询使用索引优化
     * - 关联查询使用批量IN查询
     * - 避免在数据库层面进行复杂的多表JOIN
     *
     * 注意事项：
     * - 当数据量较大时，内存分页可能影响性能，建议优化为数据库分页
     * - 过滤条件较多时，建议在数据库层面进行过滤
     *
     * @param request 分页查询请求参数
     * @return 用户分页结果，包含完整的关联信息
     */
    public PageResult<OaUserPageResp> page(UserPageRequest request) {
        try {
            PageResult<OaUserPageResp> page = new PageResult<>();

            // 第一步：根据条件查询用户基础数据
            List<OaUserEntity> userList = getOaUserList(request);
            if (CollectionUtils.isEmpty(userList)) {
                log.info("用户查询结果为空，查询条件：{}", request);
                return page;
            }

            // 第二步：转换为响应对象
            List<OaUserPageResp> oaUserPageRespList = BeanUtils.toBean(userList, OaUserPageResp.class);

            // 第三步：提取用户ID集合用于批量查询关联数据
            List<Long> userIds = oaUserPageRespList.stream()
                    .filter(Objects::nonNull)
                    .map(OaUserPageResp::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 第四步：批量查询关联数据（性能优化：使用批量查询避免N+1问题）
            Map<Long, List<UserDepartmentResp>> deptMap = getDepartmentMap(userIds);
            Map<Long, List<OaProjectGroupResp>> projectGroupMap = getProjectGroupMap(userIds);
            Map<Long, List<OaRoleResp>> roleMap = getRoleMap(userIds);
            Map<Long, List<OaJobResp>> jobMap = getJobMap(userIds);
            Map<Long, List<OaLeaderResp>> leaderMap = getLeaderMap(userIds);

            // 第五步：组装关联数据到用户信息中
            for (OaUserPageResp resp : oaUserPageRespList) {
                if (Objects.nonNull(resp) && Objects.nonNull(resp.getId())) {
                    resp.setJobs(jobMap.getOrDefault(resp.getId(), new ArrayList<>()));
                    resp.setDepartments(deptMap.getOrDefault(resp.getId(), new ArrayList<>()));
                    resp.setProjectGroups(projectGroupMap.getOrDefault(resp.getId(), new ArrayList<>()));
                    resp.setRoles(roleMap.getOrDefault(resp.getId(), new ArrayList<>()));
                    resp.setLeaders(leaderMap.getOrDefault(resp.getId(), new ArrayList<>()));
                }
            }

            // 第六步：根据条件进行二次过滤（内存过滤）
            List<OaUserPageResp> filteredList = applyFilters(oaUserPageRespList, request);

            // 第七步：内存分页处理
            PageResult<OaUserPageResp> result = applyMemoryPaging(filteredList, request);

            log.info("用户分页查询成功，总记录数：{}，当前页记录数：{}", result.getTotal(), result.getList().size());
            return result;

        } catch (Exception e) {
            log.error("用户分页查询失败", e);
            return new PageResult<>();
        }
    }


    // 批量获取部门数据（其他关联数据类似）
    private Map<Long, List<UserDepartmentResp>> getDepartmentMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<OaUserDepartmentRelationEntity> relations = oaDepartmentUserRelationMapper.findByUserIds(userIds);

        // 提取部门ID
        Set<Long> deptIds = relations.stream().map(OaUserDepartmentRelationEntity::getDepartmentId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }
        // 批量查询部门详情
        List<OaDepartmentEntity> deptList = oaDepartmentMapper.selectByIds(deptIds);
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyMap();
        }
        Map<Long, OaDepartmentEntity> deptDetailMap = deptList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OaDepartmentEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<UserDepartmentResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            if (Objects.nonNull(relation) && Objects.nonNull(relation.getDepartmentId()) && Objects.nonNull(relation.getUserId())) {
                OaDepartmentEntity dept = deptDetailMap.get(relation.getDepartmentId());
                if (dept != null) {
                    UserDepartmentResp resp = new UserDepartmentResp();
                    resp.setDeptId(dept.getId());
                    resp.setName(dept.getDepartmentName());
                    resp.setCode(dept.getDepartmentCode());
                    resultMap.computeIfAbsent(relation.getUserId(), k -> new ArrayList<>()).add(resp);
                }
            }
        });

        return resultMap;
    }


    // 批量获取项目组数据（其他关联数据类似）
    private Map<Long, List<OaProjectGroupResp>> getProjectGroupMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<OaProjectGroupUserRelationEntity> relations = oaProjectGroupUserRelationMapper.findByUserIds(userIds);

        // 提取项目组ID
        Set<Long> projectGroupIds = relations.stream().map(OaProjectGroupUserRelationEntity::getProjectGroupId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(projectGroupIds)) {
            return Collections.emptyMap();
        }
        // 批量查询项目组详情
        List<OaProjectGroupEntity> projectGroupList = oaProjectGroupMapper.selectByIds(projectGroupIds);
        if (CollectionUtils.isEmpty(projectGroupList)) {
            return Collections.emptyMap();
        }
        Map<Long, OaProjectGroupEntity> projectGroupDetailMap = projectGroupList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OaProjectGroupEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<OaProjectGroupResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            if (Objects.nonNull(relation) && Objects.nonNull(relation.getProjectGroupId()) && Objects.nonNull(relation.getUserId())) {
                OaProjectGroupEntity projectGroupEntity = projectGroupDetailMap.get(relation.getProjectGroupId());
                if (projectGroupEntity != null) {
                    OaProjectGroupResp resp = new OaProjectGroupResp();
                    resp.setName(projectGroupEntity.getGroupName());
                    resp.setProjectGroupId(projectGroupEntity.getId());
                    resultMap.computeIfAbsent(relation.getUserId(), k -> new ArrayList<>()).add(resp);
                }
            }
        });

        return resultMap;
    }

    // 批量获取权限数据（其他关联数据类似）
    private Map<Long, List<OaRoleResp>> getRoleMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<OaUserRoleRelationEntity> relations = oaUserRoleRelationMapper.findByUserIds(userIds);

        // 提取权限ID
        Set<Long> roleIds = relations.stream().map(OaUserRoleRelationEntity::getRoleId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyMap();
        }
        // 批量查询角色详情
        List<OaRoleEntity> roleList = oaRoleMapper.selectByIds(roleIds);
        if (CollectionUtils.isEmpty(roleList)) {
            return Collections.emptyMap();
        }
        Map<Long, OaRoleEntity> roleDetailMap = roleList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OaRoleEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<OaRoleResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            if (Objects.nonNull(relation) && Objects.nonNull(relation.getRoleId()) && Objects.nonNull(relation.getUserId())) {
                OaRoleEntity roleEntity = roleDetailMap.get(relation.getRoleId());
                if (roleEntity != null) {
                    OaRoleResp resp = new OaRoleResp();
                    resp.setRoleName(roleEntity.getRoleName());
                    resp.setRoleId(roleEntity.getId());
                    resultMap.computeIfAbsent(relation.getUserId(), k -> new ArrayList<>()).add(resp);
                }
            }
        });

        return resultMap;
    }


    // 批量获取岗位数据（其他关联数据类似）
    private Map<Long, List<OaJobResp>> getJobMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        //通过userIds查询出 用户岗位关联数据
        List<OaUserJobRelationEntity> relations = oaUserJobRelationMapper.findByUserIds(userIds);

        // 提取岗位ID
        Set<Long> jobIds = relations.stream().map(OaUserJobRelationEntity::getJobId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(jobIds)) {
            return Collections.emptyMap();
        }
        // 批量查询岗位详情
        List<OaJobEntity> jobList = oaJobMapper.selectByIds(jobIds);
        if (CollectionUtils.isEmpty(jobList)) {
            return Collections.emptyMap();
        }

        // 将岗位列表转换为Map，便于快速查找
        Map<Long, OaJobEntity> jobDetailMap = jobList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OaJobEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<OaJobResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            if (Objects.nonNull(relation) && Objects.nonNull(relation.getJobId()) && Objects.nonNull(relation.getUserId())) {
                OaJobEntity jobEntity = jobDetailMap.get(relation.getJobId());
                if (jobEntity != null) {
                    OaJobResp resp = new OaJobResp();
                    resp.setName(jobEntity.getJobName());
                    resp.setJobId(jobEntity.getId());
                    resultMap.computeIfAbsent(relation.getUserId(), k -> new ArrayList<>()).add(resp);
                }
            }
        });

        return resultMap;
    }

    // 批量获取上级领导数据（其他关联数据类似）
    private Map<Long, List<OaLeaderResp>> getLeaderMap(List<Long> userIds) {
        // 1. 查询用户与领导的关系
        LambdaQueryWrapper<OaUserLeaderRelationEntity> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.in(OaUserLeaderRelationEntity::getUserId, userIds)
                .eq(OaUserLeaderRelationEntity::getDeleteTime, 0);
        List<OaUserLeaderRelationEntity> relations = oaUserLeaderRelationMapper.selectList(relationWrapper);

        if (CollectionUtils.isEmpty(relations)) {
            return userIds.stream()
                    .collect(Collectors.toMap(Function.identity(), id -> Collections.emptyList()));
        }

        // 2. 提取所有领导ID并去重
        Set<Long> leaderIds = relations.stream()
                .map(OaUserLeaderRelationEntity::getLeaderId)
                .collect(Collectors.toSet());


        // 3. 查询领导用户信息
        LambdaQueryWrapper<OaUserEntity> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(OaUserEntity::getId, leaderIds)
                .eq(OaUserEntity::getDeleteTime, 0)
                .eq(OaUserEntity::getLeaveTime, 0);
        List<OaUserEntity> leaders = oaUserMapper.selectList(userWrapper);

        // 4. 构建领导ID到用户实体的映射
        Map<Long, OaUserEntity> leaderMap = leaders.stream()
                .collect(Collectors.toMap(OaUserEntity::getId, Function.identity()));

        // 5. 按用户ID分组领导关系
        Map<Long, List<Long>> userToLeaderIds = relations.stream()
                .collect(Collectors.groupingBy(
                        OaUserLeaderRelationEntity::getUserId,
                        Collectors.mapping(OaUserLeaderRelationEntity::getLeaderId, Collectors.toList())
                ));


        // 6. 构建最终结果映射
        Map<Long, List<OaLeaderResp>> resultMap = new HashMap<>();

        for (Long userId : userIds) {
            List<OaLeaderResp> leaderResps = new ArrayList<>();

            // 获取该用户的所有领导ID
            List<Long> userLeaderIds = userToLeaderIds.get(userId);
            if (userLeaderIds != null) {
                for (Long leaderId : userLeaderIds) {
                    OaUserEntity leader = leaderMap.get(leaderId);
                    if (leader != null) {
                        OaLeaderResp resp = new OaLeaderResp();
                        resp.setUserId(leaderId);
                        resp.setName(leader.getRealName() != null ?
                                leader.getRealName() : leader.getUsername());
                        leaderResps.add(resp);
                    }
                }
            }

            resultMap.put(userId, leaderResps);
        }
        return resultMap;
    }


    /**
     * @param request 请求参数
     * @return 根据条件筛选后的数据
     */
    private List<OaUserEntity> getOaUserList(UserPageRequest request) {
        LambdaQueryWrapper<OaUserEntity> eqUser = Wrappers.lambdaQuery(OaUserEntity.class).eq(StringUtils.hasText(request.getGender()), OaUserEntity::getGender, request.getGender()).eq(StringUtils.hasText(request.getState()), OaUserEntity::getState, request.getState()).between(StringUtils.hasText(request.getStartTime()) && StringUtils.hasText(request.getEndTime()), OaUserEntity::getCreateTime, request.getStartTime(), request.getEndTime()).eq(OaUserEntity::getDeleteTime, 0).eq(OaUserEntity::getLeaveTime, 0);

        // 合并三个 LIKE 条件为 OR 关系
        if (StringUtils.hasText(request.getPrimaryKey())) {
            String keyword = request.getPrimaryKey();
            eqUser.and(wrapper -> wrapper.like(OaUserEntity::getUsername, keyword).or().like(OaUserEntity::getUserCode, keyword).or().like(OaUserEntity::getPhone, keyword));
        }

        return oaUserMapper.selectList(eqUser);
    }


    /**
     * 编辑接口
     *
     * @param request 请求参数
     * @return
     */
    public void edit(UserEditRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw exception(USER_UPDATE_NOT_EXISTS);
        }

        //user领域调用
//        oaUserDomainService.editUser(BeanUtils.toBean(request, UserBO.class));

        OaUserEntity entity = oaUserMapper.selectById(request.getId());
        if (entity == null) {
            throw exception(USER_UPDATE_NOT_EXISTS);
        }
        entity = BeanUtils.toBean(request, OaUserEntity.class);
        oaUserMapper.updateById(entity);

        //先删除后增
        if (!CollectionUtils.isEmpty(request.getDeptIds())){
            List<OaUserDepartmentRelationEntity> relationEntities = new ArrayList<>();
            oaDepartmentUserRelationMapper.logicDelete(Wrappers.lambdaQuery(OaUserDepartmentRelationEntity.class).eq(OaUserDepartmentRelationEntity::getUserId, request.getId()));
            for (Long deptId : request.getDeptIds()) {
                OaUserDepartmentRelationEntity relation = new OaUserDepartmentRelationEntity();
                relation.setUserId(request.getId());
                relation.setDepartmentId(deptId);
                relationEntities.add(relation);
            }
            oaDepartmentUserRelationMapper.insertOrUpdate(relationEntities);
        }

        //先删除后增
        if (!CollectionUtils.isEmpty(request.getJobIds())){
            List<OaUserJobRelationEntity> relationEntities = new ArrayList<>();
            oaUserJobRelationMapper.logicDelete(Wrappers.lambdaQuery(OaUserJobRelationEntity.class).eq(OaUserJobRelationEntity::getUserId, request.getId()));
            for (Long jobId : request.getJobIds()) {
                OaUserJobRelationEntity relation = new OaUserJobRelationEntity();
                relation.setUserId(request.getId());
                relation.setJobId(jobId);
                relationEntities.add(relation);
            }
            oaUserJobRelationMapper.insertOrUpdate(relationEntities);
        }

        //先删除后增
        if (!CollectionUtils.isEmpty(request.getParentIds())){
            List<OaUserLeaderRelationEntity> relationEntities = new ArrayList<>();
            oaUserLeaderRelationMapper.logicDelete(Wrappers.lambdaQuery(OaUserLeaderRelationEntity.class)
                    .eq(OaUserLeaderRelationEntity::getUserId, request.getId()));
            for (Long parentId : request.getParentIds()) {
                OaUserLeaderRelationEntity relation = new OaUserLeaderRelationEntity();
                relation.setUserId(request.getId());
                relation.setLeaderId(parentId);
                relationEntities.add(relation);
            }
            oaUserLeaderRelationMapper.insertOrUpdate(relationEntities);
        }
    }

    public OaUserDetailResp detail(Long id) {
        if (Objects.isNull(id)) {
            throw exception(USER_NOT_EXISTS);
        }
        LambdaQueryWrapper<OaUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaUserEntity::getId, id)
                .eq(OaUserEntity::getDeleteTime, 0);
        OaUserEntity user = oaUserMapper.selectOne(wrapper);
        if (Objects.isNull(user)) {
            throw exception(USER_NOT_EXISTS);
        }

        OaUserDetailResp userDetailResp = BeanUtils.toBean(user, OaUserDetailResp.class);
        if (Objects.isNull(userDetailResp)) {
            throw exception(USER_NOT_EXISTS);
        }
        List<OaDepartmentLeaderRelationEntity> oaDepartmentLeaderRelationEntities = oaDepartmentLeaderRelationMapper.selectList(Wrappers.lambdaQuery(OaDepartmentLeaderRelationEntity.class).eq(OaDepartmentLeaderRelationEntity::getUserId, id).eq(OaDepartmentLeaderRelationEntity::getDeleteTime, 0));
        List<Long> deptIdList = oaDepartmentLeaderRelationEntities.stream().map(OaDepartmentLeaderRelationEntity::getDeptId).toList();
        if (!deptIdList.isEmpty()){
            List<OaDepartmentEntity> oaDepartmentEntities = oaDepartmentMapper.selectList(Wrappers.lambdaQuery(OaDepartmentEntity.class).eq(OaDepartmentEntity::getDeleteTime, 0).in(OaDepartmentEntity::getId, deptIdList));
            List<UserDepartmentResp> departmentRespList = oaDepartmentEntities.stream().map(oaDepartmentEntity -> {
                UserDepartmentResp userDepartmentResp = new UserDepartmentResp();
                userDepartmentResp.setDeptId(oaDepartmentEntity.getId());
                userDepartmentResp.setName(oaDepartmentEntity.getDepartmentName());
                userDepartmentResp.setCode(oaDepartmentEntity.getDepartmentCode());
                return userDepartmentResp;
            }).toList();
            userDetailResp.setManagedDepartments(departmentRespList);
        }


        Map<Long, List<UserDepartmentResp>> deptMap = getDepartmentMap(Collections.singletonList(user.getId()));
        Map<Long, List<OaProjectGroupResp>> projectGroupMap = getProjectGroupMap(Collections.singletonList(user.getId()));
        Map<Long, List<OaRoleResp>> roleMap = getRoleMap(Collections.singletonList(user.getId()));
        Map<Long, List<OaJobResp>> jobMap = getJobMap(Collections.singletonList(user.getId()));
        Map<Long, List<OaLeaderResp>> leaderMap = getLeaderMap(Collections.singletonList(user.getId()));

        userDetailResp.setDepartments(deptMap.getOrDefault(userDetailResp.getId(), new ArrayList<>()));
        userDetailResp.setJobs(jobMap.getOrDefault(userDetailResp.getId(), new ArrayList<>()));
        userDetailResp.setProjectGroups(projectGroupMap.getOrDefault(userDetailResp.getId(), new ArrayList<>()));
        userDetailResp.setRoles(roleMap.getOrDefault(userDetailResp.getId(), new ArrayList<>()));
        userDetailResp.setLeaders(leaderMap.getOrDefault(userDetailResp.getId(), new ArrayList<>()));
        return userDetailResp;
    }

    public void reset(UserResetRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw exception(USER_NOT_EXISTS);
        }
        oaUserDomainService.reset(Collections.singletonList(request.getId()));
    }

    /**
     * 冻结
     *
     * @param request 请求体
     */
    public void lock(UserLockRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getUserId())) {
            throw exception(USER_NOT_EXISTS);
        }
        oaUserDomainService.lock(request.getUserId());
    }

    /**
     * 解冻
     *
     * @param request 请求体
     */
    public void unlock(UserUnLockRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getUserId())) {
            throw exception(USER_NOT_EXISTS);
        }
        oaUserDomainService.unlock(request.getUserId());
    }

    public OaUserInfoResp info() {
        OaUserDetailResp detail = detail(1L);
        if (Objects.isNull(detail)) {
            throw exception(USER_NOT_EXISTS);
        }
        OaUserInfoResp resp = BeanUtils.toBean(detail, OaUserInfoResp.class);
        if (Objects.isNull(resp)) {
            throw exception(USER_NOT_EXISTS);
        }
        return resp;
    }

    public List<OaUserDetailResp> getUsers(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OaUserDepartmentRelationEntity> wrapper = Wrappers.lambdaQuery(OaUserDepartmentRelationEntity.class);
        wrapper.in(OaUserDepartmentRelationEntity::getDepartmentId, deptIds);
        wrapper.eq(OaUserDepartmentRelationEntity::getDeleteTime, 0);
        List<OaUserDepartmentRelationEntity> oaUserDepartmentRelationEntities = oaDepartmentUserRelationMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(oaUserDepartmentRelationEntities)) {
            return null;
        }
        List<Long> userIds = oaUserDepartmentRelationEntities.stream().map(OaUserDepartmentRelationEntity::getUserId).toList();
        List<OaUserEntity> oaUserEntities = oaUserMapper.selectBatchIds(userIds);
        oaUserEntities = oaUserEntities.stream().filter(t -> Objects.equals(t.getDeleteTime(), 0L) && Objects.equals(t.getLeaveTime(), 0L)).toList();

        return BeanUtils.toBean(oaUserEntities, OaUserDetailResp.class);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 应用过滤条件
     *
     * 业务逻辑：
     * - 根据部门ID列表过滤用户
     * - 根据岗位ID列表过滤用户
     * - 根据项目组ID列表过滤用户
     * - 根据角色ID列表过滤用户
     *
     * 性能优化：
     * - 使用Stream API进行内存过滤
     * - 短路求值提升性能
     *
     * @param userList 用户列表
     * @param request 查询请求
     * @return 过滤后的用户列表
     */
    private List<OaUserPageResp> applyFilters(List<OaUserPageResp> userList, UserPageRequest request) {
        List<OaUserPageResp> filteredList = userList;

        // 按部门过滤
        if (!CollectionUtils.isEmpty(request.getDepartmentIds())) {
            filteredList = filteredList.stream()
                    .filter(resp -> Objects.nonNull(resp.getDepartments()) &&
                            resp.getDepartments().stream()
                                    .anyMatch(dept -> Objects.nonNull(dept.getDeptId()) &&
                                            request.getDepartmentIds().contains(dept.getDeptId())))
                    .collect(Collectors.toList());
        }

        // 按岗位过滤
        if (!CollectionUtils.isEmpty(request.getJobIds())) {
            filteredList = filteredList.stream()
                    .filter(resp -> Objects.nonNull(resp.getJobs()) &&
                            resp.getJobs().stream()
                                    .anyMatch(job -> Objects.nonNull(job.getJobId()) &&
                                            request.getJobIds().contains(job.getJobId())))
                    .collect(Collectors.toList());
        }

        // 按项目组过滤
        if (!CollectionUtils.isEmpty(request.getProjectGroupIds())) {
            filteredList = filteredList.stream()
                    .filter(resp -> Objects.nonNull(resp.getProjectGroups()) &&
                            resp.getProjectGroups().stream()
                                    .anyMatch(pg -> Objects.nonNull(pg.getProjectGroupId()) &&
                                            request.getProjectGroupIds().contains(pg.getProjectGroupId())))
                    .collect(Collectors.toList());
        }

        // 按角色过滤
        if (!CollectionUtils.isEmpty(request.getRoleIds())) {
            filteredList = filteredList.stream()
                    .filter(resp -> Objects.nonNull(resp.getRoles()) &&
                            resp.getRoles().stream()
                                    .anyMatch(role -> Objects.nonNull(role.getRoleId()) &&
                                            request.getRoleIds().contains(role.getRoleId())))
                    .collect(Collectors.toList());
        }

        // 添加默认排序：按 userCode 正序
        filteredList = filteredList.stream()
                .sorted(Comparator.comparing(OaUserPageResp::getUserCode,
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());


        return filteredList;
    }

    /**
     * 内存分页处理
     *
     * 业务逻辑：
     * - 计算分页参数
     * - 对结果进行分页切割
     * - 返回分页结果
     *
     * 性能优化：
     * - 使用subList进行内存分页，避免数据复制
     * - 边界检查防止数组越界
     *
     * @param dataList 数据列表
     * @param request 分页请求
     * @return 分页结果
     */
    private PageResult<OaUserPageResp> applyMemoryPaging(List<OaUserPageResp> dataList, UserPageRequest request) {
        int totalRecords = dataList.size();
        // 防止pageSize为0
        int pageSize = Math.max(request.getPageSize(), 1);
        // 防止page为0
        int currentPage = Math.max(request.getPage(), 1);

        // 计算分页参数
        int startIndex = (currentPage - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalRecords);

        // 边界检查
        if (startIndex >= totalRecords) {
            return new PageResult<>(new ArrayList<>(), (long) totalRecords);
        }

        // 分页切割
        List<OaUserPageResp> pagedList = dataList.subList(startIndex, endIndex);

        return new PageResult<>(pagedList, (long) totalRecords);
    }

    /**
     * 更新用户详细信息（如果有userTicket）
     * 每次有userTicket都更新mobile、gender、email、avatar四个字段
     *
     * @param existingUser 已存在的用户
     * @param userTicket 用户票据
     */
    private void updateUserDetailIfNeeded(OaUserEntity existingUser, String userTicket) {
        try {
            // 查询企业微信用户详细信息
            UserDetailDTO userDetail = queryQyWechatUserDetail(userTicket);

            if (Objects.isNull(userDetail)) {
                log.debug("未获取到企业微信用户详细信息，跳过更新: userId={}", existingUser.getId());
                return;
            }

            // 记录更新前的值（用于日志）
            String oldMobile = existingUser.getPhone();
            Integer oldGender = existingUser.getGender();
            String oldEmail = existingUser.getEmail();
            String oldAvatar = existingUser.getAvatarUrl();

            boolean needUpdate = false;

            // 更新手机号
            String newMobile = StringUtils.hasText(userDetail.getMobile()) ? userDetail.getMobile() : "";
            if (!Objects.equals(oldMobile, newMobile)) {
                existingUser.setPhone(newMobile);
                needUpdate = true;
            }

            // 更新性别
            Integer newGender = 0; // 默认未知
            if (StringUtils.hasText(userDetail.getGender())) {
                try {
                    newGender = Integer.parseInt(userDetail.getGender());
                } catch (NumberFormatException e) {
                    log.warn("解析性别失败，使用默认值: gender={}", userDetail.getGender());
                }
            }
            if (!Objects.equals(oldGender, newGender)) {
                existingUser.setGender(newGender);
                needUpdate = true;
            }

            // 更新邮箱
            String newEmail = StringUtils.hasText(userDetail.getEmail()) ? userDetail.getEmail() : "";
            if (!Objects.equals(oldEmail, newEmail)) {
                existingUser.setEmail(newEmail);
                needUpdate = true;
            }

            // 更新头像
            String newAvatar = StringUtils.hasText(userDetail.getAvatar()) ? userDetail.getAvatar() : "";
            if (!Objects.equals(oldAvatar, newAvatar)) {
                existingUser.setAvatarUrl(newAvatar);
                needUpdate = true;
            }

            // 如果有字段需要更新，则执行数据库更新
            if (needUpdate) {
                int updateResult = oaUserMapper.updateById(existingUser);
                if (updateResult > 0) {
                    log.info("成功更新用户详细信息: userId={}, mobile={}→{}, gender={}→{}, email={}→{}, avatar={}→{}",
                        existingUser.getId(), oldMobile, newMobile, oldGender, newGender,
                        oldEmail, newEmail, oldAvatar, newAvatar);
                } else {
                    log.error("更新用户详细信息失败: userId={}", existingUser.getId());
                }
            } else {
                log.debug("用户详细信息无变化，跳过更新: userId={}", existingUser.getId());
            }

        } catch (Exception e) {
            log.error("更新用户详细信息异常: userId={}, userTicket={}", existingUser.getId(), userTicket, e);
            // 更新失败不影响主流程
        }
    }
}

package com.miaowen.oa.system.server.interfaces.req.user;


import com.miaowen.oa.framework.common.pojo.PageParam;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */

@Data
@Accessors(chain = true)
public class RolePageRequest extends PageParam {

    /**
     * 角色id
     */
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    /**
     * 部门ids
     */
    List<Long> departmentIds;

    /**
     * 岗位ids
     */
    private List<Long> jobIds;

    /**
     * 所属项目组ids
     */
    private List<Long> projectGroupIds;


    /**
     * 关键字
     */
    private String primaryKey;


}

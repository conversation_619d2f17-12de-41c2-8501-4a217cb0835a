package com.miaowen.oa.system.interfaces.req.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@Schema(description = "添加角色人员请求")
public class RoleAddUserRequest {


    @Schema(description = "用户ID列表 (0表示根节点)", example = "[0]")
    private List<Long> userIds;


    @NotNull(message = "角色id不能为空")
    @Schema(description = "角色id", example = "1")
    private Long roleId;

}
package com.miaowen.oa.system.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.infrastructure.entity.OaProjectEntity;
import com.miaowen.oa.system.interfaces.req.project.ProjectPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.project.ProjectSaveRequest;
import com.miaowen.oa.system.interfaces.req.project.ProjectUpdateRequest;
import com.miaowen.oa.system.interfaces.res.project.OaProjectDetailResp;
import com.miaowen.oa.system.interfaces.res.project.OaProjectPageResp;

/**
 * <AUTHOR>
 * @Date 2025/7/18 14:18
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
public interface OaProjectService extends IService<OaProjectEntity> {
    void addProject(ProjectSaveRequest projectSaveRequest);

    void updateProject(ProjectUpdateRequest projectUpdateRequest);

    PageResult<OaProjectPageResp> getProjectPageList(ProjectPageQueryRequest projectPageQueryRequest);

    OaProjectDetailResp getProjectById(Long id);

    void deleteJob(Long id);
}

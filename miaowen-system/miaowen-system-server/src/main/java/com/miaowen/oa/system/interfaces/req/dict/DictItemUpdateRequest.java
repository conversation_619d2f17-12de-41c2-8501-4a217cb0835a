package com.miaowen.oa.system.interfaces.req.dict;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/15 18:18
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class DictItemUpdateRequest {
    private Long id;

    /**
     * 字典项名称
     */
    private String label;

    /**
     * 字典项值
     */
    private String itemValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 0-正常 1-禁用
     */
    private Integer state;


}

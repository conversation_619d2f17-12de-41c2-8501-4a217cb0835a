package com.miaowen.oa.system.infrastructure.mapper;

import com.miaowen.oa.framework.mybatis.core.mapper.CustomBaseMapper;
import com.miaowen.oa.system.infrastructure.entity.OaDepartmentEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:22
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Mapper
public interface OaDepartmentMapper extends CustomBaseMapper<OaDepartmentEntity> {
    List<OaDepartmentEntity> findByUserIds(List<Long> userIds);
}

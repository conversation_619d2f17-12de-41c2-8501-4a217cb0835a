package com.miaowen.oa.system.interfaces.req.dict;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/15 18:17
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class DictItemBatchSaveRequest {

    @NotNull
    private Long dictId;

    @NotNull
    private String dictCode;

    @NotNull(message = "字典值不能为空")
    @Size(min = 1,message = "字典值不能为空")
    private List<DictItemSaveRequest> dictItemList;

}

package com.miaowen.oa.system.interfaces.res.dept;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.oa.system.infrastructure.entity.OaDepartmentEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:41
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaDepartmentResp {
    private Long id;

    //子部门信息
    private List<OaDepartmentResp> children;
    // 部门名称
    private String departmentName;
    // 上级部门ID
    private Long parentId;
    // 上级部门名字
    private String parentName;
    //排序
    private Integer sort;
    //描述
    private String description;
    //部门编码
    private String departmentCode;
    //更新时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    //更新人
    private Long updater;
    //更新人姓名
    private String updaterName;
    //当前部门下的人员数量
    private Integer userCount;

    private List<DepartmentLeaderUserResp> departmentLeaderUserRespList;

}

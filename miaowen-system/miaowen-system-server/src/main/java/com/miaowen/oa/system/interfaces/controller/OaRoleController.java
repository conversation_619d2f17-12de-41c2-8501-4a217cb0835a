package com.miaowen.oa.system.interfaces.controller;


import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.application.repository.OaRoleService;
import com.miaowen.oa.system.interfaces.req.role.*;
import com.miaowen.oa.system.interfaces.res.role.OaRoleGroupListResp;
import com.miaowen.oa.system.interfaces.res.role.OaRoleResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2025/7/10 19:57
 * @Company 武汉市妙闻网络科技有限公司
 * @Description /api/$version(/in)?/$service
 */
@Tag(name = "角色管理")
@Slf4j
@RequestMapping("/role")
@RestController
public class OaRoleController {

    @Resource
    private OaRoleService oaRoleService;


    @Operation(summary = "查询用户列表")
    @GetMapping("/userList")
    public CommonResult<?> page(@ModelAttribute com.miaowen.oa.system.server.interfaces.req.user.RolePageRequest request) {
        return CommonResult.success(oaRoleService.page(request));
    }

    @Operation(summary = "添加人员到角色")
    @PostMapping("/addUser")
    public CommonResult<Long> addUser(@Validated @RequestBody RoleAddUserRequest request) {
        oaRoleService.addUser(request);
        return CommonResult.success();
    }


    @Operation(summary = "授权")
    @PostMapping("/authority")
    public CommonResult<Long> authority(@Validated @RequestBody RoleAuthorityRequest request) {
        oaRoleService.authority(request);
        return CommonResult.success();
    }

    @Operation(summary = "添加部门到角色")
    @PostMapping("/addDept")
    public CommonResult<Long> addDept(@Validated @RequestBody RoleAddDepartmentRequest request) {
       oaRoleService.addDept(request);
        return CommonResult.success();
    }

    @Operation(summary = "创建角色")
    @PostMapping("")
    public CommonResult<Long> createRole(@Validated @RequestBody RoleCreateRequest request) {
        Long roleId = oaRoleService.createRole(request);
        return CommonResult.success(roleId);
    }


    @Operation(summary = "更新角色")
    @PutMapping("/{id}")
    public CommonResult<Void> updateRole(
            @PathVariable("id") Long id,
            @Validated @RequestBody RoleUpdateRequest request) {
        // 确保路径ID和请求体ID一致
        request.setId(id);
        oaRoleService.updateRole(request);
        return CommonResult.success();
    }


    @Operation(summary = "删除角色")
    @PermitAll
    @DeleteMapping("/{id}")
    public CommonResult<Void> deleteRole(@PathVariable("id") Long id) {
        oaRoleService.deleteRole(id);
        return CommonResult.success();
    }


    @Operation(summary = "移除用户")
    @PermitAll
    @DeleteMapping("/removeUser")
    public CommonResult<Void> removeUser(@Validated @RequestBody RoleBatchRemoveRequest request) {
        oaRoleService.removeUser(request);
        return CommonResult.success();
    }



    @Operation(summary = "查询角色详情")
    @PermitAll
    @GetMapping("/{id}")
    public CommonResult<OaRoleResp> getRoleDetail(@PathVariable("id") Long id) {
        OaRoleResp role = oaRoleService.getRoleDetail(id);
        return CommonResult.success(role);
    }



    @Operation(summary = "通过权限分组id查询权限列表")
    @PermitAll
    @GetMapping("/list")
    public CommonResult<?> getRoles(@RequestParam(name = "roleGroupId", required = false)Long roleGroupId) {
        List<OaRoleResp> list = oaRoleService.getRoles(roleGroupId);
        return CommonResult.success(list);
    }


}

package com.miaowen.oa.system.domain.dto;

import lombok.Data;

/**
 * 用户联系信息值对象
 * <AUTHOR>
 */
@Data
public class UserContact {
    private  String email;
    private  String companyEmail;
    private  String phone;
    private  Integer province;
    private  String address;
    /**
     * 身份证号
     */
    private  String identity;

    public UserContact(String email, String companyEmail, String phone, Integer province,
                      String address, String identity) {
        this.email = email;
        this.companyEmail = companyEmail;
        this.phone = validateMobile(phone);
        this.province = province;
        this.address = address;
        this.identity = validateIdentity(identity);
    }
    
    private String validateMobile(String mobile) {
        // 实际项目中应有手机号格式验证
        return mobile;
    }
    
    private String validateIdentity(String identity) {
        // 实际项目中应有身份证格式验证
        return identity;
    }
    
    /**
     * 更新联系信息
     */
    public void update(UserContact newContact) {
        // 实际实现中会进行字段级更新
        this.email = newContact.getEmail();
        this.phone = newContact.getPhone();
        this.province = newContact.getProvince();
        this.identity = newContact.getIdentity();
    }
}
package com.miaowen.oa.system.infrastructure.convertor;

import com.miaowen.oa.system.api.user.dto.UserInfoDTO;
import com.miaowen.oa.system.domain.model.OaUser;
import com.miaowen.oa.system.infrastructure.entity.OaUserEntity;

import java.util.Objects;

/**
 * UserConvertor :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-17
 */
public class UserConvertor {
    public static UserInfoDTO toDTO(OaUser user) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(user.getUserId().getValue());
        userInfoDTO.setUsername(user.getBasicInfo().getUsername());
        userInfoDTO.setPhone(user.getContact().getPhone());
        userInfoDTO.setJoinTime(user.getCareer().getJoinTime());
        userInfoDTO.setLeaveTime(user.getCareer().getLeaveTime());
        userInfoDTO.setPassword(user.getSecurity().getPassword().getPassword());
        userInfoDTO.setUserState(user.getState().getCode());
        userInfoDTO.setQyWechatUserId(user.getSecurity().getQyWechatUserId());
        userInfoDTO.setEnablePasswordLogin(user.getSecurity().getEnablePasswordLogin());
        return userInfoDTO;
    }

    public static UserInfoDTO entityToDTO(OaUserEntity user) {
        if (Objects.isNull(user)){
            return null;
        }
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(user.getId());
        userInfoDTO.setUsername(user.getUsername());
        userInfoDTO.setPhone(user.getPhone());
        userInfoDTO.setJoinTime(user.getJoinTime());
        userInfoDTO.setLeaveTime(user.getLeaveTime().intValue());
        userInfoDTO.setPassword(user.getPassword());
        userInfoDTO.setUserState(user.getState());
        userInfoDTO.setQyWechatUserId(user.getQyWechatUserId());
        userInfoDTO.setEnablePasswordLogin(user.getEnablePasswordLogin());
        return userInfoDTO;
    }
}

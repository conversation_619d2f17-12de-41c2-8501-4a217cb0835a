package com.miaowen.oa.system.interfaces.res.role;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/14:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaRoleResp {

    private Long id;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色分组id
     */
    private Long roleGroupId;


    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 是否是超级管理员：0-否，1-是
     */
    private Integer isSuperAdmin = 0;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 添加时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     *  创建人id
     */
    private Long creator;

    /**
     * 创建人名称
     */
    private String createName;

}

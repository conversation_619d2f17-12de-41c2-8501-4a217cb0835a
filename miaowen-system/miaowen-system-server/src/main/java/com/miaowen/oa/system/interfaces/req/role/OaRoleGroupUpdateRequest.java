package com.miaowen.oa.system.interfaces.req.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@Schema(description = "更新角色分组请求")
public class OaRoleGroupUpdateRequest {

    @NotNull(message = "角色分组ID不能为空")
    @Schema(description = "角色分组ID", example = "1", required = true)
    private Long id;

    @NotBlank(message = "分组名称不能为空")
    @Size(max = 50, message = "分组名称长度不能超过50个字符")
    @Schema(description = "分组名称", example = "系统角色组", required = true)
    private String groupName;

    @Size(max = 255, message = "描述长度不能超过255个字符")
    @Schema(description = "描述", example = "系统内置角色组")
    private String description;

    @Schema(description = "排序", example = "1")
    private Integer sort;

}

package com.miaowen.oa.system.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 用户银行信息值对象
 * 
 * 严格按照OaUserEntity中的实际字段设计，不添加不存在的字段
 * 
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode
public class UserBankInfo {

    // ========== 银行账户信息（对应OaUserEntity实际字段） ==========
    
    /**
     * 开户行
     * 对应OaUserEntity.bankName
     */
    private final String bankName;
    
    /**
     * 银行卡号
     * 对应OaUserEntity.bankCardNumber
     */
    private final String bankCardNumber;

    // ========== 构造函数 ==========
    
    /**
     * 完整构造函数（严格按照OaUserEntity字段）
     */
    public UserBankInfo(String bankName, String bankCardNumber) {
        this.bankName = bankName;
        this.bankCardNumber = bankCardNumber;
    }

    // ========== 工厂方法 ==========
    
    /**
     * 创建默认银行信息
     */
    public static UserBankInfo createDefault() {
        return new UserBankInfo("", "");
    }
    
    /**
     * 创建基础银行信息
     */
    public static UserBankInfo createBasic(String bankName, String bankCardNumber) {
        return new UserBankInfo(bankName, bankCardNumber);
    }

    // ========== 业务方法 ==========
    
    /**
     * 是否有银行信息
     */
    public boolean hasBankInfo() {
        return this.bankName != null && !this.bankName.trim().isEmpty()
            && this.bankCardNumber != null && !this.bankCardNumber.trim().isEmpty();
    }
    
    /**
     * 获取银行卡号掩码显示（隐藏中间部分）
     */
    public String getMaskedBankCardNumber() {
        if (this.bankCardNumber == null || this.bankCardNumber.length() < 8) {
            return this.bankCardNumber;
        }
        
        String cardNumber = this.bankCardNumber;
        int length = cardNumber.length();
        
        if (length <= 8) {
            return cardNumber.substring(0, 4) + "****";
        } else {
            return cardNumber.substring(0, 4) + "****" + cardNumber.substring(length - 4);
        }
    }
    
    /**
     * 验证银行卡号格式
     */
    public boolean isValidBankCardNumber() {
        if (this.bankCardNumber == null || this.bankCardNumber.trim().isEmpty()) {
            return false;
        }
        
        // 银行卡号通常为10-25位数字
        String regex = "^\\d{10,25}$";
        return Pattern.matches(regex, this.bankCardNumber.trim());
    }

    // ========== 更新方法 ==========
    
    /**
     * 更新银行信息
     */
    public UserBankInfo updateBankInfo(String bankName, String bankCardNumber) {
        return new UserBankInfo(bankName, bankCardNumber);
    }

    // ========== 验证方法 ==========
    
    /**
     * 验证银行信息是否有效
     */
    public boolean isValid() {
        return this.bankName != null && !this.bankName.trim().isEmpty()
            && this.bankCardNumber != null && !this.bankCardNumber.trim().isEmpty()
            && this.isValidBankCardNumber();
    }

    // ========== 安全方法 ==========
    
    /**
     * 获取安全的银行信息（用于日志或显示）
     */
    public UserBankInfo getSafeInfo() {
        return new UserBankInfo(
            this.bankName,
            this.getMaskedBankCardNumber() // 使用掩码卡号
        );
    }
}

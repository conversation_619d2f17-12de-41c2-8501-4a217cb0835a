package com.miaowen.oa.system.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 * 
 * 功能说明：
 * 1. 定义系统支持的操作类型
 * 2. 提供类型码和描述的映射
 * 3. 便于类型判断和转换
 * 4. 支持扩展新的操作类型
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Getter
@AllArgsConstructor
public enum OperationTypeEnum {

    /**
     * 查看操作
     */
    VIEW(1, "查看", "view", "查询和浏览数据"),

    /**
     * 新增操作
     */
    CREATE(2, "新增", "create", "创建新的数据记录"),

    /**
     * 修改操作
     */
    UPDATE(3, "修改", "update", "更新已有数据记录"),

    /**
     * 删除操作
     */
    DELETE(4, "删除", "delete", "删除数据记录"),

    /**
     * 导出操作
     */
    EXPORT(5, "导出", "export", "导出数据到文件"),

    /**
     * 导入操作
     */
    IMPORT(6, "导入", "import", "从文件导入数据"),

    /**
     * 登录操作
     */
    LOGIN(7, "登录", "login", "用户登录系统"),

    /**
     * 登出操作
     */
    LOGOUT(8, "登出", "logout", "用户退出系统"),

    /**
     * 其他操作
     */
    OTHER(9, "其他", "other", "其他类型的操作");

    /**
     * 操作类型码
     */
    private final Integer code;

    /**
     * 操作类型名称
     */
    private final String name;

    /**
     * 操作类型英文名
     */
    private final String englishName;

    /**
     * 操作类型描述
     */
    private final String description;

    /**
     * 根据类型码获取枚举
     * 
     * @param code 类型码
     * @return 操作类型枚举
     */
    public static OperationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OperationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据类型码获取名称
     * 
     * @param code 类型码
     * @return 类型名称
     */
    public static String getNameByCode(Integer code) {
        OperationTypeEnum type = getByCode(code);
        return type != null ? type.getName() : "未知";
    }

    /**
     * 根据类型码获取英文名
     * 
     * @param code 类型码
     * @return 英文名
     */
    public static String getEnglishNameByCode(Integer code) {
        OperationTypeEnum type = getByCode(code);
        return type != null ? type.getEnglishName() : "unknown";
    }

    /**
     * 根据类型码获取描述
     * 
     * @param code 类型码
     * @return 类型描述
     */
    public static String getDescriptionByCode(Integer code) {
        OperationTypeEnum type = getByCode(code);
        return type != null ? type.getDescription() : "未知操作类型";
    }

    /**
     * 判断是否为读操作
     * 
     * @param code 类型码
     * @return 是否为读操作
     */
    public static boolean isReadOperation(Integer code) {
        return VIEW.getCode().equals(code) || EXPORT.getCode().equals(code);
    }

    /**
     * 判断是否为写操作
     * 
     * @param code 类型码
     * @return 是否为写操作
     */
    public static boolean isWriteOperation(Integer code) {
        return CREATE.getCode().equals(code) || 
               UPDATE.getCode().equals(code) || 
               DELETE.getCode().equals(code) ||
               IMPORT.getCode().equals(code);
    }

    /**
     * 判断是否为高风险操作
     * 
     * @param code 类型码
     * @return 是否为高风险操作
     */
    public static boolean isHighRiskOperation(Integer code) {
        return DELETE.getCode().equals(code);
    }

    /**
     * 判断是否为认证操作
     * 
     * @param code 类型码
     * @return 是否为认证操作
     */
    public static boolean isAuthOperation(Integer code) {
        return LOGIN.getCode().equals(code) || LOGOUT.getCode().equals(code);
    }
}

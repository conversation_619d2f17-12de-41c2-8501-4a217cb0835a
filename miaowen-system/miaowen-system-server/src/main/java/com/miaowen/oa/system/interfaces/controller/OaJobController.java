package com.miaowen.oa.system.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.domain.service.OaJobService;
import com.miaowen.oa.system.interfaces.req.job.JobPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.job.JobSaveRequest;
import com.miaowen.oa.system.interfaces.req.job.JobUpdateRequest;
import com.miaowen.oa.system.interfaces.res.job.OaJobDetailResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobPageRsep;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2025/7/15 10:14
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Tag(name = "岗位管理")
@RestController
@RequestMapping("/job")
@AllArgsConstructor
public class OaJobController {

    private final OaJobService oaJobService;

    @Operation(summary = "新增岗位")
    @PostMapping
    public CommonResult<Void> addJob(@RequestBody JobSaveRequest jobSaveRequest) {
        oaJobService.addJob(jobSaveRequest);
        return CommonResult.success(null);
    }

    @Operation(summary = "编辑岗位")
    @PutMapping
    public CommonResult<Void> updateJob(@RequestBody JobUpdateRequest jobUpdateRequest) {
        oaJobService.updateJob(jobUpdateRequest);

        return CommonResult.success(null);
    }

    @Operation(summary = "分页查询岗位列表")
    @GetMapping("/page")
    public CommonResult<PageResult<OaJobPageRsep>> getJobPageList(JobPageQueryRequest jobPageQueryRequest) {
        PageResult<OaJobPageRsep> pageResult = oaJobService.getJobPageList(jobPageQueryRequest);


        return CommonResult.success(pageResult);
    }

    @Operation(summary = "按ID查询岗位")
    @GetMapping("/{id}")
    public CommonResult<OaJobDetailResp> getJobById(@PathVariable("id") Long id) {
        OaJobDetailResp oaJobDetailResp = oaJobService.getJobById(id);
        return CommonResult.success(oaJobDetailResp);
    }

    @Operation(summary = "删除岗位")
    @DeleteMapping("/{id}")
    public CommonResult<Void> deleteJob(@PathVariable("id") Long id) {
        oaJobService.removeById(id);
        return CommonResult.success(null);
    }
}

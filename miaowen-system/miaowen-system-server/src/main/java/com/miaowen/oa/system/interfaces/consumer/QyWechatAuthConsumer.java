package com.miaowen.oa.system.interfaces.consumer;

import com.miaowen.oa.framework.mq.rabbitmq.core.login.QyWechatMessage;
import com.miaowen.oa.framework.mq.rabbitmq.core.login.QyWechatTopic;
import com.miaowen.oa.system.application.repository.OaUserService;
import com.miaowen.oa.system.interfaces.req.user.UserCreateRequest;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;

/**
 * QyWechatAuthConsumer :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-17
 */
@Slf4j
@Component
@AllArgsConstructor
public class QyWechatAuthConsumer {
    private final OaUserService oaUserService;

    @RabbitListener(bindings = @QueueBinding(
        value = @Queue(name = QyWechatTopic.QUEUE, durable = "true"),
        exchange = @Exchange(name = QyWechatTopic.EXCHANGE, type = ExchangeTypes.DIRECT),
        key = QyWechatTopic.KEY
    ))
    public void handleAvgConvertCostWarnMessage(Message<QyWechatMessage> message, Channel channel,
                                                @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        try {
            QyWechatMessage payload = message.getPayload();
            oaUserService.createUserIfNotExists(new UserCreateRequest(payload.getQyWechatUserId(), payload.getUserTicket()));
        } catch (Exception e) {
            log.error("用户认证成功消息", e);
        }finally {
            // 确认消息
            channel.basicAck(tag, false);
        }

    }
}

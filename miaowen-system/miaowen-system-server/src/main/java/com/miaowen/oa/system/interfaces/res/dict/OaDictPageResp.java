package com.miaowen.oa.system.interfaces.res.dict;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/15 15:49
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaDictPageResp {

    private Integer id;

    /**
     * 字典名称
     */
    private String dictName;
    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 0-正常 1-禁用
     */
    private Integer state;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private Long updater;

    private String updaterName;


}

package com.miaowen.oa.system.interfaces.req.user;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */

@Data
@Accessors(chain = true)
public class UserEditRequest {

    /**
     * 用户id
     */
    private Long id;

    /**
     * 性别
     */
    String gender;

    /**
     * 电话号码
     */
    String mobile;

    /**
     * 个人邮箱
     */
    String email;

    /**
     * 身份证号
     */
    private String identity;

    /**
     * 所属管理部门ids
     */
    private List<Long> deptIds;

    /**
     * 岗位ids
     */
    private List<Long>  jobIds;

    /**
     * 职级
     */
    private String level;

    /**
     * 上级领导用户id
     */
    private List<Long>  parentIds;


    /**
     * 排序
     */
    private String sort;

    /**
     * 数据权限:0自己 1部门 2全公司
     */
    private Integer dataLimitRank;
}

package com.miaowen.oa.system.infrastructure.filter;

import lombok.Data;

/**
 * 操作上下文
 * 
 * 功能说明：
 * 1. 存储单次请求的操作信息
 * 2. 遵循空间局部性原理，将相关数据聚合在一起
 * 3. 按字段访问频率排序，提高缓存命中率
 * 4. 使用ThreadLocal存储，避免参数传递
 * 
 * 设计原则：
 * - 空间局部性：相关字段放在一起
 * - 时间局部性：常用字段放在前面
 * - 内存优化：使用合适的数据类型
 * - 线程安全：配合ThreadLocal使用
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Data
public class OperationContext {

    // ========== 时间相关字段（最常访问） ==========
    
    /**
     * 请求开始时间（毫秒）
     */
    private Long startTime;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;

    // ========== 用户信息（高频访问） ==========
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;

    // ========== 操作信息（高频访问） ==========
    
    /**
     * 操作类型
     * 1-查看 2-新增 3-修改 4-删除 5-导出 6-导入 7-登录 8-登出 9-其他
     */
    private Integer operationType;
    
    /**
     * 操作模块
     */
    private String operationModule;
    
    /**
     * 操作功能
     */
    private String operationFunction;
    
    /**
     * 操作描述
     */
    private String operationDescription;
    
    /**
     * 操作状态
     * 0-失败 1-成功
     */
    private Integer operationState;

    // ========== 请求信息（中频访问） ==========
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 请求URL
     */
    private String requestUrl;
    
    /**
     * 请求参数
     */
    private String requestParams;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;

    // ========== 业务信息（中频访问） ==========
    
    /**
     * 业务数据ID
     */
    private Long businessId;
    
    /**
     * 业务数据类型
     */
    private String businessType;

    // ========== 数据变更信息（中频访问） ==========
    
    /**
     * 操作前数据
     */
    private String beforeData;
    
    /**
     * 操作后数据
     */
    private String afterData;
    
    /**
     * 变更字段信息
     */
    private String changedFields;

    // ========== 响应信息（中频访问） ==========
    
    /**
     * 响应结果
     */
    private String responseResult;

    // ========== 风险和安全信息（低频访问） ==========
    
    /**
     * 风险等级
     * 1-低风险 2-中风险 3-高风险
     */
    private Integer riskLevel;
    
    /**
     * 是否敏感操作
     * 0-否 1-是
     */
    private Integer isSensitive;
    
    /**
     * 操作来源
     * 1-Web端 2-移动端 3-API调用 4-定时任务 5-系统内部
     */
    private Integer operationSource;

    // ========== 追踪和调试信息（低频访问） ==========
    
    /**
     * 链路追踪ID
     */
    private String traceId;
    
    /**
     * 异常信息
     */
    private Exception exception;

    // ========== 辅助方法 ==========

    /**
     * 是否操作成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return operationState != null && operationState == 1;
    }

    /**
     * 是否有异常
     * 
     * @return 是否有异常
     */
    public boolean hasException() {
        return exception != null;
    }

    /**
     * 是否有数据变更
     * 
     * @return 是否有数据变更
     */
    public boolean hasDataChange() {
        return (beforeData != null && !beforeData.isEmpty()) || 
               (afterData != null && !afterData.isEmpty());
    }

    /**
     * 是否为读操作
     * 
     * @return 是否为读操作
     */
    public boolean isReadOperation() {
        return operationType != null && (operationType == 1 || operationType == 5);
    }

    /**
     * 是否为写操作
     * 
     * @return 是否为写操作
     */
    public boolean isWriteOperation() {
        return operationType != null && (operationType == 2 || operationType == 3 || operationType == 4);
    }

    /**
     * 是否为高风险操作
     * 
     * @return 是否为高风险操作
     */
    public boolean isHighRisk() {
        return riskLevel != null && riskLevel >= 3;
    }

    /**
     * 是否为敏感操作
     * 
     * @return 是否为敏感操作
     */
    public boolean isSensitiveOperation() {
        return isSensitive != null && isSensitive == 1;
    }

    /**
     * 获取操作类型名称
     * 
     * @return 操作类型名称
     */
    public String getOperationTypeName() {
        if (operationType == null) {
            return "未知";
        }
        switch (operationType) {
            case 1: return "查看";
            case 2: return "新增";
            case 3: return "修改";
            case 4: return "删除";
            case 5: return "导出";
            case 6: return "导入";
            case 7: return "登录";
            case 8: return "登出";
            case 9: return "其他";
            default: return "未知";
        }
    }

    /**
     * 获取风险等级名称
     * 
     * @return 风险等级名称
     */
    public String getRiskLevelName() {
        if (riskLevel == null) {
            return "未知";
        }
        switch (riskLevel) {
            case 1: return "低风险";
            case 2: return "中风险";
            case 3: return "高风险";
            default: return "未知";
        }
    }

    /**
     * 获取执行时间描述
     * 
     * @return 执行时间描述
     */
    public String getExecutionTimeDesc() {
        if (executionTime == null) {
            return "未知";
        }
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else if (executionTime < 60000) {
            return String.format("%.2fs", executionTime / 1000.0);
        } else {
            return String.format("%.2fmin", executionTime / 60000.0);
        }
    }

    /**
     * 重置上下文（用于对象池复用）
     */
    public void reset() {
        this.startTime = null;
        this.executionTime = null;
        this.userId = null;
        this.username = null;
        this.operationType = null;
        this.operationModule = null;
        this.operationFunction = null;
        this.operationDescription = null;
        this.operationState = null;
        this.requestMethod = null;
        this.requestUrl = null;
        this.requestParams = null;
        this.clientIp = null;
        this.userAgent = null;
        this.businessId = null;
        this.businessType = null;
        this.beforeData = null;
        this.afterData = null;
        this.changedFields = null;
        this.responseResult = null;
        this.riskLevel = null;
        this.isSensitive = null;
        this.operationSource = null;
        this.traceId = null;
        this.exception = null;
    }
}

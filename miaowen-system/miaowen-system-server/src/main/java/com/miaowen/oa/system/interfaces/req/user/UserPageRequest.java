package com.miaowen.oa.system.interfaces.req.user;


import com.miaowen.oa.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */

@Data
@Accessors(chain = true)
public class UserPageRequest extends PageParam {

    /**
     * 起始-生成时间
     */
    String startTime;

    /**
     * 终止-生成时间
     */
    String endTime;

    /**
     * 性别
     */
    String gender;

    /**
     * 部门ids
     */
    List<Long> departmentIds;


    /**
     * 电话号码
     */
    String phone;

    /**
     * 角色ids
     */
    private List<Long> roleIds;

    /**
     * 岗位ids
     */
    private List<Long> jobIds;

    /**
     * 所属项目组ids
     */
    private List<Long> projectGroupIds;

    /**
     * 状态
     */
    private String state;


    /**
     * 关键字
     */
    private String primaryKey;


}

package com.miaowen.oa.system.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.domain.service.OaApplicationService;
import com.miaowen.oa.system.infrastructure.entity.OaApplicationEntity;
import com.miaowen.oa.system.interfaces.req.application.ApplicationPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.application.ApplicationSaveRequest;
import com.miaowen.oa.system.interfaces.req.application.ApplicationUpdateRequest;
import com.miaowen.oa.system.interfaces.res.application.OaApplicationDetailResp;
import com.miaowen.oa.system.interfaces.res.application.OaApplicationPageResp;
import com.miaowen.oa.system.interfaces.res.application.OaApplicationResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/17 15:22
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Tag(name = "应用管理")
@Slf4j
@RequestMapping("/application")
@RestController
@AllArgsConstructor
public class OaApplicationController {
    @Autowired
    private OaApplicationService oaApplicationService;

    /**
     * 新增应用
     *
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "新增应用")
    public CommonResult<Void> addApplication(@RequestBody ApplicationSaveRequest applicationSaveRequest) {
        oaApplicationService.addApplication(applicationSaveRequest);
        return CommonResult.success(null);
    }

    /**
     * 编辑应用
     *
     * @return 操作结果
     */
    @PutMapping
    @Operation(summary = "编辑应用")
    public CommonResult<Void> updateApplication(@RequestBody ApplicationUpdateRequest applicationUpdateRequest) {
        oaApplicationService.updateApplication(applicationUpdateRequest);
        return CommonResult.success(null);
    }

    /**
     * 删除应用
     *
     * @param id 应用ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除应用")
    public CommonResult<Void> deleteApplication(@PathVariable("id") Long id) {
        oaApplicationService.deleteApplication(id);
        return CommonResult.success(null);
    }

    /**
     * 获取分页应用列表
     *
     * @param page 分页参数
     * @return 应用列表
     */
    @GetMapping("/page")
    @Operation(summary = "获取分页应用列表")
    public CommonResult<PageResult<OaApplicationPageResp>> getApplicationPage(ApplicationPageQueryRequest page) {
        PageResult<OaApplicationPageResp> pageResult = oaApplicationService.getApplicationPage(page);

        return CommonResult.success(pageResult);
    }

    /**
     * 获取应用列表
     */
    @GetMapping
    @Operation(summary = "获取应用列表")
    public CommonResult<List<OaApplicationResp>> getApplicationList() {
        List<OaApplicationResp> list = oaApplicationService.getApplicationList();
        return CommonResult.success(list);

    }

    /**
     * 获取应用详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "编辑部门")
    public CommonResult<OaApplicationDetailResp> getApplicationDetail(@PathVariable("id") Long id) {
        OaApplicationDetailResp oaApplicationDetailResp = oaApplicationService.getApplicationDetail(id);
        return CommonResult.success(oaApplicationDetailResp);
    }


}

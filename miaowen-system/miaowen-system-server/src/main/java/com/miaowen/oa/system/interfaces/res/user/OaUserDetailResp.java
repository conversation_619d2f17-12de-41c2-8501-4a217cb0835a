package com.miaowen.oa.system.interfaces.res.user;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.oa.system.interfaces.res.dept.UserDepartmentResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobResp;
import com.miaowen.oa.system.interfaces.res.job.OaUserJobExperienceResp;
import com.miaowen.oa.system.interfaces.res.project.OaProjectGroupResp;
import com.miaowen.oa.system.interfaces.res.role.OaRoleResp;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaUserDetailResp {

    /**
     * 用户id
     */

    private Long id;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 真实名称
     */
    private String realName;


    /**
     * 员工编号(工号):固定四位数字,例如0001, 0002
     */
    private String userCode;


    /**
     * 1男 2女
     */
    private Integer gender;



    /**
     * 手机号
     */
    private String phone;

    /**
     * 个人邮箱
     */
    private String email;

    /**
     * 身份证号
     */
    private String identity;






    /**
     * 所属部门
     */
    private List<UserDepartmentResp> departments;

    /**
     * 管理的部门
     */
    private List<UserDepartmentResp> managedDepartments;

    /**
     * 所属项目组
     */
    private List<OaProjectGroupResp> projectGroups;

    /**
     * 职级
     */
    private String level;

    /**
     * 直属上级
     */
    private List<OaLeaderResp> leaders;

    /**
     * 角色
     */
    private List<OaRoleResp> roles;

    /**
     * 岗位
     */
    private List<OaJobResp> jobs;


    /**
     * 状态： 1正常 2禁用
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;


    /**
     * 数据权限:0自己 1部门 2全公司
     */
    private Integer dataLimitRank;


    /**
     *  生成时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    /**
     * 职业历程
     */
    List<OaUserJobExperienceResp> experiences;
}

package com.miaowen.oa.system.interfaces.controller;

/**
 * @ClassName OaUserController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/10 19:57
 */


import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.application.repository.OaUserService;
import com.miaowen.oa.system.interfaces.req.user.*;
import com.miaowen.oa.system.interfaces.res.user.OaUserDetailResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2025/7/10 19:57
 * @Company 武汉市妙闻网络科技有限公司
 * @Description /api/$version(/in)?/$service
 */
@Tag(name = "用户管理")
@Slf4j
@RequestMapping("/user")
@RestController
public class OaUserController {

    @Resource
    private OaUserService oaUserService;


    @Operation(summary = "用户分页列表")
    @PermitAll
    @GetMapping("")
    public CommonResult<?> page(@ModelAttribute UserPageRequest request) {
        return CommonResult.success(oaUserService.page(request));
    }


    @Operation(summary = "用户详情")
    @PermitAll
    @GetMapping("/{id}")
    private CommonResult<?> detail(@PathVariable("id") Long id) {
        return CommonResult.success(oaUserService.detail(id));
    }



    @Operation(summary = "编辑用户")
    @PermitAll
    @PutMapping("/{id}")
    private CommonResult<?> edit(@PathVariable("id") Long id,
                                 @Validated @RequestBody  UserEditRequest request) {
        request.setId(id);
        oaUserService.edit(request);
        return CommonResult.success();
    }


    @Operation(summary = "重置密码")
    @PermitAll
    @PostMapping("/reset")
    private CommonResult<?> reset(@RequestBody @Validated UserResetRequest request) {
        oaUserService.reset(request);
        return CommonResult.success();
    }


    @Operation(summary = "冻结账户")
    @PermitAll
    @PostMapping("/lock")
    private CommonResult<?> lock(@RequestBody @Validated UserLockRequest request) {
        oaUserService.lock(request);
        return CommonResult.success();
    }


    @Operation(summary = "解冻账户")
    @PermitAll
    @PostMapping("/unlock")
    private CommonResult<?> unlock(@RequestBody @Validated UserUnLockRequest request) {
        oaUserService.unlock(request);
        return CommonResult.success();
    }


    @Operation(summary = "通过部门ids查询用户列表")
    @PermitAll
    @GetMapping("/list")
    public CommonResult<?> getUsers(@RequestParam(name = "deptIds")List<Long> deptIds) {
        List<OaUserDetailResp> list = oaUserService.getUsers(deptIds);
        return CommonResult.success(list);
    }



    @Operation(summary = "个人信息")
    @PermitAll
    @GetMapping("/info")
    public CommonResult<?> info() {
        return CommonResult.success(oaUserService.info());
    }


    @Operation(summary = "刷新数据")
    @PermitAll
    @GetMapping("/dataRefresh")
    public CommonResult<?> dataRefresh() {
//        oaUserService.dataRefresh();
        return CommonResult.success();
    }


}

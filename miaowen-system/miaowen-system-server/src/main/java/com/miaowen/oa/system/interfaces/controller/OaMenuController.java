package com.miaowen.oa.system.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.web.core.util.WebFrameworkUtils;
import com.miaowen.oa.system.application.repository.OaMenuService;
import com.miaowen.oa.system.interfaces.req.menu.OaMenuAddRequest;
import com.miaowen.oa.system.interfaces.req.menu.OaMenuPageRequest;
import com.miaowen.oa.system.interfaces.req.menu.OaMenuUpdateRequest;
import com.miaowen.oa.system.interfaces.res.menu.OaMenuDetailResp;
import com.miaowen.oa.system.interfaces.res.menu.OaMenuPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Tag(name = "菜单管理")
@Slf4j
@RequestMapping("/menus")
@RestController
public class OaMenuController {

    @Resource
    private OaMenuService oaMenuService;

    @Operation(summary = "创建菜单")
    @PostMapping("")
    @PermitAll
    public CommonResult<Long> createMenu(@Validated @RequestBody OaMenuAddRequest request) {
        Long menuId = oaMenuService.createMenu(request);
        return CommonResult.success(menuId);
    }

    @Operation(summary = "更新菜单")
    @PutMapping("/{id}")
    @PermitAll
    public CommonResult<Void> updateMenu(
            @PathVariable("id") Long id,
            @Validated @RequestBody OaMenuUpdateRequest request) {
        request.setId(id);
        oaMenuService.updateMenu(request);
        return CommonResult.success();
    }

    @Operation(summary = "删除菜单")
    @DeleteMapping("/{id}")
    @PermitAll
    public CommonResult<Void> deleteMenu(@PathVariable("id") Long id) {
        oaMenuService.deleteMenu(id);
        return CommonResult.success();
    }

    @Operation(summary = "获取菜单详情")
    @GetMapping("/{id}")
    @PermitAll
    public CommonResult<OaMenuDetailResp> getMenuDetail(@PathVariable("id") Long id) {
        OaMenuDetailResp menu = oaMenuService.getMenuDetail(id);
        return CommonResult.success(menu);
    }

    @Operation(summary = "查询菜单")
    @GetMapping("/page")
    @PermitAll
    public CommonResult<List<OaMenuPageResp>> queryMenus(@ModelAttribute OaMenuPageRequest request) {
        List<OaMenuPageResp> page = oaMenuService.queryMenus(request);
        return CommonResult.success(page);
    }

    @Operation(summary = "获取菜单树")
    @GetMapping("/tree")
    @PermitAll
    public CommonResult<List<OaMenuPageResp>> getMenuTree(@RequestParam(name = "roleId", required = false) Long roleId) {
        List<OaMenuPageResp> menuTree = oaMenuService.getMenuTree(roleId);
        return CommonResult.success(menuTree);
    }

    @Operation(summary = "获取当前用户菜单树")
    @GetMapping("/user-tree")
    public CommonResult<List<OaMenuDetailResp>> getUserMenuTree() {
        // 从安全上下文中获取当前用户ID
        Long userId = WebFrameworkUtils.getLoginUserId();
        if (Objects.isNull(userId)) {
            // 如果无法获取用户ID，返回空列表而不是抛出异常
            return CommonResult.success(new ArrayList<>());
        }
        List<OaMenuDetailResp> menuTree = oaMenuService.getUserMenuTree(userId);
        return CommonResult.success(menuTree);
    }
}
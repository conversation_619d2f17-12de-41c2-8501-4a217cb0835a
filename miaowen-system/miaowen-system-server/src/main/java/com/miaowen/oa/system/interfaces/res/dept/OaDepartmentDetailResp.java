package com.miaowen.oa.system.interfaces.res.dept;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 14:25
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaDepartmentDetailResp {

    private Long id;
    // 部门名称
    private String departmentName;
    // 上级部门ID
    private Long parentId;
    //排序
    private Integer sort;
    //描述
    private String description;
    //部门编码
    private String departmentCode;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String creatorName;

    private List<DepartmentLeaderUserResp> departmentLeaderUserRespList;
}

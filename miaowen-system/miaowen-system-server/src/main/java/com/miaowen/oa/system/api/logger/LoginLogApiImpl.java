package com.miaowen.oa.system.api.logger;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.api.logger.dto.LoginLogCreateReqDTO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static com.miaowen.oa.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class LoginLogApiImpl implements LoginLogApi {


    @Override
    public CommonResult<Boolean> createLoginLog(LoginLogCreateReqDTO reqDTO) {
        return success(true);
    }

}

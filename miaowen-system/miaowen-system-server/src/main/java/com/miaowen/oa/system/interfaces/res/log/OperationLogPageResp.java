package com.miaowen.oa.system.interfaces.res.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志分页响应
 * 
 * 功能说明：
 * 1. 返回操作日志的详细信息
 * 2. 包含操作前后数据对比
 * 3. 提供友好的显示字段
 * 4. 支持数据脱敏展示
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "操作日志分页响应")
public class OperationLogPageResp {

    // ========== 基础信息 ==========
    
    @Schema(description = "日志ID", example = "1001")
    private Long id;

    @Schema(description = "链路追踪ID", example = "OL1642752000000123456")
    private String traceId;

    @Schema(description = "创建时间", example = "2025-01-18 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2025-01-18 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // ========== 用户信息 ==========
    
    @Schema(description = "操作用户ID", example = "1001")
    private Long userId;

    @Schema(description = "操作用户名", example = "admin")
    private String username;

    // ========== 操作信息 ==========
    
    @Schema(description = "操作类型", example = "2")
    private Integer operationType;

    @Schema(description = "操作类型名称", example = "新增")
    private String operationTypeName;

    @Schema(description = "操作模块", example = "user")
    private String operationModule;

    @Schema(description = "操作功能", example = "创建用户")
    private String operationFunction;

    @Schema(description = "操作描述", example = "创建用户：张三")
    private String operationDescription;

    @Schema(description = "操作状态", example = "1")
    private Integer operationState;

    @Schema(description = "操作状态名称", example = "成功")
    private String operationStateName;

    // ========== 业务信息 ==========
    
    @Schema(description = "业务类型", example = "user")
    private String businessType;

    @Schema(description = "业务ID", example = "1001")
    private Long businessId;

    // ========== 请求信息 ==========
    
    @Schema(description = "请求方法", example = "POST")
    private String requestMethod;

    @Schema(description = "请求URL", example = "/api/v1/user")
    private String requestUrl;

    @Schema(description = "请求参数", example = "{\"username\":\"zhangsan\"}")
    private String requestParams;

    @Schema(description = "客户端IP", example = "*************")
    private String clientIp;

    @Schema(description = "用户代理", example = "Mozilla/5.0...")
    private String userAgent;

    // ========== 执行信息 ==========
    
    @Schema(description = "执行时间(毫秒)", example = "150")
    private Long executionTime;

    @Schema(description = "执行时间描述", example = "150ms")
    private String executionTimeDesc;

    @Schema(description = "响应结果", example = "{\"code\":200,\"message\":\"success\"}")
    private String responseResult;

    @Schema(description = "错误信息", example = "")
    private String errorMessage;

    // ========== 数据变更信息 ==========
    
    @Schema(description = "操作前数据", example = "{\"id\":1001,\"name\":\"旧名称\"}")
    private String beforeData;

    @Schema(description = "操作后数据", example = "{\"id\":1001,\"name\":\"新名称\"}")
    private String afterData;

    @Schema(description = "变更字段", example = "{\"name\":{\"before\":\"旧名称\",\"after\":\"新名称\"}}")
    private String changedFields;

    @Schema(description = "数据版本", example = "1")
    private Integer dataVersion;

    // ========== 风险和安全信息 ==========
    
    @Schema(description = "风险等级", example = "2")
    private Integer riskLevel;

    @Schema(description = "风险等级名称", example = "中风险")
    private String riskLevelName;

    @Schema(description = "是否敏感操作", example = "0")
    private Integer isSensitive;

    @Schema(description = "敏感操作描述", example = "否")
    private String sensitiveDesc;

    @Schema(description = "操作来源", example = "1")
    private Integer operationSource;

    @Schema(description = "操作来源名称", example = "Web端")
    private String operationSourceName;

    @Schema(description = "审计标记", example = "")
    private String auditFlag;

    @Schema(description = "扩展数据", example = "{}")
    private String extraData;

    // ========== 辅助方法 ==========

    /**
     * 是否操作成功
     */
    public boolean isSuccess() {
        return operationState != null && operationState == 1;
    }

    /**
     * 是否有异常
     */
    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    /**
     * 是否有数据变更
     */
    public boolean hasDataChange() {
        return (beforeData != null && !beforeData.trim().isEmpty()) || 
               (afterData != null && !afterData.trim().isEmpty());
    }

    /**
     * 是否为读操作
     */
    public boolean isReadOperation() {
        return operationType != null && (operationType == 1 || operationType == 5);
    }

    /**
     * 是否为写操作
     */
    public boolean isWriteOperation() {
        return operationType != null && (operationType == 2 || operationType == 3 || operationType == 4);
    }

    /**
     * 是否为高风险操作
     */
    public boolean isHighRisk() {
        return riskLevel != null && riskLevel >= 3;
    }

    /**
     * 是否为敏感操作
     */
    public boolean isSensitiveOperation() {
        return isSensitive != null && isSensitive == 1;
    }

    /**
     * 获取操作来源名称
     */
    public String getOperationSourceName() {
        if (operationSource == null) {
            return "未知";
        }
        switch (operationSource) {
            case 1: return "Web端";
            case 2: return "移动端";
            case 3: return "API调用";
            case 4: return "定时任务";
            case 5: return "系统内部";
            default: return "未知";
        }
    }

    /**
     * 获取敏感操作描述
     */
    public String getSensitiveDesc() {
        return isSensitiveOperation() ? "是" : "否";
    }

    /**
     * 获取简短的操作描述
     */
    public String getShortDescription() {
        if (operationDescription == null) {
            return "";
        }
        if (operationDescription.length() <= 50) {
            return operationDescription;
        }
        return operationDescription.substring(0, 47) + "...";
    }

    /**
     * 获取格式化的执行时间
     */
    public String getFormattedExecutionTime() {
        if (executionTime == null) {
            return "未知";
        }
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else if (executionTime < 60000) {
            return String.format("%.2fs", executionTime / 1000.0);
        } else {
            return String.format("%.2fmin", executionTime / 60000.0);
        }
    }

    /**
     * 获取操作结果描述
     */
    public String getResultDescription() {
        if (isSuccess()) {
            return hasError() ? "成功(有警告)" : "成功";
        } else {
            return "失败";
        }
    }
}

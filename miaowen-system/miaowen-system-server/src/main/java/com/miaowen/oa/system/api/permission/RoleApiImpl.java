package com.miaowen.oa.system.api.permission;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;

import static com.miaowen.oa.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class RoleApiImpl implements RoleApi {

    @Override
    public CommonResult<Boolean> validRoleList(Collection<Long> ids) {
        return success(true);
    }
}

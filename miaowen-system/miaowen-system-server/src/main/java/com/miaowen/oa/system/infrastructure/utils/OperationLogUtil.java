package com.miaowen.oa.system.infrastructure.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 操作日志工具类
 * 
 * 功能说明：
 * 1. 提供日志相关的工具方法
 * 2. 数据对比和变更检测
 * 3. 链路追踪ID生成
 * 4. 数据脱敏和安全处理
 * 5. 遵循时间局部性和空间局部性原理
 * 
 * 性能优化：
 * - 时间局部性：缓存常用的格式化器和工具对象
 * - 空间局部性：相关方法聚合在一起
 * - 内存优化：使用对象池和缓存机制
 * - 算法优化：使用高效的数据对比算法
 * 
 * 设计原则：
 * - 工具性：提供纯函数式的工具方法
 * - 性能：高效的算法实现
 * - 安全：数据脱敏和防护
 * - 可扩展：支持自定义扩展
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Slf4j
public class OperationLogUtil {

    /**
     * 日期时间格式化器（线程安全）
     * 时间局部性优化：缓存常用的格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * ObjectMapper实例（线程安全）
     * 空间局部性优化：复用JSON处理器
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 敏感字段列表
     * 时间局部性优化：使用Set提高查找效率
     */
    private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
            "password", "passwd", "pwd", "secret", "token", "key", "auth",
            "credential", "authorization", "signature", "sign", "salt"
    ));

    /**
     * 链路追踪ID前缀
     */
    private static final String TRACE_ID_PREFIX = "OL";

    // ========== 链路追踪相关方法 ==========

    /**
     * 生成链路追踪ID
     * 
     * 格式：OL + 时间戳(13位) + 随机数(6位)
     * 示例：OL1642752000000123456
     * 
     * 性能优化：
     * - 使用ThreadLocalRandom提高并发性能
     * - 避免使用UUID，减少字符串长度
     * - 时间戳保证唯一性，随机数防止冲突
     * 
     * @return 链路追踪ID
     */
    public static String generateTraceId() {
        long timestamp = System.currentTimeMillis();
        int random = ThreadLocalRandom.current().nextInt(100000, 999999);
        return TRACE_ID_PREFIX + timestamp + random;
    }

    /**
     * 验证链路追踪ID格式
     * 
     * @param traceId 链路追踪ID
     * @return 是否有效
     */
    public static boolean isValidTraceId(String traceId) {
        if (!StringUtils.hasText(traceId)) {
            return false;
        }
        return traceId.startsWith(TRACE_ID_PREFIX) && traceId.length() == 21;
    }

    /**
     * 从链路追踪ID中提取时间戳
     * 
     * @param traceId 链路追踪ID
     * @return 时间戳，如果无效则返回null
     */
    public static Long extractTimestampFromTraceId(String traceId) {
        if (!isValidTraceId(traceId)) {
            return null;
        }
        try {
            String timestampStr = traceId.substring(2, 15);
            return Long.valueOf(timestampStr);
        } catch (Exception e) {
            return null;
        }
    }

    // ========== 数据对比相关方法 ==========

    /**
     * 对比两个JSON字符串的差异
     * 
     * 性能优化：
     * - 使用Jackson的JsonNode进行高效对比
     * - 只对比叶子节点，减少递归深度
     * - 空间局部性：相关数据聚合处理
     * 
     * @param beforeJson 操作前JSON
     * @param afterJson 操作后JSON
     * @return 变更字段信息
     */
    public static String compareJsonData(String beforeJson, String afterJson) {
        if (!StringUtils.hasText(beforeJson) || !StringUtils.hasText(afterJson)) {
            return null;
        }

        try {
            JsonNode beforeNode = OBJECT_MAPPER.readTree(beforeJson);
            JsonNode afterNode = OBJECT_MAPPER.readTree(afterJson);

            Map<String, Object> changes = new HashMap<>();
            compareJsonNodes("", beforeNode, afterNode, changes);

            if (changes.isEmpty()) {
                return null;
            }

            return OBJECT_MAPPER.writeValueAsString(changes);

        } catch (Exception e) {
            log.warn("JSON数据对比失败", e);
            return null;
        }
    }

    /**
     * 递归对比JSON节点
     * 
     * @param path 字段路径
     * @param beforeNode 操作前节点
     * @param afterNode 操作后节点
     * @param changes 变更记录
     */
    private static void compareJsonNodes(String path, JsonNode beforeNode, JsonNode afterNode, Map<String, Object> changes) {
        if (beforeNode == null && afterNode == null) {
            return;
        }

        if (beforeNode == null) {
            changes.put(path.isEmpty() ? "added" : path, Map.of("action", "added", "value", afterNode));
            return;
        }

        if (afterNode == null) {
            changes.put(path.isEmpty() ? "removed" : path, Map.of("action", "removed", "value", beforeNode));
            return;
        }

        if (beforeNode.isObject() && afterNode.isObject()) {
            // 对比对象
            Set<String> allFields = new HashSet<>();
            beforeNode.fieldNames().forEachRemaining(allFields::add);
            afterNode.fieldNames().forEachRemaining(allFields::add);

            for (String field : allFields) {
                String fieldPath = path.isEmpty() ? field : path + "." + field;
                compareJsonNodes(fieldPath, beforeNode.get(field), afterNode.get(field), changes);
            }
        } else if (beforeNode.isArray() && afterNode.isArray()) {
            // 对比数组（简化处理）
            if (beforeNode.size() != afterNode.size() || !beforeNode.equals(afterNode)) {
                changes.put(path, Map.of(
                        "action", "modified",
                        "before", beforeNode,
                        "after", afterNode
                ));
            }
        } else {
            // 对比基本类型
            if (!beforeNode.equals(afterNode)) {
                changes.put(path, Map.of(
                        "action", "modified",
                        "before", beforeNode,
                        "after", afterNode
                ));
            }
        }
    }

    /**
     * 提取变更字段列表
     * 
     * @param changedFieldsJson 变更字段JSON
     * @return 变更字段列表
     */
    public static List<String> extractChangedFields(String changedFieldsJson) {
        if (!StringUtils.hasText(changedFieldsJson)) {
            return Collections.emptyList();
        }

        try {
            JsonNode node = OBJECT_MAPPER.readTree(changedFieldsJson);
            List<String> fields = new ArrayList<>();
            node.fieldNames().forEachRemaining(fields::add);
            return fields;
        } catch (Exception e) {
            log.warn("提取变更字段失败", e);
            return Collections.emptyList();
        }
    }

    // ========== 数据脱敏相关方法 ==========

    /**
     * 对敏感数据进行脱敏处理
     * 
     * 安全优化：
     * - 识别敏感字段并进行脱敏
     * - 保留数据结构，只脱敏敏感值
     * - 支持自定义脱敏规则
     * 
     * @param jsonData JSON数据
     * @return 脱敏后的JSON数据
     */
    public static String maskSensitiveData(String jsonData) {
        if (!StringUtils.hasText(jsonData)) {
            return jsonData;
        }

        try {
            jsonData = jsonData.contains("body") ? extractJsonBody(jsonData) : jsonData;
            JsonNode node = OBJECT_MAPPER.readTree(jsonData);
            JsonNode maskedNode = maskJsonNode(node);
            return OBJECT_MAPPER.writeValueAsString(maskedNode);
        } catch (Exception e) {
            log.warn("数据脱敏失败", e);
            return jsonData;
        }
    }


    /**
     * 从原始字符串中提取 JSON 主体部分
     *
     * @param input 包含 "body:" 前缀的原始字符串
     * @return 纯 JSON 字符串
     * @throws IllegalArgumentException 如果输入格式无效
     */
    public static String extractJsonBody(String input) {
        if (input == null || input.isEmpty()) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }

        // 1. 查找 "body:" 的位置
        int bodyIndex = input.indexOf("body:");
        if (bodyIndex == -1) {
            throw new IllegalArgumentException("输入字符串中未找到 'body:' 前缀");
        }

        // 2. 获取 "body:" 之后的部分
        String jsonPart = input.substring(bodyIndex + "body:".length());

        // 3. 去除前导空白字符（包括换行符和空格）
        jsonPart = jsonPart.trim();

        // 4. 验证 JSON 结构
        if (!isValidJsonStructure(jsonPart)) {
            throw new IllegalArgumentException("提取的内容不是有效的 JSON 结构");
        }

        return jsonPart;
    }

    /**
     * 验证字符串是否为有效的 JSON 结构
     *
     * @param jsonStr 待验证的字符串
     * @return 是否是有效的 JSON 结构
     */
    private static boolean isValidJsonStructure(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return false;
        }

        // 简单的结构验证（不进行完整解析）
        String trimmed = jsonStr.trim();
        return (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                (trimmed.startsWith("[") && trimmed.endsWith("]"));
    }

    /**
     * 递归脱敏JSON节点
     * 
     * @param node JSON节点
     * @return 脱敏后的节点
     */
    private static JsonNode maskJsonNode(JsonNode node) {
        if (node.isObject()) {
            Map<String, Object> result = new HashMap<>();
            node.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (isSensitiveField(key)) {
                    result.put(key, maskValue(value.asText()));
                } else {
                    result.put(key, maskJsonNode(value));
                }
            });
            return OBJECT_MAPPER.valueToTree(result);
        } else if (node.isArray()) {
            List<Object> result = new ArrayList<>();
            for (JsonNode item : node) {
                result.add(maskJsonNode(item));
            }
            return OBJECT_MAPPER.valueToTree(result);
        } else {
            return node;
        }
    }

    /**
     * 判断是否为敏感字段
     * 
     * @param fieldName 字段名
     * @return 是否敏感
     */
    private static boolean isSensitiveField(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        return SENSITIVE_FIELDS.stream().anyMatch(lowerFieldName::contains);
    }

    /**
     * 脱敏字段值
     * 
     * @param value 原始值
     * @return 脱敏后的值
     */
    private static String maskValue(String value) {
        if (!StringUtils.hasText(value)) {
            return value;
        }
        
        if (value.length() <= 2) {
            return "**";
        } else if (value.length() <= 6) {
            return value.charAt(0) + "***" + value.charAt(value.length() - 1);
        } else {
            return value.substring(0, 2) + "****" + value.substring(value.length() - 2);
        }
    }

    // ========== 字符串处理相关方法 ==========

    /**
     * 限制字符串长度
     * 
     * 性能优化：避免存储过长的字符串
     * 
     * @param str 原始字符串
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    public static String limitStringLength(String str, int maxLength) {
        if (!StringUtils.hasText(str) || str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength - 3) + "...";
    }

    /**
     * 清理SQL注入风险字符
     * 
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public static String cleanSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }
        
        // 移除常见的SQL注入字符
        return input.replaceAll("[';\"\\-\\-/\\*\\*/]", "");
    }

    /**
     * 格式化执行时间
     * 
     * @param executionTime 执行时间（毫秒）
     * @return 格式化后的时间字符串
     */
    public static String formatExecutionTime(Long executionTime) {
        if (executionTime == null) {
            return "未知";
        }
        
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else if (executionTime < 60000) {
            return String.format("%.2fs", executionTime / 1000.0);
        } else {
            return String.format("%.2fmin", executionTime / 60000.0);
        }
    }

    /**
     * 格式化日期时间
     * 
     * @param dateTime 日期时间
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 格式化日期
     * 
     * @param dateTime 日期时间
     * @return 格式化后的日期字符串
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DATE_FORMATTER);
    }

    /**
     * 格式化时间
     * 
     * @param dateTime 日期时间
     * @return 格式化后的时间字符串
     */
    public static String formatTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(TIME_FORMATTER);
    }

    // ========== 验证相关方法 ==========

    /**
     * 验证JSON格式
     * 
     * @param jsonStr JSON字符串
     * @return 是否为有效JSON
     */
    public static boolean isValidJson(String jsonStr) {
        if (!StringUtils.hasText(jsonStr)) {
            return false;
        }
        
        try {
            OBJECT_MAPPER.readTree(jsonStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取JSON数据大小（字节）
     * 
     * @param jsonStr JSON字符串
     * @return 数据大小
     */
    public static int getJsonSize(String jsonStr) {
        if (!StringUtils.hasText(jsonStr)) {
            return 0;
        }
        return jsonStr.getBytes().length;
    }
}

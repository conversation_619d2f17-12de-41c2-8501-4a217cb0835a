package com.miaowen.oa.system.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.infrastructure.entity.OaOperationLogEntity;
import com.miaowen.oa.system.interfaces.req.log.OperationLogPageRequest;
import com.miaowen.oa.system.interfaces.res.log.OperationLogPageResp;
import com.miaowen.oa.system.interfaces.res.log.OperationLogStatisticsResp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 操作日志服务接口
 * 
 * 功能说明：
 * 1. 定义操作日志的核心业务方法
 * 2. 支持CRUD操作和复杂查询
 * 3. 提供统计分析功能
 * 4. 支持异步处理和批量操作
 * 5. 提供数据导出和清理功能
 * 
 * 设计原则：
 * - 接口职责单一，方法语义清晰
 * - 支持异步处理，提高系统性能
 * - 提供丰富的查询和统计方法
 * - 考虑扩展性和可维护性
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
public interface OperationLogService extends IService<OaOperationLogEntity> {

    // ========== 查询方法 ==========

    /**
     * 分页查询操作日志
     * 
     * 功能说明：
     * - 支持多条件筛选查询
     * - 支持关键字模糊搜索
     * - 按时间倒序排列
     * - 分页返回结果
     * 
     * @param request 查询请求参数
     * @return 分页查询结果
     */
    PageResult<OperationLogPageResp> pageOperationLog(OperationLogPageRequest request);

    /**
     * 根据ID查询操作日志详情
     * 
     * 功能说明：
     * - 查询单条日志的详细信息
     * - 包含操作前后数据对比
     * - 显示完整的操作上下文
     * 
     * @param id 日志ID
     * @return 操作日志详情
     */
    OperationLogPageResp getOperationLogById(Long id);

    /**
     * 查询用户最近操作记录
     * 
     * 功能说明：
     * - 查询指定用户的最近操作
     * - 按时间倒序排列
     * - 限制返回数量
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近操作记录列表
     */
    List<OperationLogPageResp> getUserRecentOperations(Long userId, Integer limit);

    /**
     * 查询业务数据操作历史
     * 
     * 功能说明：
     * - 查询指定业务数据的所有操作记录
     * - 用于数据变更追踪
     * - 支持数据审计
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 业务数据操作历史
     */
    List<OperationLogPageResp> getBusinessOperationHistory(String businessType, Long businessId);

    /**
     * 查询高风险操作记录
     * 
     * 功能说明：
     * - 查询指定风险等级以上的操作
     * - 支持时间范围筛选
     * - 用于安全监控
     * 
     * @param riskLevel 风险等级
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 高风险操作记录列表
     */
    List<OperationLogPageResp> getHighRiskOperations(Integer riskLevel, LocalDateTime startTime, LocalDateTime endTime);

    // ========== 统计方法 ==========

    /**
     * 统计用户操作次数
     * 
     * 功能说明：
     * - 统计指定用户在时间范围内的操作次数
     * - 用于用户活跃度分析
     * - 支持自定义时间范围
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    Long countUserOperations(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取操作日志统计信息
     * 
     * 功能说明：
     * - 提供全面的统计分析数据
     * - 包含操作类型分布、用户活跃度等
     * - 支持时间范围筛选
     * - 用于数据分析和报表展示
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    OperationLogStatisticsResp getOperationLogStatistics(LocalDateTime startTime, LocalDateTime endTime);

    // ========== 记录方法 ==========

    /**
     * 异步记录操作日志
     * 
     * 功能说明：
     * - 异步记录单条操作日志
     * - 不阻塞主业务流程
     * - 支持数据脱敏处理
     * - 自动设置默认值
     * 
     * @param operationLog 操作日志实体
     * @return 异步执行结果
     */
    CompletableFuture<Boolean> recordOperationLogAsync(OaOperationLogEntity operationLog);

    /**
     * 批量记录操作日志
     * 
     * 功能说明：
     * - 批量记录多条操作日志
     * - 提高写入性能
     * - 支持事务处理
     * - 自动数据脱敏
     * 
     * @param operationLogs 操作日志列表
     * @return 成功记录的数量
     */
    Integer batchRecordOperationLogs(List<OaOperationLogEntity> operationLogs);

    // ========== 导出方法 ==========

    /**
     * 导出操作日志
     * 
     * 功能说明：
     * - 根据查询条件导出日志数据
     * - 支持多种格式导出
     * - 限制导出数量防止性能问题
     * 
     * @param request 查询条件
     * @return 导出文件内容或文件路径
     */
    String exportOperationLogs(OperationLogPageRequest request);

    /**
     * 异步导出操作日志
     * 
     * 功能说明：
     * - 异步导出大量日志数据
     * - 避免长时间等待
     * - 支持后台处理
     * 
     * @param request 查询条件
     * @return 异步执行结果
     */
    CompletableFuture<String> exportOperationLogsAsync(OperationLogPageRequest request);

    // ========== 清理方法 ==========

    /**
     * 异步清理过期日志
     * 
     * 功能说明：
     * - 清理超过保留期限的日志数据
     * - 异步执行，不阻塞请求
     * - 支持自定义保留天数
     * - 软删除处理
     * 
     * @param retentionDays 保留天数
     * @return 异步执行结果，返回清理的记录数
     */
    CompletableFuture<Integer> cleanExpiredLogsAsync(Integer retentionDays);

    // ========== 数据恢复方法 ==========

    /**
     * 根据日志恢复数据
     * 
     * 功能说明：
     * - 根据操作日志中的数据恢复业务数据
     * - 用于误操作恢复
     * - 需要管理员权限
     * - 支持数据回滚
     * 
     * @param logId 日志ID
     * @return 恢复是否成功
     */
    Boolean recoverDataByLog(Long logId);

    /**
     * 获取数据变更摘要
     * 
     * 功能说明：
     * - 获取操作日志的数据变更摘要
     * - 用于快速了解数据变更情况
     * - 支持变更对比展示
     * 
     * @param logId 日志ID
     * @return 数据变更摘要
     */
    String getDataChangeSummary(Long logId);

    // ========== 扩展方法 ==========

    /**
     * 检查操作权限
     * 
     * 功能说明：
     * - 检查用户是否有权限执行指定操作
     * - 用于权限控制和安全检查
     * 
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param resourceType 资源类型
     * @return 是否有权限
     */
    default Boolean checkOperationPermission(Long userId, Integer operationType, String resourceType) {
        // 默认实现，子类可以重写
        return true;
    }

    /**
     * 获取操作建议
     * 
     * 功能说明：
     * - 根据历史操作记录提供操作建议
     * - 用于智能提示和优化建议
     * 
     * @param userId 用户ID
     * @param operationType 操作类型
     * @return 操作建议
     */
    default String getOperationSuggestion(Long userId, Integer operationType) {
        // 默认实现，子类可以重写
        return null;
    }

    /**
     * 分析操作模式
     * 
     * 功能说明：
     * - 分析用户的操作模式和习惯
     * - 用于行为分析和异常检测
     * 
     * @param userId 用户ID
     * @param days 分析天数
     * @return 操作模式分析结果
     */
    default String analyzeOperationPattern(Long userId, Integer days) {
        // 默认实现，子类可以重写
        return null;
    }
}

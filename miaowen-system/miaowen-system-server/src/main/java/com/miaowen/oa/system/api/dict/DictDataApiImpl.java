package com.miaowen.oa.system.api.dict;

import com.miaowen.oa.framework.common.biz.system.dict.dto.DictDataRespDTO;
import com.miaowen.oa.framework.common.pojo.CommonResult;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.miaowen.oa.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
@Primary // 由于 DictDataCommonApi 的存在，必须声明为 @Primary Bean
public class DictDataApiImpl implements DictDataApi {



    @Override
    public CommonResult<Boolean> validateDictDataList(String dictType, Collection<String> values) {
        return success(true);
    }

    @Override
    public CommonResult<List<DictDataRespDTO>> getDictDataList(String dictType) {
        return success(Collections.emptyList());
    }

}

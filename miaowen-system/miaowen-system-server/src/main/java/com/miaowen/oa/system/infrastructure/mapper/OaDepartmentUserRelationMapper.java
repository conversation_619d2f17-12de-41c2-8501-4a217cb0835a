package com.miaowen.oa.system.infrastructure.mapper;


import com.miaowen.oa.framework.mybatis.core.mapper.CustomBaseMapper;
import com.miaowen.oa.system.infrastructure.entity.OaDepartmentEntity;
import com.miaowen.oa.system.infrastructure.entity.OaUserDepartmentRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:22
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */

@Mapper
public interface OaDepartmentUserRelationMapper extends CustomBaseMapper<OaUserDepartmentRelationEntity> {

    @Select("<script>" +
            "SELECT user_id AS userId, department_id AS departmentId " +
            "FROM oa_department_user_relation " +
            "WHERE delete_time = 0 and user_id IN " +
            "<foreach item='id' collection='userIds' open='(' separator=',' close=')'>" +
            "   #{id}" +
            "</foreach>" +
            "</script>")
    List<OaUserDepartmentRelationEntity> findByUserIds(@Param("userIds") List<Long> userIds);
}

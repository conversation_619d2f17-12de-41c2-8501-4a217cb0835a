package com.miaowen.oa.system.application.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.miaowen.oa.framework.common.util.json.JsonUtils;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.system.infrastructure.constants.RedisKeyConstants;
import com.miaowen.oa.system.infrastructure.entity.OaMenuEntity;
import com.miaowen.oa.system.infrastructure.entity.OaMenuRoleRelationEntity;
import com.miaowen.oa.system.infrastructure.entity.OaUserRoleRelationEntity;
import com.miaowen.oa.system.infrastructure.mapper.OaMenuMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaMenuRoleRelationMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaRoleMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaUserRoleRelationMapper;
import com.miaowen.oa.system.interfaces.req.menu.OaMenuAddRequest;
import com.miaowen.oa.system.interfaces.req.menu.OaMenuPageRequest;
import com.miaowen.oa.system.interfaces.req.menu.OaMenuUpdateRequest;
import com.miaowen.oa.system.interfaces.res.menu.OaMenuDetailResp;
import com.miaowen.oa.system.interfaces.res.menu.OaMenuPageResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.miaowen.oa.framework.common.exception.util.ServiceExceptionUtil.*;

import static com.miaowen.oa.framework.common.util.collection.CollectionUtils.convertList;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.MENU_NOT_EXISTS;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;

/**
 * 菜单服务层
 * <p>
 * 职责说明：
 * 1. 菜单的CRUD操作
 * 2. 菜单树形结构构建
 * 3. 用户菜单权限查询
 * 4. 菜单与角色关联关系管理
 * <p>
 * 性能优化：
 * - 使用批量查询减少数据库访问
 * - 菜单树构建采用递归算法
 * - 查询结果进行空值过滤
 * <p>
 * 安全措施：
 * - 所有参数进行空值校验
 * - 使用自定义异常统一错误处理
 * - 数据库操作异常捕获
 *
 * <AUTHOR>
 * @Date 2025/7/10 19:50
 * @Company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Repository
public class OaMenuService {


    @Resource
    private OaRoleMapper oaRoleMapper;

    @Resource
    private OaUserRoleRelationMapper oaUserRoleRelationMapper;


    @Resource
    private OaMenuMapper oaMenuMapper;

    @Resource
    private OaMenuRoleRelationMapper oaMenuRoleRelationMapper;


    /**
     * 创建菜单
     * <p>
     * 业务逻辑：
     * 1. 参数校验：检查请求对象是否为空
     * 2. 数据转换：将请求对象转换为实体对象
     * 3. 编码生成：自动生成唯一的菜单编码
     * 4. 父菜单校验：如果有父菜单，校验父菜单是否存在
     * 5. 数据持久化：保存到数据库
     * <p>
     * 性能优化：
     * - 使用UUID生成唯一编码，避免重复查询
     * - 单次数据库插入操作
     *
     * @param request 创建菜单请求对象
     * @return 新创建菜单的ID
     * @throws com.miaowen.oa.framework.common.exception.ServiceException 当参数无效或创建失败时抛出
     */
    public Long createMenu(OaMenuAddRequest request) {
        // 第一步：参数校验
        if (Objects.isNull(request)) {
            log.error("创建菜单失败：请求参数为空");
            throw exception(MENU_NOT_EXISTS);
        }

        // 第二步：校验父菜单是否存在（如果指定了父菜单）
        if (Objects.nonNull(request.getParentId()) && !Objects.equals(0L, request.getParentId())) {
            validateParentMenuExists(request.getParentId());
        }

        // 第三步：数据转换
        OaMenuEntity entity = BeanUtils.toBean(request, OaMenuEntity.class);
        if (Objects.isNull(entity)) {
            log.error("创建菜单失败：数据转换失败");
            throw exception(MENU_NOT_EXISTS);
        }

        // 第四步：生成唯一菜单编码
        entity.setMenuCode(generateUniqueMenuCode());
        entity.setSettings(JsonUtils.toJsonString(request.getSettings()));

        // 第五步：数据持久化
        try {
            oaMenuMapper.insert(entity);
            log.info("菜单创建成功，菜单ID：{}，菜单名称：{}", entity.getId(), entity.getMenuName());
            return entity.getId();
        } catch (Exception e) {
            log.error("创建菜单失败：数据库操作异常", e);
            throw exception(MENU_NOT_EXISTS);
        }
    }

    /**
     * 更新菜单
     * <p>
     * 业务逻辑：
     * 1. 参数校验：检查请求对象和菜单ID是否为空
     * 2. 存在性校验：检查要更新的菜单是否存在
     * 3. 父菜单校验：如果修改了父菜单，校验新父菜单是否存在
     * 4. 循环引用校验：防止将菜单设置为自己的子菜单的父菜单
     * 5. 数据更新：执行数据库更新操作
     * <p>
     * 性能优化：
     * - 先查询再更新，确保数据一致性
     * - 使用主键更新，性能最优
     *
     * @param request 更新菜单请求对象
     * @throws com.miaowen.oa.framework.common.exception.ServiceException 当参数无效或更新失败时抛出
     */
    public void updateMenu(OaMenuUpdateRequest request) {
        // 第一步：参数校验
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            log.error("更新菜单失败：请求参数或菜单ID为空");
            throw exception(MENU_NOT_EXISTS);
        }

        // 第二步：检查菜单是否存在
        OaMenuEntity existingEntity = oaMenuMapper.selectById(request.getId());
        if (existingEntity == null) {
            log.error("更新菜单失败：菜单不存在，菜单ID：{}", request.getId());
            throw exception(MENU_NOT_EXISTS);
        }

        // 第三步：校验父菜单（如果修改了父菜单）
        if (Objects.nonNull(request.getParentId()) && !Objects.equals(0L, request.getParentId())) {
            // 防止将菜单设置为自己的子菜单的父菜单
            validateNotCircularReference(request.getId(), request.getParentId());
            // 校验父菜单是否存在
            validateParentMenuExists(request.getParentId());
        }

        // 第四步：数据转换和更新
        OaMenuEntity entity = BeanUtils.toBean(request, OaMenuEntity.class);
        if (Objects.isNull(entity)) {
            log.error("更新菜单失败：数据转换失败，菜单ID：{}", request.getId());
            throw exception(MENU_NOT_EXISTS);
        }

        // 第五步：执行更新操作
        try {
            oaMenuMapper.updateById(entity);
            log.info("菜单更新成功，菜单ID：{}，菜单名称：{}", entity.getId(), entity.getMenuName());
        } catch (Exception e) {
            log.error("更新菜单失败：数据库操作异常，菜单ID：{}", request.getId(), e);
            throw exception(MENU_NOT_EXISTS);
        }
    }

    /**
     * 删除菜单
     * <p>
     * 业务逻辑：
     * 1. 参数校验：检查菜单ID是否为空
     * 2. 存在性校验：检查要删除的菜单是否存在
     * 3. 子菜单校验：检查是否存在子菜单，如果有则不允许删除
     * 4. 关联数据清理：删除菜单与角色的关联关系
     * 5. 菜单删除：执行逻辑删除操作
     * <p>
     * 性能优化：
     * - 使用批量删除清理关联数据
     * - 逻辑删除避免物理删除的性能开销
     * <p>
     * 安全措施：
     * - 级联删除关联数据，保证数据一致性
     * - 事务保证操作原子性
     *
     * @param id 要删除的菜单ID
     * @throws com.miaowen.oa.framework.common.exception.ServiceException 当参数无效或删除失败时抛出
     */
    public void deleteMenu(Long id) {
        // 第一步：参数校验
        if (Objects.isNull(id)) {
            log.error("删除菜单失败：菜单ID为空");
            throw exception(MENU_NOT_EXISTS);
        }

        // 第二步：检查菜单是否存在
        OaMenuEntity existingMenu = oaMenuMapper.selectById(id);
        if (Objects.isNull(existingMenu)) {
            log.error("删除菜单失败：菜单不存在，菜单ID：{}", id);
            throw exception(MENU_NOT_EXISTS);
        }


        try {

            // 第三步：递归获取所有子菜单ID（包括自身）
            List<Long> allMenuIds = new ArrayList<>();
            allMenuIds.add(id); // 包括自身
            collectAllChildMenuIds(id, allMenuIds);

            // 第四步：删除菜单和角色的关系数据
            LambdaQueryWrapper<OaMenuRoleRelationEntity> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(OaMenuRoleRelationEntity::getMenuId, id);
            oaMenuRoleRelationMapper.logicDelete(deleteWrapper);

            // 第无步：删除菜单数据（逻辑删除）
            oaMenuMapper.logicDeleteById(id);

            log.info("菜单删除成功，菜单ID：{}，菜单名称：{}", id, existingMenu.getMenuName());
        } catch (Exception e) {
            log.error("删除菜单失败：数据库操作异常，菜单ID：{}", id, e);
            throw exception(MENU_NOT_EXISTS);
        }
    }

    /**
     * 递归收集所有子菜单ID
     * @param parentId 父菜单ID
     * @param menuIds 存储所有菜单ID的集合（包括自身）
     */
    private void collectAllChildMenuIds(Long parentId, List<Long> menuIds) {
        // 查询当前菜单的所有直接子菜单
        LambdaQueryWrapper<OaMenuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaMenuEntity::getParentId, parentId)
                .eq(OaMenuEntity::getDeleteTime, 0); // 只考虑未删除的菜单

        List<OaMenuEntity> children = oaMenuMapper.selectList(wrapper);

        if (!CollectionUtils.isEmpty(children)) {
            for (OaMenuEntity child : children) {
                Long childId = child.getId();
                menuIds.add(childId); // 添加子菜单ID
                collectAllChildMenuIds(childId, menuIds); // 递归收集孙子菜单
            }
        }
    }

    public OaMenuDetailResp getMenuDetail(Long id) {
        if (Objects.isNull(id)) {
            throw exception(MENU_NOT_EXISTS);
        }
        LambdaQueryWrapper<OaMenuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaMenuEntity::getId, id).eq(OaMenuEntity::getDeleteTime, 0);
        OaMenuEntity entity = oaMenuMapper.selectOne(wrapper);
        if (entity == null) {
            throw exception(MENU_NOT_EXISTS);
        }

        OaMenuDetailResp resp = BeanUtils.toBean(entity, OaMenuDetailResp.class);
        if (Objects.isNull(resp)) {
            throw exception(MENU_NOT_EXISTS);
        }
        resp.setSettings(JsonUtils.parseArray(entity.getSettings(), String.class));
        return resp;
    }

    public List<OaMenuRoleRelationEntity> getRelationsByRoleId(Long roleId) {
        LambdaQueryWrapper<OaMenuRoleRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaMenuRoleRelationEntity::getRoleId, roleId).eq(OaMenuRoleRelationEntity::getDeleteTime, 0);

        return oaMenuRoleRelationMapper.selectList(wrapper);
    }

    public List<OaMenuRoleRelationEntity> getRelationsByMenuId(Long menuId) {
        LambdaQueryWrapper<OaMenuRoleRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaMenuRoleRelationEntity::getMenuId, menuId).eq(OaMenuRoleRelationEntity::getDeleteTime, 0);

        return oaMenuRoleRelationMapper.selectList(wrapper);
    }

    public List<Long> getRoleIdsByMenuId(Long menuId) {
        LambdaQueryWrapper<OaMenuRoleRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OaMenuRoleRelationEntity::getRoleId).eq(OaMenuRoleRelationEntity::getMenuId, menuId).eq(OaMenuRoleRelationEntity::getDeleteTime, 0);

        return oaMenuRoleRelationMapper.selectList(wrapper).stream().map(OaMenuRoleRelationEntity::getRoleId).collect(Collectors.toList());
    }

    public List<Long> getMenuIdsByRoleId(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OaMenuRoleRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OaMenuRoleRelationEntity::getMenuId).in(!CollectionUtils.isEmpty(roleIds), OaMenuRoleRelationEntity::getRoleId, roleIds).eq(OaMenuRoleRelationEntity::getDeleteTime, 0);

        return oaMenuRoleRelationMapper.selectList(wrapper).stream().map(OaMenuRoleRelationEntity::getMenuId).collect(Collectors.toList());
    }


    /**
     * 菜单查询方法
     *
     * 业务逻辑：
     * 1. 菜单名称搜索：模糊匹配菜单名称，仅返回匹配节点 + 其父节点链 + 其子节点，不包含同级节点
     * 2. 菜单类型搜索：精确匹配菜单类型，仅返回匹配节点本身，构建平铺树结构
     * 3. 状态搜索：精确匹配状态，返回匹配节点 + 完整父节点链（但不包含子节点）
     * 4. 多条件搜索：支持多个条件同时搜索，取交集结果
     * 5. 无参查询：返回所有菜单的完整树形结构，包含parentId=0的根节点
     *
     * 搜索场景示例：
     * 场景1：menuName="用户" -> 返回包含"用户"的菜单节点 + 其父节点链 + 其所有子节点
     * 场景2：menuTypeList=["1"] -> 返回所有类型为"1"的菜单，平铺结构，无父子关系
     * 场景3：state=1 -> 返回所有状态为1的菜单 + 其父节点链，不包含子节点
     * 场景4：menuName="用户" + state=1 -> 返回同时满足名称包含"用户"且状态为1的菜单 + 父节点链 + 子节点
     * 场景5：menuTypeList=["1"] + state=1 -> 返回类型为"1"且状态为1的菜单 + 父节点链，不包含子节点
     * 场景6：无条件 -> 返回完整菜单树
     *
     * 性能优化：
     * - 一次性查询所有菜单，避免多次数据库访问
     * - 使用Set进行节点去重，提升查找性能
     * - 根据搜索条件采用不同的节点收集策略
     *
     * @param request 菜单查询请求
     * @return 菜单树形结构列表
     */
    public List<OaMenuPageResp> queryMenus(OaMenuPageRequest request) {
        try {
            // 第一步：查询所有有效菜单
            List<OaMenuEntity> allMenus = getAllValidMenus();
            if (CollectionUtils.isEmpty(allMenus)) {
                log.info("查询菜单结果为空");
                return new ArrayList<>();
            }

            // 第二步：检查是否有搜索条件
            boolean hasMenuName = StringUtils.hasText(request.getMenuName());
            boolean hasMenuType = !CollectionUtils.isEmpty(request.getMenuTypeList());
            boolean hasState = Objects.nonNull(request.getState());

            // 第三步：根据搜索条件确定处理策略
            if (!hasMenuName && !hasMenuType && !hasState) {
                // 无参查询策略：返回完整树形结构
                return handleNoConditionSearch(allMenus);
            } else if (hasMenuType) {
                // 包含菜单类型搜索：返回平铺结构（无论是否有其他条件）
                return handleMenuTypeSearch(request, allMenus);
            } else {
                // 其他单条件或多条件搜索（不包含菜单类型）：返回树形结构
                return handleMultiConditionSearch(request, allMenus);
            }

        } catch (Exception e) {
            log.error("菜单查询失败", e);
            return new ArrayList<>();
        }
    }


    // ==================== 菜单查询策略方法 ====================

    /**
     * 获取所有有效菜单
     *
     * SQL优化：
     * - 使用索引字段进行查询
     * - 按排序字段排序便于后续处理
     *
     * @return 所有有效菜单列表
     */
    private List<OaMenuEntity> getAllValidMenus() {
        LambdaQueryWrapper<OaMenuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaMenuEntity::getDeleteTime, 0)
                .orderByAsc(OaMenuEntity::getSort)
                .orderByAsc(OaMenuEntity::getId);
        return oaMenuMapper.selectList(wrapper);
    }

    /**
     * 处理多条件搜索（包括单条件的菜单名称、状态搜索，以及多条件组合搜索）
     *
     * 业务逻辑：
     * - 菜单名称：模糊匹配，仅返回匹配节点 + 其父节点链 + 其子节点，不包含同级节点
     * - 菜单状态：精确匹配，返回匹配节点 + 父节点链（不包含子节点）
     * - 多条件：取各条件的交集，然后应用相应的节点收集策略
     *
     * @param request 查询请求
     * @param allMenus 所有菜单
     * @return 树形结构菜单列表
     */
    private List<OaMenuPageResp> handleMultiConditionSearch(OaMenuPageRequest request, List<OaMenuEntity> allMenus) {
        // 第一步：根据各个条件过滤菜单，取交集
        List<OaMenuEntity> filteredMenus = allMenus.stream()
                .filter(menu -> matchesAllConditions(menu, request))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredMenus)) {
            return new ArrayList<>();
        }

        // 第二步：收集需要展示的节点ID
        Set<Long> nodeIds = new HashSet<>();

        // 收集匹配节点及其父节点链
        for (OaMenuEntity menu : filteredMenus) {
            collectParentChain(nodeIds, menu, allMenus);
        }

        // 第三步：根据搜索条件决定是否包含子节点
        boolean hasMenuName = StringUtils.hasText(request.getMenuName());
        if (hasMenuName) {
            // 如果包含菜单名称搜索，则收集所有子节点
            Set<Long> matchedNodeIds = filteredMenus.stream()
                    .map(OaMenuEntity::getId)
                    .collect(Collectors.toSet());
            collectAllChildren(nodeIds, matchedNodeIds, allMenus);
        }
        // 如果只是状态搜索，则不包含子节点（只有父节点链）

        // 第四步：构建结果列表并生成树形结构
        List<OaMenuEntity> resultMenus = allMenus.stream()
                .filter(menu -> nodeIds.contains(menu.getId()))
                .collect(Collectors.toList());

        return buildMenuTree(resultMenus, 0L);
    }

    /**
     * 检查菜单是否匹配所有搜索条件
     *
     * @param menu 菜单实体
     * @param request 查询请求
     * @return 是否匹配所有条件
     */
    private boolean matchesAllConditions(OaMenuEntity menu, OaMenuPageRequest request) {
        // 菜单名称条件（模糊匹配）
        if (StringUtils.hasText(request.getMenuName())) {
            if (Objects.isNull(menu.getMenuName()) ||
                !menu.getMenuName().contains(request.getMenuName())) {
                return false;
            }
        }

        // 菜单类型条件（精确匹配）
        if (!CollectionUtils.isEmpty(request.getMenuTypeList())) {
            if (Objects.isNull(menu.getMenuType()) ||
                !request.getMenuTypeList().contains(menu.getMenuType())) {
                return false;
            }
        }

        // 状态条件（精确匹配）
        if (Objects.nonNull(request.getState())) {
            if (Objects.isNull(menu.getState()) ||
                !request.getState().equals(menu.getState())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 处理菜单类型搜索
     *
     * 业务逻辑：
     * - 精确匹配菜单类型
     * - 仅返回匹配节点本身
     * - 不包含父节点和子节点
     * - 构建平铺树结构（每个节点作为独立根节点）
     *
     * @param request 查询请求
     * @param allMenus 所有菜单
     * @return 平铺结构菜单列表
     */
    private List<OaMenuPageResp> handleMenuTypeSearch(OaMenuPageRequest request, List<OaMenuEntity> allMenus) {
        // 精确匹配菜单类型
        List<OaMenuEntity> matchedMenus = allMenus.stream()
                .filter(menu -> Objects.nonNull(menu.getMenuType()) &&
                               request.getMenuTypeList().contains(menu.getMenuType()))
                .toList();

        //模糊匹配菜单名称
        if (StringUtils.hasText(request.getMenuName())){
            matchedMenus = matchedMenus.stream()
                    .filter(menu -> StringUtils.hasText(menu.getMenuName()) &&
                            menu.getMenuName().contains(request.getMenuName()))
                    .toList();
        }


        //精准匹配状态
        if (Objects.nonNull(request.getState())) {
           matchedMenus = matchedMenus.stream()
                   .filter(menu -> Objects.nonNull(menu.getState()) &&
                           Objects.equals(menu.getState(), request.getState()))
                   .toList();
       }

        // 转换为平铺结构（每个节点作为独立根节点，无子节点）
        return matchedMenus.stream()
                .map(this::convertToResp)
                // 清空子节点确保平铺结构
                .peek(resp -> resp.setChildren(new ArrayList<>()))
                .sorted(Comparator.comparingInt(OaMenuPageResp::getSort))
                .collect(Collectors.toList());
    }



    /**
     * 处理无条件搜索
     *
     * 业务逻辑：
     * - 返回所有菜单的完整树形结构
     * - 包含parentId=0的根节点
     *
     * @param allMenus 所有菜单
     * @return 完整树形结构菜单列表
     */
    private List<OaMenuPageResp> handleNoConditionSearch(List<OaMenuEntity> allMenus) {
        // 返回完整的菜单树形结构，从parentId=0开始
        return buildMenuTree(allMenus, 0L);
    }

    // ==================== 辅助工具方法 ====================

    /**
     * 收集父节点链（递归向上直到根节点）
     *
     * 业务逻辑：
     * - 从当前节点开始，递归向上查找父节点
     * - 直到找到根节点（parentId=0或null）
     * - 避免循环引用
     *
     * 性能优化：
     * - 使用Set避免重复添加
     * - 循环检测防止无限递归
     *
     * @param nodeIds 节点ID集合
     * @param menu 起始菜单节点
     * @param allMenus 所有菜单列表
     */
    private void collectParentChain(Set<Long> nodeIds, OaMenuEntity menu, List<OaMenuEntity> allMenus) {
        if (Objects.isNull(menu) || Objects.isNull(menu.getId())) {
            return;
        }

        Long currentId = menu.getId();
        Set<Long> visited = new HashSet<>(); // 防止循环引用

        while (Objects.nonNull(currentId) && !Objects.equals(0L, currentId)) {
            // 循环检测
            if (visited.contains(currentId)) {
                log.warn("检测到菜单循环引用，菜单ID：{}", currentId);
                break;
            }
            visited.add(currentId);

            // 添加当前节点
            nodeIds.add(currentId);

            // 查找父节点
            final Long finalCurrentId = currentId;
            OaMenuEntity current = allMenus.stream()
                    .filter(Objects::nonNull)
                    .filter(m -> Objects.equals(m.getId(), finalCurrentId))
                    .findFirst()
                    .orElse(null);

            // 移动到父节点
            currentId = (Objects.nonNull(current)) ? current.getParentId() : null;
        }
    }

    /**
     * 收集指定节点的所有子节点（BFS广度优先遍历）
     *
     * 业务逻辑：
     * - 从给定的父节点集合开始
     * - 使用BFS遍历所有子节点（递归到叶子节点）
     * - 避免重复添加节点
     * - 只收集子节点，不包含同级节点
     *
     * 性能优化：
     * - 使用队列进行BFS遍历
     * - 使用Set避免重复处理
     *
     * @param nodeIds 节点ID集合（会被修改，用于存储结果）
     * @param parentIds 要收集子节点的父节点ID集合
     * @param allMenus 所有菜单列表
     */
    private void collectAllChildren(Set<Long> nodeIds, Set<Long> parentIds, List<OaMenuEntity> allMenus) {
        if (CollectionUtils.isEmpty(parentIds) || CollectionUtils.isEmpty(allMenus)) {
            return;
        }

        Queue<Long> queue = new LinkedList<>(parentIds);
        // 防止重复处理
        Set<Long> processed = new HashSet<>();

        while (!queue.isEmpty()) {
            Long parentId = queue.poll();

            if (Objects.isNull(parentId) || processed.contains(parentId)) {
                continue;
            }
            processed.add(parentId);

            // 查找当前父节点的直接子节点
            List<OaMenuEntity> directChildren = allMenus.stream()
                    .filter(Objects::nonNull)
                    .filter(menu -> Objects.equals(parentId, menu.getParentId()))
                    .toList();

            // 将子节点添加到结果集，并继续查找子节点的子节点
            for (OaMenuEntity child : directChildren) {
                if (Objects.nonNull(child.getId()) && nodeIds.add(child.getId())) {
                    queue.offer(child.getId()); // 继续查找子节点的子节点
                }
            }
        }
    }


    /**
     * 转换实体为分页响应对象
     */
    private OaMenuPageResp convertToPageResp(OaMenuEntity entity) {
        OaMenuPageResp resp = BeanUtils.toBean(entity, OaMenuPageResp.class);

        // 如果需要，可以在这里添加额外的转换逻辑
        // 例如：resp.setHasChildren(checkHasChildren(entity.getId()));

        return resp;
    }

    public List<OaMenuPageResp> getMenuTree(Long roleId) {
        // 获取所有菜单（包括逻辑删除过滤）
        LambdaQueryWrapper<OaMenuEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaMenuEntity::getDeleteTime, 0);

        // 如果指定了角色ID，只查询该角色关联的菜单
        List<Long> menuIds = getMenuIdsByRoleId(Collections.singletonList(roleId));
        if (CollectionUtils.isEmpty(menuIds)) {
            return new ArrayList<>();
        }
        queryWrapper.in(OaMenuEntity::getId, menuIds);

        queryWrapper.orderByAsc(OaMenuEntity::getSort);
        List<OaMenuEntity> allMenus = oaMenuMapper.selectList(queryWrapper);

        // 构建树形结构
        return buildMenuTree(allMenus, 0L);
    }

    /**
     * 获取用户菜单树
     * <p>
     * 业务逻辑：
     * 1. 参数校验：检查用户ID是否为空
     * 2. 角色查询：获取用户关联的所有角色ID
     * 3. 菜单权限查询：根据角色ID查询用户有权限的菜单
     * 4. 菜单详情查询：批量查询菜单详细信息
     * 5. 树形结构构建：将平铺的菜单列表构建成树形结构
     * 6. 排序处理：按照菜单排序字段进行排序
     * <p>
     * 性能优化：
     * - 使用IN查询批量获取数据，减少数据库访问次数
     * - 菜单树构建使用递归算法，时间复杂度O(n)
     * - 结果缓存可在上层实现
     * <p>
     * 安全措施：
     * - 基于角色的权限控制，确保用户只能看到有权限的菜单
     * - 过滤无效菜单数据
     *
     * @param userId 用户ID
     * @return 用户有权限的菜单树列表
     * @throws com.miaowen.oa.framework.common.exception.ServiceException 当用户不存在时抛出
     */
    public List<OaMenuDetailResp> getUserMenuTree(Long userId) {
        // 第一步：参数校验
        if (Objects.isNull(userId)) {
            log.error("获取用户菜单树失败：用户ID为空");
            throw exception(USER_NOT_EXISTS);
        }

        // 第二步：获取用户关联的角色ID列表
        List<Long> roleIds = getUserRoleIds(userId);
        if (CollectionUtils.isEmpty(roleIds)) {
            log.info("用户无关联角色，返回空菜单树，用户ID：{}", userId);
            return new ArrayList<>();
        }

        // 2. 获取这些角色关联的菜单ID（去重）
        Set<Long> menuIdSet = new HashSet<>();
        List<Long> menuIds = getMenuIdsByRoleId(roleIds);
        if (!CollectionUtils.isEmpty(menuIds)) {
            menuIdSet.addAll(menuIds);
        }


        if (menuIdSet.isEmpty()) {
            return new ArrayList<>();
        }

        // 3. 查询菜单实体（只查询启用的菜单）
        LambdaQueryWrapper<OaMenuEntity> menuQuery = new LambdaQueryWrapper<>();
        menuQuery.in(CollectionUtils.isNotEmpty(menuIdSet), OaMenuEntity::getId, menuIdSet)
                // 只查询状态为启用的菜单
                .eq(OaMenuEntity::getState, 1).eq(OaMenuEntity::getDeleteTime, 0).orderByAsc(OaMenuEntity::getSort);

        List<OaMenuEntity> menus = oaMenuMapper.selectList(menuQuery);

        // 4. 构建树形结构
        return buildDetailMenuTree(menus, 0L);
    }


    /**
     * 构建详细菜单树
     */
    private List<OaMenuDetailResp> buildDetailMenuTree(List<OaMenuEntity> menus, Long parentId) {
        return menus.stream().filter(menu -> parentId.equals(menu.getParentId())).map(menu -> {
            OaMenuDetailResp resp = BeanUtils.toBean(menu, OaMenuDetailResp.class);
            resp.setChildren(buildDetailMenuTree(menus, menu.getId()));
            return resp;
        }).sorted(Comparator.comparingInt(OaMenuDetailResp::getSort)).collect(Collectors.toList());
    }


    /**
     * 获取用户关联的角色ID列表
     */
    private List<Long> getUserRoleIds(Long userId) {
        // 实际项目中应根据业务实现
        // 这里示例：假设有用户角色关系表查询
        return oaUserRoleRelationMapper.findByUserIds(Collections.singletonList(userId)).stream().map(OaUserRoleRelationEntity::getRoleId).toList();
    }

    private OaMenuPageResp convertToResp(OaMenuEntity entity) {
        OaMenuPageResp resp = BeanUtils.toBean(entity, OaMenuPageResp.class);
        return resp;
    }

    private List<OaMenuPageResp> buildMenuTree(List<OaMenuEntity> menus, Long parentId) {
        return menus.stream().filter(menu -> parentId.equals(menu.getParentId())).map(menu -> {
            OaMenuPageResp resp = convertToResp(menu);
            resp.setChildren(buildMenuTree(menus, menu.getId()));
            return resp;
        }).sorted(Comparator.comparingInt(OaMenuPageResp::getSort)).collect(Collectors.toList());
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 生成唯一的菜单编码
     * <p>
     * 性能优化：
     * - 使用UUID避免数据库查询检查重复
     * - 添加时间戳前缀便于排序和调试
     *
     * @return 唯一的菜单编码
     */
    private String generateUniqueMenuCode() {
        return "MENU_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    /**
     * 校验父菜单是否存在
     * <p>
     * SQL优化：
     * - 使用主键查询，性能最优
     * - 只查询必要字段减少网络传输
     *
     * @param parentId 父菜单ID
     * @throws com.miaowen.oa.framework.common.exception.ServiceException 当父菜单不存在时抛出
     */
    private void validateParentMenuExists(Long parentId) {
        if (Objects.isNull(parentId) || Objects.equals(0L, parentId)) {
            return;
        }

        OaMenuEntity parentMenu = oaMenuMapper.selectById(parentId);
        if (Objects.isNull(parentMenu)) {
            log.error("父菜单不存在，父菜单ID：{}", parentId);
            throw exception(MENU_NOT_EXISTS);
        }
    }

    /**
     * 校验菜单循环引用
     * <p>
     * 业务逻辑：
     * - 防止将菜单A设置为菜单A的子菜单的父菜单
     * - 递归检查所有子菜单路径
     *
     * @param menuId      当前菜单ID
     * @param newParentId 新的父菜单ID
     * @throws com.miaowen.oa.framework.common.exception.ServiceException 当存在循环引用时抛出
     */
    private void validateNotCircularReference(Long menuId, Long newParentId) {
        if (Objects.equals(menuId, newParentId)) {
            log.error("不能将菜单设置为自己的父菜单，菜单ID：{}", menuId);
            throw exception(MENU_NOT_EXISTS);
        }

        // 递归检查新父菜单的所有子菜单
        List<Long> childMenuIds = getChildMenuIds(menuId);
        if (childMenuIds.contains(newParentId)) {
            log.error("不能将菜单设置为自己子菜单的父菜单，菜单ID：{}，新父菜单ID：{}", menuId, newParentId);
            throw exception(MENU_NOT_EXISTS);
        }
    }

    /**
     * 获取菜单的所有子菜单ID（递归）
     * <p>
     * SQL优化：
     * - 使用单次查询获取所有子菜单
     * - 递归在内存中进行，减少数据库访问
     *
     * @param parentId 父菜单ID
     * @return 所有子菜单ID列表
     */
    private List<Long> getChildMenuIds(Long parentId) {
        List<Long> result = new ArrayList<>();

        LambdaQueryWrapper<OaMenuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaMenuEntity::getParentId, parentId).eq(OaMenuEntity::getDeleteTime, 0).select(OaMenuEntity::getId, OaMenuEntity::getParentId);

        List<OaMenuEntity> childMenus = oaMenuMapper.selectList(wrapper);
        for (OaMenuEntity child : childMenus) {
            result.add(child.getId());
            // 递归获取子菜单的子菜单
            result.addAll(getChildMenuIds(child.getId()));
        }

        return result;
    }

    /**
     * 校验菜单下是否存在子菜单
     * <p>
     * SQL优化：
     * - 使用COUNT查询，只返回数量不返回数据
     * - 添加索引提升查询性能
     *
     * @param menuId 菜单ID
     * @throws com.miaowen.oa.framework.common.exception.ServiceException 当存在子菜单时抛出
     */
    private void validateNoChildMenus(Long menuId) {
        LambdaQueryWrapper<OaMenuEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaMenuEntity::getParentId, menuId).eq(OaMenuEntity::getDeleteTime, 0);

        Long count = oaMenuMapper.selectCount(wrapper);
        if (count > 0) {
            log.error("菜单下存在子菜单，无法删除，菜单ID：{}，子菜单数量：{}", menuId, count);
            throw exception(MENU_NOT_EXISTS);
        }
    }

    /**
     * 获得权限对应的菜单编号数组
     * @param permission
     * @return
     */
    @Cacheable(value = RedisKeyConstants.PERMISSION_MENU_ID_LIST, key = "#permission")
    public List<Long> getMenuIdListByPermissionFromCache(String permission) {
        List<OaMenuEntity> menus = oaMenuMapper.selectList(Wrappers.lambdaQuery(OaMenuEntity.class).eq(OaMenuEntity::getAuthoritySign, permission).eq(OaMenuEntity::getDeleteTime, 0));
        return convertList(menus, OaMenuEntity::getId);
    }
}


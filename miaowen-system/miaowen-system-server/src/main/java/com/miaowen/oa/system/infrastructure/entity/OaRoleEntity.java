package com.miaowen.oa.system.infrastructure.entity;



import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value ="oa_role")
public class OaRoleEntity  extends BaseDO {

    /**
     * 角色分组id
     */
    private Long roleGroupId;


    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 是否是超级管理员：0-否，1-是
     */
    private Integer isSuperAdmin = 0;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态:0正常 1禁用
     */
    private Integer state;

}

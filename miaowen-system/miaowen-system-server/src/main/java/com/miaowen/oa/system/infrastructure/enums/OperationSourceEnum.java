package com.miaowen.oa.system.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作来源枚举
 * 
 * 功能说明：
 * 1. 定义操作的来源渠道
 * 2. 用于区分不同的访问方式
 * 3. 支持多端统计分析
 * 4. 便于渠道管理
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Getter
@AllArgsConstructor
public enum OperationSourceEnum {

    /**
     * Web端
     */
    WEB(1, "Web端", "web", "通过浏览器访问"),

    /**
     * 移动端
     */
    MOBILE(2, "移动端", "mobile", "通过移动应用访问"),

    /**
     * API调用
     */
    API(3, "API调用", "api", "通过API接口调用"),

    /**
     * 定时任务
     */
    SCHEDULE(4, "定时任务", "schedule", "系统定时任务执行"),

    /**
     * 系统内部
     */
    SYSTEM(5, "系统内部", "system", "系统内部调用");

    /**
     * 来源码
     */
    private final Integer code;

    /**
     * 来源名称
     */
    private final String name;

    /**
     * 来源英文名
     */
    private final String englishName;

    /**
     * 来源描述
     */
    private final String description;

    /**
     * 根据来源码获取枚举
     * 
     * @param code 来源码
     * @return 操作来源枚举
     */
    public static OperationSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OperationSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return null;
    }

    /**
     * 根据来源码获取名称
     * 
     * @param code 来源码
     * @return 来源名称
     */
    public static String getNameByCode(Integer code) {
        OperationSourceEnum source = getByCode(code);
        return source != null ? source.getName() : "未知";
    }

    /**
     * 根据来源码获取英文名
     * 
     * @param code 来源码
     * @return 英文名
     */
    public static String getEnglishNameByCode(Integer code) {
        OperationSourceEnum source = getByCode(code);
        return source != null ? source.getEnglishName() : "unknown";
    }

    /**
     * 根据来源码获取描述
     * 
     * @param code 来源码
     * @return 来源描述
     */
    public static String getDescriptionByCode(Integer code) {
        OperationSourceEnum source = getByCode(code);
        return source != null ? source.getDescription() : "未知来源";
    }

    /**
     * 判断是否为用户操作
     * 
     * @param code 来源码
     * @return 是否为用户操作
     */
    public static boolean isUserOperation(Integer code) {
        return WEB.getCode().equals(code) || MOBILE.getCode().equals(code) || API.getCode().equals(code);
    }

    /**
     * 判断是否为系统操作
     * 
     * @param code 来源码
     * @return 是否为系统操作
     */
    public static boolean isSystemOperation(Integer code) {
        return SCHEDULE.getCode().equals(code) || SYSTEM.getCode().equals(code);
    }

    /**
     * 根据User-Agent推断操作来源
     * 
     * @param userAgent 用户代理字符串
     * @return 操作来源码
     */
    public static Integer inferSourceFromUserAgent(String userAgent) {
        if (userAgent == null || userAgent.trim().isEmpty()) {
            return SYSTEM.getCode();
        }
        
        String lowerUserAgent = userAgent.toLowerCase();
        
        // 移动端判断
        if (lowerUserAgent.contains("mobile") || 
            lowerUserAgent.contains("android") || 
            lowerUserAgent.contains("iphone") || 
            lowerUserAgent.contains("ipad")) {
            return MOBILE.getCode();
        }
        
        // API调用判断
        if (lowerUserAgent.contains("okhttp") || 
            lowerUserAgent.contains("apache-httpclient") || 
            lowerUserAgent.contains("java") ||
            lowerUserAgent.contains("python") ||
            lowerUserAgent.contains("curl") ||
            lowerUserAgent.contains("postman")) {
            return API.getCode();
        }
        
        // 浏览器判断
        if (lowerUserAgent.contains("mozilla") || 
            lowerUserAgent.contains("chrome") || 
            lowerUserAgent.contains("safari") || 
            lowerUserAgent.contains("firefox") ||
            lowerUserAgent.contains("edge")) {
            return WEB.getCode();
        }
        
        // 默认为Web端
        return WEB.getCode();
    }
}

package com.miaowen.oa.system.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.miaowen.oa.system.domain.service.OperationLogService;
import com.miaowen.oa.system.infrastructure.entity.OaOperationLogEntity;
import com.miaowen.oa.system.infrastructure.mapper.OaOperationLogMapper;
import com.miaowen.oa.system.infrastructure.utils.OperationLogUtil;
import com.miaowen.oa.system.interfaces.req.log.OperationLogPageRequest;
import com.miaowen.oa.system.interfaces.res.log.OperationLogPageResp;
import com.miaowen.oa.system.interfaces.res.log.OperationLogStatisticsResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 操作日志服务实现类
 * 
 * 功能说明：
 * 1. 实现操作日志的CRUD操作
 * 2. 提供分页查询和条件筛选
 * 3. 支持统计分析功能
 * 4. 异步处理提升性能
 * 5. 数据清理和归档功能
 * 6. 遵循时间局部性和空间局部性原理
 * 
 * 性能优化：
 * - 时间局部性：最近的数据更可能被访问，优先查询最新数据
 * - 空间局部性：相关数据聚合查询，减少数据库访问次数
 * - 异步处理：日志记录不阻塞主业务流程
 * - 分页查询：避免大数据量查询影响性能
 * - 索引优化：利用数据库索引提升查询效率
 * 
 * 设计原则：
 * - 高性能：异步处理，批量操作
 * - 高可用：异常处理，降级策略
 * - 易扩展：模块化设计，便于功能扩展
 * - 数据安全：敏感数据脱敏，防止泄露
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Service
public class OperationLogServiceImpl extends ServiceImpl<OaOperationLogMapper, OaOperationLogEntity> implements OperationLogService {

    @Resource
    private OaOperationLogMapper operationLogMapper;

    // 常量定义
    private static final Integer DEFAULT_RETENTION_DAYS = 90;
    private static final Integer MAX_EXPORT_LIMIT = 10000;
    private static final Integer DEFAULT_PAGE_SIZE = 20;

    @Override
    public PageResult<OperationLogPageResp> pageOperationLog(OperationLogPageRequest request) {
        try {
            // 第一步：参数校验和默认值设置
            if (request == null) {
                return PageResult.empty();
            }
            
            // 设置默认分页参数
            if (request.getPage() == null || request.getPage() <= 0) {
                request.setPage(1);
            }
            if (request.getPageSize() == null || request.getPageSize() <= 0) {
                request.setPageSize(DEFAULT_PAGE_SIZE);
            }
            
            // 第二步：构建分页对象
            Page<OaOperationLogEntity> page = new Page<>(request.getPage(), request.getPageSize());
            
            // 第三步：构建查询条件（时间局部性优化：按访问频率排序条件）
            LambdaQueryWrapper<OaOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            
            // 基础条件：未删除（最常用的条件）
            queryWrapper.eq(OaOperationLogEntity::getDeleteTime, 0);
            
            // 时间范围条件（高频使用，优先判断）
            if (request.getStartTime() != null) {
                queryWrapper.ge(OaOperationLogEntity::getCreateTime, request.getStartTime());
            }
            if (request.getEndTime() != null) {
                queryWrapper.le(OaOperationLogEntity::getCreateTime, request.getEndTime());
            }
            
            // 用户条件（高频使用）
            if (request.getUserId() != null) {
                queryWrapper.eq(OaOperationLogEntity::getUserId, request.getUserId());
            }
            
            // 操作类型条件
            if (request.getOperationType() != null) {
                queryWrapper.eq(OaOperationLogEntity::getOperationType, request.getOperationType());
            }
            
            // 操作模块条件
            if (StringUtils.hasText(request.getOperationModule())) {
                queryWrapper.eq(OaOperationLogEntity::getOperationModule, request.getOperationModule());
            }
            
            // 业务类型条件
            if (StringUtils.hasText(request.getBusinessType())) {
                queryWrapper.eq(OaOperationLogEntity::getBusinessType, request.getBusinessType());
            }
            
            // 操作状态条件
            if (request.getOperationState() != null) {
                queryWrapper.eq(OaOperationLogEntity::getOperationState, request.getOperationState());
            }
            
            // 风险等级条件
            if (request.getRiskLevel() != null) {
                queryWrapper.ge(OaOperationLogEntity::getRiskLevel, request.getRiskLevel());
            }
            
            // 关键字搜索（模糊查询，性能较低，放在最后）
            if (StringUtils.hasText(request.getKeyword())) {
                queryWrapper.and(wrapper -> wrapper
                    .like(OaOperationLogEntity::getOperationFunction, request.getKeyword())
                    .or()
                    .like(OaOperationLogEntity::getOperationDescription, request.getKeyword())
                    .or()
                    .like(OaOperationLogEntity::getUsername, request.getKeyword())
                );
            }
            
            // 排序：按创建时间倒序（时间局部性：最新的数据更可能被访问）
            queryWrapper.orderByDesc(OaOperationLogEntity::getCreateTime);
            
            // 第四步：执行分页查询
            IPage<OaOperationLogEntity> pageResult = operationLogMapper.selectPage(page, queryWrapper);
            
            // 第五步：转换结果
            List<OperationLogPageResp> respList = convertToPageResp(pageResult.getRecords());
            
            return new PageResult<>(respList, pageResult.getTotal());
            
        } catch (Exception e) {
            log.error("分页查询操作日志失败", e);
            return PageResult.empty();
        }
    }

    @Override
    public OperationLogPageResp getOperationLogById(Long id) {
        try {
            if (id == null) {
                return null;
            }
            
            // 查询条件：ID和未删除
            LambdaQueryWrapper<OaOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OaOperationLogEntity::getId, id)
                       .eq(OaOperationLogEntity::getDeleteTime, 0);
            
            OaOperationLogEntity entity = operationLogMapper.selectOne(queryWrapper);
            if (entity == null) {
                return null;
            }
            
            return convertToPageResp(entity);
            
        } catch (Exception e) {
            log.error("根据ID查询操作日志失败，ID：{}", id, e);
            return null;
        }
    }

    @Override
    public List<OperationLogPageResp> getUserRecentOperations(Long userId, Integer limit) {
        try {
            if (userId == null || limit == null || limit <= 0) {
                return new ArrayList<>();
            }
            
            // 限制查询数量，避免大数据量查询
            int queryLimit = Math.min(limit, 100);
            
            // 构建查询条件（时间局部性：查询最近的操作）
            LambdaQueryWrapper<OaOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                       .eq(OaOperationLogEntity::getUserId, userId)
                       .orderByDesc(OaOperationLogEntity::getCreateTime)
                       .last("LIMIT " + queryLimit);
            
            List<OaOperationLogEntity> entities = operationLogMapper.selectList(queryWrapper);
            return convertToPageResp(entities);
            
        } catch (Exception e) {
            log.error("查询用户最近操作记录失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<OperationLogPageResp> getBusinessOperationHistory(String businessType, Long businessId) {
        try {
            if (!StringUtils.hasText(businessType) || businessId == null) {
                return new ArrayList<>();
            }
            
            // 构建查询条件（空间局部性：查询相关业务数据的所有操作）
            LambdaQueryWrapper<OaOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                       .eq(OaOperationLogEntity::getBusinessType, businessType)
                       .eq(OaOperationLogEntity::getBusinessId, businessId)
                       .orderByDesc(OaOperationLogEntity::getCreateTime);
            
            List<OaOperationLogEntity> entities = operationLogMapper.selectList(queryWrapper);
            return convertToPageResp(entities);
            
        } catch (Exception e) {
            log.error("查询业务数据操作历史失败，业务类型：{}，业务ID：{}", businessType, businessId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<OperationLogPageResp> getHighRiskOperations(Integer riskLevel, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            if (riskLevel == null || riskLevel < 1) {
                riskLevel = 3; // 默认查询高风险操作
            }
            
            // 设置默认时间范围
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(7);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            // 构建查询条件
            LambdaQueryWrapper<OaOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                       .ge(OaOperationLogEntity::getRiskLevel, riskLevel)
                       .between(OaOperationLogEntity::getCreateTime, startTime, endTime)
                       .orderByDesc(OaOperationLogEntity::getCreateTime);
            
            List<OaOperationLogEntity> entities = operationLogMapper.selectList(queryWrapper);
            return convertToPageResp(entities);
            
        } catch (Exception e) {
            log.error("查询高风险操作记录失败，风险等级：{}", riskLevel, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Long countUserOperations(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            if (userId == null) {
                return 0L;
            }
            
            // 设置默认时间范围
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            // 构建查询条件
            LambdaQueryWrapper<OaOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                       .eq(OaOperationLogEntity::getUserId, userId)
                       .between(OaOperationLogEntity::getCreateTime, startTime, endTime);
            
            return operationLogMapper.selectCount(queryWrapper);
            
        } catch (Exception e) {
            log.error("统计用户操作次数失败，用户ID：{}", userId, e);
            return 0L;
        }
    }

    @Override
    @Async("operationLogExecutor")
    public CompletableFuture<Boolean> recordOperationLogAsync(OaOperationLogEntity operationLog) {
        try {
            if (operationLog == null) {
                return CompletableFuture.completedFuture(false);
            }
            
            // 数据脱敏处理
            maskSensitiveData(operationLog);
            
            // 设置默认值
            if (operationLog.getCreateTime() == null) {
                operationLog.setCreateTime(LocalDateTime.now());
            }
            if (operationLog.getUpdateTime() == null) {
                operationLog.setUpdateTime(LocalDateTime.now());
            }
            if (operationLog.getDataVersion() == null) {
                operationLog.setDataVersion(1);
            }
            
            // 插入数据库
            int result = operationLogMapper.insert(operationLog);
            
            log.debug("异步记录操作日志完成，结果：{}，描述：{}", result > 0, operationLog.getOperationDescription());
            
            return CompletableFuture.completedFuture(result > 0);
            
        } catch (Exception e) {
            log.error("异步记录操作日志失败", e);
            return CompletableFuture.completedFuture(false);
        }
    }

    @Override
    public Integer batchRecordOperationLogs(List<OaOperationLogEntity> operationLogs) {
        try {
            if (CollectionUtils.isEmpty(operationLogs)) {
                return 0;
            }

            // 批量数据脱敏和默认值设置
            LocalDateTime now = LocalDateTime.now();
            for (OaOperationLogEntity log : operationLogs) {
                maskSensitiveData(log);
                if (log.getCreateTime() == null) {
                    log.setCreateTime(now);
                }
                if (log.getUpdateTime() == null) {
                    log.setUpdateTime(now);
                }
                if (log.getDataVersion() == null) {
                    log.setDataVersion(1);
                }
            }

            // 批量插入（使用MyBatis-Plus的批量插入）
            boolean result = saveBatch(operationLogs);

            log.info("批量记录操作日志完成，数量：{}，结果：{}", operationLogs.size(), result);

            return result ? operationLogs.size() : 0;

        } catch (Exception e) {
            log.error("批量记录操作日志失败", e);
            return 0;
        }
    }

    @Override
    public OperationLogStatisticsResp getOperationLogStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 设置默认时间范围
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }

            OperationLogStatisticsResp statistics = new OperationLogStatisticsResp();

            // 基础统计
            buildBasicStatistics(statistics, startTime, endTime);

            // 操作类型分布统计
            buildOperationTypeStatistics(statistics, startTime, endTime);

            // 用户活跃度统计
            buildUserActivityStatistics(statistics, startTime, endTime);

            // 风险等级统计
            buildRiskLevelStatistics(statistics, startTime, endTime);

            // 时间趋势统计
            buildTimeTrendStatistics(statistics, startTime, endTime);

            return statistics;

        } catch (Exception e) {
            log.error("获取操作日志统计信息失败", e);
            return new OperationLogStatisticsResp();
        }
    }

    @Override
    @Async("operationLogCleanExecutor")
    public CompletableFuture<Integer> cleanExpiredLogsAsync(Integer retentionDays) {
        try {
            if (retentionDays == null || retentionDays <= 0) {
                retentionDays = DEFAULT_RETENTION_DAYS;
            }

            // 计算过期时间
            LocalDateTime expireTime = LocalDateTime.now().minusDays(retentionDays);

            // 软删除过期日志
            LambdaQueryWrapper<OaOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                       .lt(OaOperationLogEntity::getCreateTime, expireTime);

            OaOperationLogEntity updateEntity = new OaOperationLogEntity();
            updateEntity.setDeleteTime((int) (System.currentTimeMillis() / 1000));
            updateEntity.setUpdateTime(LocalDateTime.now());

            int result = operationLogMapper.update(updateEntity, queryWrapper);

            log.info("异步清理过期日志完成，保留天数：{}，清理数量：{}", retentionDays, result);

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            log.error("异步清理过期日志失败", e);
            return CompletableFuture.completedFuture(0);
        }
    }

    @Override
    public String exportOperationLogs(OperationLogPageRequest request) {
        try {
            if (request == null) {
                return null;
            }

            // 限制导出数量
            if (request.getPageSize() == null || request.getPageSize() > MAX_EXPORT_LIMIT) {
                request.setPageSize(MAX_EXPORT_LIMIT);
            }

            // 查询数据
            PageResult<OperationLogPageResp> pageResult = pageOperationLog(request);
            if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                return null;
            }

            // 生成导出文件（这里简化处理，实际应该生成Excel或CSV文件）
            StringBuilder sb = new StringBuilder();
            sb.append("操作日志导出\n");
            sb.append("导出时间：").append(OperationLogUtil.formatDateTime(LocalDateTime.now())).append("\n");
            sb.append("数据条数：").append(pageResult.getList().size()).append("\n\n");

            // 表头
            sb.append("序号,用户名,操作类型,操作模块,操作功能,操作状态,创建时间,执行时间\n");

            // 数据行
            for (int i = 0; i < pageResult.getList().size(); i++) {
                OperationLogPageResp log = pageResult.getList().get(i);
                sb.append(i + 1).append(",")
                  .append(log.getUsername()).append(",")
                  .append(getOperationTypeName(log.getOperationType())).append(",")
                  .append(log.getOperationModule()).append(",")
                  .append(log.getOperationFunction()).append(",")
                  .append(getOperationStateName(log.getOperationState())).append(",")
                  .append(OperationLogUtil.formatDateTime(log.getCreateTime())).append(",")
                  .append(OperationLogUtil.formatExecutionTime(log.getExecutionTime())).append("\n");
            }

            return sb.toString();

        } catch (Exception e) {
            log.error("导出操作日志失败", e);
            return null;
        }
    }

    @Override
    @Async("operationLogExecutor")
    public CompletableFuture<String> exportOperationLogsAsync(OperationLogPageRequest request) {
        try {
            String result = exportOperationLogs(request);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步导出操作日志失败", e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public Boolean recoverDataByLog(Long logId) {
        try {
            if (logId == null) {
                return false;
            }

            // 查询日志记录
            OaOperationLogEntity logEntity = getById(logId);
            if (logEntity == null) {
                log.warn("日志记录不存在，ID：{}", logId);
                return false;
            }

            // 检查是否有操作前数据
            if (!StringUtils.hasText(logEntity.getBeforeData())) {
                log.warn("日志记录没有操作前数据，无法恢复，ID：{}", logId);
                return false;
            }

            // 这里应该根据业务类型调用相应的恢复逻辑
            // 为了演示，这里只是记录日志
            log.info("数据恢复请求，日志ID：{}，业务类型：{}，业务ID：{}",
                    logId, logEntity.getBusinessType(), logEntity.getBusinessId());

            return true;

        } catch (Exception e) {
            log.error("根据日志恢复数据失败，日志ID：{}", logId, e);
            return false;
        }
    }

    @Override
    public String getDataChangeSummary(Long logId) {
        try {
            if (logId == null) {
                return null;
            }

            // 查询日志记录
            OaOperationLogEntity logEntity = getById(logId);
            if (logEntity == null) {
                return null;
            }

            // 生成数据变更摘要
            StringBuilder summary = new StringBuilder();
            summary.append("操作类型：").append(getOperationTypeName(logEntity.getOperationType())).append("\n");
            summary.append("操作时间：").append(OperationLogUtil.formatDateTime(logEntity.getCreateTime())).append("\n");
            summary.append("操作用户：").append(logEntity.getUsername()).append("\n");

            if (StringUtils.hasText(logEntity.getChangedFields())) {
                summary.append("变更字段：").append(logEntity.getChangedFields()).append("\n");
            }

            if (StringUtils.hasText(logEntity.getBeforeData())) {
                summary.append("操作前数据：").append(logEntity.getBeforeData()).append("\n");
            }

            if (StringUtils.hasText(logEntity.getAfterData())) {
                summary.append("操作后数据：").append(logEntity.getAfterData()).append("\n");
            }

            return summary.toString();

        } catch (Exception e) {
            log.error("获取数据变更摘要失败，日志ID：{}", logId, e);
            return null;
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 转换实体列表为响应列表
     *
     * 空间局部性优化：批量转换，减少方法调用开销
     *
     * @param entities 实体列表
     * @return 响应列表
     */
    private List<OperationLogPageResp> convertToPageResp(List<OaOperationLogEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::convertToPageResp)
                .collect(Collectors.toList());
    }

    /**
     * 转换实体为响应对象
     *
     * @param entity 实体对象
     * @return 响应对象
     */
    private OperationLogPageResp convertToPageResp(OaOperationLogEntity entity) {
        if (entity == null) {
            return null;
        }

        OperationLogPageResp resp = BeanUtils.toBean(entity, OperationLogPageResp.class);

        // 设置扩展字段
        resp.setOperationTypeName(getOperationTypeName(entity.getOperationType()));
        resp.setOperationStateName(getOperationStateName(entity.getOperationState()));
        resp.setRiskLevelName(getRiskLevelName(entity.getRiskLevel()));
        resp.setExecutionTimeDesc(OperationLogUtil.formatExecutionTime(entity.getExecutionTime()));

        return resp;
    }

    /**
     * 获取操作类型名称
     *
     * @param operationType 操作类型
     * @return 类型名称
     */
    private String getOperationTypeName(Integer operationType) {
        if (operationType == null) {
            return "未知";
        }
        switch (operationType) {
            case 1: return "查看";
            case 2: return "新增";
            case 3: return "修改";
            case 4: return "删除";
            case 5: return "导出";
            case 6: return "导入";
            case 7: return "登录";
            case 8: return "登出";
            case 9: return "其他";
            default: return "未知";
        }
    }

    /**
     * 获取操作状态名称
     *
     * @param operationState 操作状态
     * @return 状态名称
     */
    private String getOperationStateName(Integer operationState) {
        if (operationState == null) {
            return "未知";
        }
        return operationState == 1 ? "成功" : "失败";
    }

    /**
     * 获取风险等级名称
     *
     * @param riskLevel 风险等级
     * @return 等级名称
     */
    private String getRiskLevelName(Integer riskLevel) {
        if (riskLevel == null) {
            return "未知";
        }
        switch (riskLevel) {
            case 1: return "低风险";
            case 2: return "中风险";
            case 3: return "高风险";
            default: return "未知";
        }
    }

    /**
     * 敏感数据脱敏处理
     *
     * 安全优化：对敏感字段进行脱敏处理
     *
     * @param operationLog 操作日志实体
     */
    private void maskSensitiveData(OaOperationLogEntity operationLog) {
        if (operationLog == null) {
            return;
        }

        // 脱敏请求参数
        if (StringUtils.hasText(operationLog.getRequestParams())) {
            operationLog.setRequestParams(OperationLogUtil.maskSensitiveData(operationLog.getRequestParams()));
        }

        // 脱敏响应结果
        if (StringUtils.hasText(operationLog.getResponseResult())) {
            operationLog.setResponseResult(OperationLogUtil.maskSensitiveData(operationLog.getResponseResult()));
        }

        // 脱敏操作前数据
        if (StringUtils.hasText(operationLog.getBeforeData())) {
            operationLog.setBeforeData(OperationLogUtil.maskSensitiveData(operationLog.getBeforeData()));
        }

        // 脱敏操作后数据
        if (StringUtils.hasText(operationLog.getAfterData())) {
            operationLog.setAfterData(OperationLogUtil.maskSensitiveData(operationLog.getAfterData()));
        }

        // 限制字段长度，防止存储过长的数据
        if (StringUtils.hasText(operationLog.getOperationDescription())) {
            operationLog.setOperationDescription(OperationLogUtil.limitStringLength(operationLog.getOperationDescription(), 500));
        }

        if (StringUtils.hasText(operationLog.getRequestUrl())) {
            operationLog.setRequestUrl(OperationLogUtil.limitStringLength(operationLog.getRequestUrl(), 500));
        }

        if (StringUtils.hasText(operationLog.getUserAgent())) {
            operationLog.setUserAgent(OperationLogUtil.limitStringLength(operationLog.getUserAgent(), 1000));
        }
    }

    /**
     * 构建基础统计数据
     *
     * @param statistics 统计响应对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void buildBasicStatistics(OperationLogStatisticsResp statistics, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 总操作次数
            LambdaQueryWrapper<OaOperationLogEntity> totalWrapper = new LambdaQueryWrapper<>();
            totalWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                       .between(OaOperationLogEntity::getCreateTime, startTime, endTime);
            Long totalOperations = operationLogMapper.selectCount(totalWrapper);
            statistics.setTotalOperations(totalOperations);

            // 成功操作次数
            LambdaQueryWrapper<OaOperationLogEntity> successWrapper = new LambdaQueryWrapper<>();
            successWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                         .eq(OaOperationLogEntity::getOperationState, 1)
                         .between(OaOperationLogEntity::getCreateTime, startTime, endTime);
            Long successOperations = operationLogMapper.selectCount(successWrapper);
            statistics.setSuccessOperations(successOperations);

            // 失败操作次数
            Long failedOperations = totalOperations - successOperations;
            statistics.setFailedOperations(failedOperations);

            // 成功率
            if (totalOperations > 0) {
                double successRate = (successOperations * 100.0) / totalOperations;
                statistics.setSuccessRate(successRate);
            } else {
                statistics.setSuccessRate(0.0);
            }

            // 今日操作次数
            LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();
            LocalDateTime todayEnd = LocalDateTime.now();
            LambdaQueryWrapper<OaOperationLogEntity> todayWrapper = new LambdaQueryWrapper<>();
            todayWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                       .between(OaOperationLogEntity::getCreateTime, todayStart, todayEnd);
            Long todayOperations = operationLogMapper.selectCount(todayWrapper);
            statistics.setTodayOperations(todayOperations);

            // 高风险操作次数
            LambdaQueryWrapper<OaOperationLogEntity> highRiskWrapper = new LambdaQueryWrapper<>();
            highRiskWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                          .ge(OaOperationLogEntity::getRiskLevel, 3)
                          .between(OaOperationLogEntity::getCreateTime, startTime, endTime);
            Long highRiskOperations = operationLogMapper.selectCount(highRiskWrapper);
            statistics.setHighRiskOperations(highRiskOperations);

            // 敏感操作次数
            LambdaQueryWrapper<OaOperationLogEntity> sensitiveWrapper = new LambdaQueryWrapper<>();
            sensitiveWrapper.eq(OaOperationLogEntity::getDeleteTime, 0)
                           .eq(OaOperationLogEntity::getIsSensitive, 1)
                           .between(OaOperationLogEntity::getCreateTime, startTime, endTime);
            Long sensitiveOperations = operationLogMapper.selectCount(sensitiveWrapper);
            statistics.setSensitiveOperations(sensitiveOperations);

        } catch (Exception e) {
            log.error("构建基础统计数据失败", e);
        }
    }

    /**
     * 构建操作类型统计数据
     *
     * @param statistics 统计响应对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void buildOperationTypeStatistics(OperationLogStatisticsResp statistics, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            QueryWrapper<OaOperationLogEntity> wrapper = new QueryWrapper<>();
            wrapper.select("operation_type", "COUNT(*) as count")
                   .eq("delete_time", 0)
                   .between("create_time", startTime, endTime)
                   .groupBy("operation_type")
                   .orderByDesc("count");

            List<Map<String, Object>> typeData = operationLogMapper.selectMaps(wrapper);

            List<OperationLogStatisticsResp.StatisticItem> typeDistribution = typeData.stream()
                    .map(map -> {
                        OperationLogStatisticsResp.StatisticItem item = new OperationLogStatisticsResp.StatisticItem();
                        item.setName(getOperationTypeName((Integer) map.get("operation_type")));
                        item.setValue(String.valueOf(map.get("operation_type")));
                        item.setCount((Long) map.get("count"));
                        return item;
                    })
                    .collect(Collectors.toList());

            statistics.setOperationTypeDistribution(typeDistribution);

        } catch (Exception e) {
            log.error("构建操作类型统计数据失败", e);
        }
    }

    /**
     * 构建用户活跃度统计数据
     *
     * @param statistics 统计响应对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void buildUserActivityStatistics(OperationLogStatisticsResp statistics, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            QueryWrapper<OaOperationLogEntity> wrapper = new QueryWrapper<>();
            wrapper.select("user_id", "username", "COUNT(*) as operation_count")
                   .eq("delete_time", 0)
                   .between("create_time", startTime, endTime)
                   .groupBy("user_id", "username")
                   .orderByDesc("operation_count")
                   .last("LIMIT 10");

            List<Map<String, Object>> activityData = operationLogMapper.selectMaps(wrapper);

            List<OperationLogStatisticsResp.UserActivityItem> userActivity = activityData.stream()
                    .map(map -> {
                        OperationLogStatisticsResp.UserActivityItem item = new OperationLogStatisticsResp.UserActivityItem();
                        item.setUserId((Long) map.get("user_id"));
                        item.setUsername((String) map.get("username"));
                        item.setOperationCount((Long) map.get("operation_count"));
                        return item;
                    })
                    .collect(Collectors.toList());

            statistics.setUserActivityRanking(userActivity);
            statistics.setActiveUsers((long) userActivity.size());

        } catch (Exception e) {
            log.error("构建用户活跃度统计数据失败", e);
        }
    }

    /**
     * 构建风险等级统计数据
     *
     * @param statistics 统计响应对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void buildRiskLevelStatistics(OperationLogStatisticsResp statistics, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            QueryWrapper<OaOperationLogEntity> wrapper = new QueryWrapper<>();
            wrapper.select("risk_level", "COUNT(*) as count")
                   .eq("delete_time", 0)
                   .between("create_time", startTime, endTime)
                   .groupBy("risk_level")
                   .orderByDesc("risk_level");

            List<Map<String, Object>> riskData = operationLogMapper.selectMaps(wrapper);

            List<OperationLogStatisticsResp.StatisticItem> riskDistribution = riskData.stream()
                    .map(map -> {
                        OperationLogStatisticsResp.StatisticItem item = new OperationLogStatisticsResp.StatisticItem();
                        item.setName(getRiskLevelName((Integer) map.get("risk_level")));
                        item.setValue(String.valueOf(map.get("risk_level")));
                        item.setCount((Long) map.get("count"));
                        return item;
                    })
                    .collect(Collectors.toList());

            statistics.setRiskLevelDistribution(riskDistribution);

        } catch (Exception e) {
            log.error("构建风险等级统计数据失败", e);
        }
    }

    /**
     * 构建时间趋势统计数据
     *
     * @param statistics 统计响应对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void buildTimeTrendStatistics(OperationLogStatisticsResp statistics, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            QueryWrapper<OaOperationLogEntity> wrapper = new QueryWrapper<>();
            wrapper.select("DATE(create_time) as operation_date", "COUNT(*) as count")
                   .eq("delete_time", 0)
                   .between("create_time", startTime, endTime)
                   .groupBy("DATE(create_time)")
                   .orderByAsc("operation_date");

            List<Map<String, Object>> trendData = operationLogMapper.selectMaps(wrapper);

            List<OperationLogStatisticsResp.TrendItem> dailyTrend = trendData.stream()
                    .map(map -> {
                        OperationLogStatisticsResp.TrendItem item = new OperationLogStatisticsResp.TrendItem();
                        item.setTimeLabel(String.valueOf(map.get("operation_date")));
                        item.setOperationCount((Long) map.get("count"));
                        return item;
                    })
                    .collect(Collectors.toList());

            statistics.setDailyOperationTrend(dailyTrend);

        } catch (Exception e) {
            log.error("构建时间趋势统计数据失败", e);
        }
    }
}

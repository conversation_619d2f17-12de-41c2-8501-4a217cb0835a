package com.miaowen.oa.system.domain.dto;

import lombok.Getter;

/**
 * 用户职业信息值对象
 * <AUTHOR>
 */
@Getter
public class UserCareer {
    /**
     * 工号
     */
    private final String userCode;
    /**
     *职级
     */
    private final String level;
    /**
     *数据权限等级
     */
    private final Integer dataLimitRank;
    /**
     *是否部门管理员
     */
    private final Integer isDepartmentManage;
    /**
     *加入时间
     */
    private final Integer joinTime;
    /**
     *离职时间
     */
    private final Integer leaveTime;
    /**
     *离职备注
     */
    private final String leaveNotes;

    public UserCareer(String userCode, String level, Integer dataLimitRank, 
                     Integer isDepartmentManage, Integer joinTime, 
                     Integer leaveTime, String leaveNotes) {
        this.userCode = userCode;
        this.level = level;
        this.dataLimitRank = dataLimitRank;
        this.isDepartmentManage = isDepartmentManage;
        this.joinTime = joinTime;
        this.leaveTime = leaveTime;
        this.leaveNotes = leaveNotes;
    }
    
    /**
     * 离职操作
     */
    public UserCareer resign(String notes) {
        return new UserCareer(
            userCode,
            level,
            dataLimitRank,
            isDepartmentManage,
            joinTime,
                // 当前时间戳
                (int)System.currentTimeMillis() / 1000,
            notes
        );
    }
}
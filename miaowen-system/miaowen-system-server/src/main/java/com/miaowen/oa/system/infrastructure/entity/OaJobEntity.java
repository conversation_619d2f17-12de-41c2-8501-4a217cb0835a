package com.miaowen.oa.system.infrastructure.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/14 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value ="oa_job")
public class OaJobEntity extends BaseDO {


    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 岗位编码
     */
    private String jobCode;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;


}

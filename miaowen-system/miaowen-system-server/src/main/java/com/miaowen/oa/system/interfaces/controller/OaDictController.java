package com.miaowen.oa.system.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.domain.service.OaDictService;
import com.miaowen.oa.system.interfaces.req.dict.DictPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictSaveRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictUpdateRequest;
import com.miaowen.oa.system.interfaces.res.dict.OaDictDetailResp;
import com.miaowen.oa.system.interfaces.res.dict.OaDictPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static com.miaowen.oa.framework.common.pojo.CommonResult.success;

/**
 * 字典管理
 * <AUTHOR>
 * @Date 2025/7/15 15:02
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Tag(name = "字典管理")
@Slf4j
@RequestMapping("/dict")
@RestController
@AllArgsConstructor
public class OaDictController {

    private OaDictService oaDictService;


    @PostMapping
    @Operation(summary = "新增字典")
    public CommonResult<Void> createDict(@Valid @RequestBody DictSaveRequest dictSaveRequest) {
        oaDictService.createDict(dictSaveRequest);
        return success(null);
    }

    @PutMapping
    @Operation(summary = "更新字典")
    public CommonResult<Void> updateDict(@Valid @RequestBody DictUpdateRequest dictUpdateRequest) {
        oaDictService.updateDict(dictUpdateRequest);
        return success(null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除字典")
    public CommonResult<Void> deleteDict(@PathVariable("id") Long id) {
        oaDictService.deleteDict(id);
        return success(null);
    }

    @GetMapping("/page")
    @Operation(summary = "字典分页查询")
    public CommonResult<PageResult<OaDictPageResp>> getDictPage(@Valid DictPageQueryRequest dictPageQueryRequest) {
        PageResult<OaDictPageResp> pageResult = oaDictService.getDictPage(dictPageQueryRequest);
        return success(pageResult);
    }


    @GetMapping("/{id}")
    @Operation(summary = "字典详情")
    public CommonResult<OaDictDetailResp> getDictDetail(@PathVariable("id") Long id) {
        OaDictDetailResp oaDictDetailResp = oaDictService.getDictDetail(id);
        return success(oaDictDetailResp);
    }


}

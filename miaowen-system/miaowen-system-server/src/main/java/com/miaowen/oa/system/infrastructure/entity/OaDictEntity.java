package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/15 11:57
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
@TableName("oa_dict")
public class OaDictEntity extends BaseDO {
    private Long id;

    /**
     * 字典名称
     */
    private String dictName;
    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 0-正常 1-禁用
     */
    private Integer state;

}

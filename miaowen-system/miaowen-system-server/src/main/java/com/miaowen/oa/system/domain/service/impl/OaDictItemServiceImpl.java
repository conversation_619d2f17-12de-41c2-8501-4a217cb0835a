package com.miaowen.oa.system.domain.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.oa.framework.common.enums.CommonStatusEnum;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.framework.common.util.stream.StreamUtil;
import com.miaowen.oa.system.domain.service.OaDictItemService;
import com.miaowen.oa.system.domain.service.OaDictService;
import com.miaowen.oa.system.infrastructure.entity.OaDictEntity;
import com.miaowen.oa.system.infrastructure.entity.OaDictItemEntity;
import com.miaowen.oa.system.infrastructure.entity.OaUserEntity;
import com.miaowen.oa.system.infrastructure.mapper.OaDictItemMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaUserMapper;
import com.miaowen.oa.system.interfaces.req.dict.DictItemBatchSaveRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictItemSaveRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictItemUpdateRequest;
import com.miaowen.oa.system.interfaces.res.dict.DictItemDetailResp;
import com.miaowen.oa.system.interfaces.res.dict.DictItemResp;
import com.miaowen.oa.system.interfaces.res.dict.OaDictDetailResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static com.miaowen.oa.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @Date 2025/7/15 14:56
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Service
public class OaDictItemServiceImpl extends ServiceImpl<OaDictItemMapper, OaDictItemEntity> implements OaDictItemService {

    @Autowired
    private OaDictService oaDictService;

    @Autowired
    private OaUserMapper oaUserMapper;



    @Override
    public void logicDeleteByDictId(Long dictId) {
        baseMapper.logicDelete(Wrappers.lambdaQuery(OaDictItemEntity.class).eq(OaDictItemEntity::getDictId, dictId));
    }

    @Override
    @Transactional
    public void batchCreateDictItem(DictItemBatchSaveRequest dictItemBatchSaveRequest) {
        List<DictItemSaveRequest> dictItemSaveRequestList = dictItemBatchSaveRequest.getDictItemList();
        String dictCode = dictItemBatchSaveRequest.getDictCode();
        Long dictId = dictItemBatchSaveRequest.getDictId();
        // 校验字典有效
        validateDictExists(dictId);
        // 校验字典数据的值的唯一性
        for (DictItemSaveRequest dictItemSaveRequest : dictItemSaveRequestList) {
            validateDictItemValueUnique(null, dictId, dictItemSaveRequest.getItemValue());
        }
        // 校验字典数据的label的唯一性
        for (DictItemSaveRequest dictItemSaveRequest : dictItemSaveRequestList) {
            validateDictItemLabelUnique(null, dictId, dictItemSaveRequest.getLabel());
        }
        List<OaDictItemEntity> oaDictItemEntityList = BeanUtils.toBean(dictItemSaveRequestList, OaDictItemEntity.class);
        for (OaDictItemEntity oaDictItemEntity : oaDictItemEntityList) {
            oaDictItemEntity.setDictId(dictId);
            oaDictItemEntity.setDictCode(dictCode);
        }
        this.saveBatch(oaDictItemEntityList);
    }

    @Override
    public void updateDictItem(DictItemUpdateRequest dictItemUpdateRequest) {
        // 校验自己存在
        OaDictItemEntity oaDictItemEntity = validateDictItemExists(dictItemUpdateRequest.getId());
        // 校验字典数据的值的唯一性
        validateDictItemValueUnique(dictItemUpdateRequest.getId(), oaDictItemEntity.getDictId(), dictItemUpdateRequest.getItemValue());
        // 校验字典数据的标签的唯一性
        validateDictItemLabelUnique(dictItemUpdateRequest.getId(), oaDictItemEntity.getDictId(), dictItemUpdateRequest.getLabel());
        OaDictItemEntity oaDictItem = BeanUtils.toBean(dictItemUpdateRequest, OaDictItemEntity.class);
        baseMapper.updateById(oaDictItem);
    }

    @Override
    public void deleteDictItem(Long id) {
        // 校验是否存在
        validateDictItemExists(id);

        // 删除字典数据
        baseMapper.logicDeleteById(id);
    }

    @Override
    public List<DictItemResp> getDictItemList(Long dictId) {
        List<OaDictItemEntity> oaDictItemEntityList = baseMapper.selectList(Wrappers.lambdaQuery(OaDictItemEntity.class).eq(OaDictItemEntity::getDictId, dictId).eq(OaDictItemEntity::getDeleteTime, 0)
                .orderByAsc(OaDictItemEntity::getSort)
                .orderByDesc(OaDictItemEntity::getUpdateTime)
        );
        List<DictItemResp> dictItemRespList = BeanUtils.toBean(oaDictItemEntityList, DictItemResp.class);

        //查询所有用户
        List<OaUserEntity> oaUserList = oaUserMapper.selectList(Wrappers.lambdaQuery(OaUserEntity.class).eq(OaUserEntity::getDeleteTime, 0));
        Map<Long, OaUserEntity> userMap = StreamUtil.map(oaUserList, OaUserEntity::getId);
        for (DictItemResp dictItemResp : dictItemRespList) {
            if (dictItemResp.getUpdater() != null) {
                OaUserEntity updaterUser = userMap.get(dictItemResp.getUpdater());
                if (updaterUser != null) {
                    dictItemResp.setUpdaterName(updaterUser.getRealName());
                }
            }
        }

        return dictItemRespList;
    }

    @Override
    public DictItemDetailResp getDictItemDetail(Long id) {
        OaDictItemEntity oaDictItemEntity = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictItemEntity.class).eq(OaDictItemEntity::getId, id).eq(OaDictItemEntity::getDeleteTime, 0));
        return BeanUtils.toBean(oaDictItemEntity, DictItemDetailResp.class);
    }

    public OaDictItemEntity validateDictItemExists(Long id) {

        OaDictItemEntity oaDictItemEntity = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictItemEntity.class).eq(OaDictItemEntity::getId, id).eq(OaDictItemEntity::getDeleteTime, 0));
        if (oaDictItemEntity == null) {
            throw exception(DICT_DATA_NOT_EXISTS);
        }
        return oaDictItemEntity;
    }

    private void validateDictItemLabelUnique(Long id, Long dictId, String label) {

        OaDictItemEntity oaDictItem = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictItemEntity.class).eq(OaDictItemEntity::getDictId, dictId).eq(OaDictItemEntity::getLabel, label));

        if (oaDictItem == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的字典数据
        if (id == null) {
            throw exception(DICT_DATA_LABEL_DUPLICATE);
        }
        if (!oaDictItem.getId().equals(id)) {
            throw exception(DICT_DATA_LABEL_DUPLICATE);
        }
    }


    public void validateDictExists(Long dictId) {
        OaDictEntity oaDictEntity = oaDictService.lambdaQuery().eq(OaDictEntity::getId, dictId).one();
        if (oaDictEntity == null) {
            throw exception(DICT_TYPE_NOT_EXISTS);
        }
    }


    public void validateDictItemValueUnique(Long id, Long dictId, String value) {
        OaDictItemEntity oaDictItem = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictItemEntity.class).eq(OaDictItemEntity::getDictId, dictId).eq(OaDictItemEntity::getItemValue, value));

        if (oaDictItem == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的字典数据
        if (id == null) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
        if (!oaDictItem.getId().equals(id)) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
    }
}

package com.miaowen.oa.system.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 风险等级枚举
 * 
 * 功能说明：
 * 1. 定义操作的风险等级
 * 2. 用于安全监控和审计
 * 3. 支持风险评估和预警
 * 4. 便于风险管控
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Getter
@AllArgsConstructor
public enum RiskLevelEnum {

    /**
     * 低风险
     */
    LOW(1, "低风险", "low", "#52c41a", "常规操作，风险较低"),

    /**
     * 中风险
     */
    MEDIUM(2, "中风险", "medium", "#faad14", "需要注意的操作，有一定风险"),

    /**
     * 高风险
     */
    HIGH(3, "高风险", "high", "#f5222d", "危险操作，需要严格控制");

    /**
     * 风险等级码
     */
    private final Integer code;

    /**
     * 风险等级名称
     */
    private final String name;

    /**
     * 风险等级英文名
     */
    private final String englishName;

    /**
     * 显示颜色
     */
    private final String color;

    /**
     * 风险等级描述
     */
    private final String description;

    /**
     * 根据等级码获取枚举
     * 
     * @param code 等级码
     * @return 风险等级枚举
     */
    public static RiskLevelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RiskLevelEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }

    /**
     * 根据等级码获取名称
     * 
     * @param code 等级码
     * @return 等级名称
     */
    public static String getNameByCode(Integer code) {
        RiskLevelEnum level = getByCode(code);
        return level != null ? level.getName() : "未知";
    }

    /**
     * 根据等级码获取颜色
     * 
     * @param code 等级码
     * @return 颜色值
     */
    public static String getColorByCode(Integer code) {
        RiskLevelEnum level = getByCode(code);
        return level != null ? level.getColor() : "#666666";
    }

    /**
     * 根据等级码获取描述
     * 
     * @param code 等级码
     * @return 等级描述
     */
    public static String getDescriptionByCode(Integer code) {
        RiskLevelEnum level = getByCode(code);
        return level != null ? level.getDescription() : "未知风险等级";
    }

    /**
     * 判断是否为高风险
     * 
     * @param code 等级码
     * @return 是否为高风险
     */
    public static boolean isHighRisk(Integer code) {
        return HIGH.getCode().equals(code);
    }

    /**
     * 判断是否需要特别关注
     * 
     * @param code 等级码
     * @return 是否需要特别关注
     */
    public static boolean needSpecialAttention(Integer code) {
        return MEDIUM.getCode().equals(code) || HIGH.getCode().equals(code);
    }

    /**
     * 根据操作类型评估风险等级
     * 
     * @param operationType 操作类型
     * @param url 请求URL
     * @return 风险等级码
     */
    public static Integer assessRiskLevel(Integer operationType, String url) {
        // 删除操作为高风险
        if (operationType != null && operationType == 4) {
            return HIGH.getCode();
        }
        
        // 敏感URL为高风险
        if (url != null) {
            String lowerUrl = url.toLowerCase();
            if (lowerUrl.contains("/delete") || lowerUrl.contains("/remove") || 
                lowerUrl.contains("/reset") || lowerUrl.contains("/clear") ||
                lowerUrl.contains("/password") || lowerUrl.contains("/secret")) {
                return HIGH.getCode();
            }
        }
        
        // 修改操作为中风险
        if (operationType != null && (operationType == 3 || operationType == 6)) {
            return MEDIUM.getCode();
        }
        
        // 其他为低风险
        return LOW.getCode();
    }
}

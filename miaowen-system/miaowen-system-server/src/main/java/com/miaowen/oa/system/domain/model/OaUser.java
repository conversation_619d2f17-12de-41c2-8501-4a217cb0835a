package com.miaowen.oa.system.domain.model;

import com.miaowen.oa.system.domain.dto.*;
import com.miaowen.oa.system.infrastructure.entity.OaUserEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户聚合根 - 重构版
 *
 * 重构内容：
 * 1. 根据OaUserEntity的完整字段结构重新设计值对象
 * 2. 增加员工信息、个人信息、银行信息等新的值对象
 * 3. 保持向后兼容性，不影响其他模块的调用
 * 4. 完善领域行为和业务逻辑
 * 5. 支持完整的用户生命周期管理
 *
 * <AUTHOR>
 * @Date 2025/7/29 (重构)
 * @Company 武汉市妙闻网络科技有限公司
 */
@Data
public class OaUser {

    // ========== 聚合根核心组件 ==========

    // 聚合根标识
    private UserId userId;

    // 用户状态（领域行为）
    private UserState state;

    // ========== 值对象组合（重构后的完整结构） ==========

    // 用户基本信息（值对象）- 保持兼容
    private UserBasicInfo basicInfo;

    // 安全信息（值对象）- 保持兼容
    private UserSecurity security;

    // 职业信息（值对象）- 保持兼容
    private UserCareer career;

    // 联系信息（值对象）- 保持兼容
    private UserContact contact;

    // ========== 新增值对象（根据OaUserEntity扩展） ==========

    // 员工信息（值对象）- 新增
    private UserEmployeeInfo employeeInfo;

    // 个人信息（值对象）- 新增
    private UserPersonalInfo personalInfo;

    // 银行信息（值对象）- 新增
    private UserBankInfo bankInfo;

    // ========== 领域行为历史记录 ==========

    // 状态转换历史（值对象集合）
    private final List<UserStatusHistory> statusHistory = new ArrayList<>();

    // ========== 构造函数 ==========

    // 禁止外部直接实例化 - 重构版（保持向后兼容）
    private OaUser(UserId userId, UserState state, UserBasicInfo basicInfo,
                   UserSecurity security, UserCareer career, UserContact contact) {
        this.userId = Objects.requireNonNull(userId, "用户ID不能为空");
        this.state = Objects.requireNonNull(state, "用户状态不能为空");
        this.basicInfo = Objects.requireNonNull(basicInfo, "基本信息不能为空");
        this.security = Objects.requireNonNull(security, "安全信息不能为空");
        this.career = Objects.requireNonNull(career, "职业信息不能为空");
        this.contact = Objects.requireNonNull(contact, "联系信息不能为空");

        // 新增字段初始化为默认值，保证兼容性
        this.employeeInfo = UserEmployeeInfo.createDefault();
        this.personalInfo = UserPersonalInfo.createDefault();
        this.bankInfo = UserBankInfo.createDefault();
    }

    // 完整构造函数 - 新增（支持所有字段）
    private OaUser(UserId userId, UserState state, UserBasicInfo basicInfo,
                   UserSecurity security, UserCareer career, UserContact contact,
                   UserEmployeeInfo employeeInfo, UserPersonalInfo personalInfo, UserBankInfo bankInfo) {
        this.userId = Objects.requireNonNull(userId, "用户ID不能为空");
        this.state = Objects.requireNonNull(state, "用户状态不能为空");
        this.basicInfo = Objects.requireNonNull(basicInfo, "基本信息不能为空");
        this.security = Objects.requireNonNull(security, "安全信息不能为空");
        this.career = Objects.requireNonNull(career, "职业信息不能为空");
        this.contact = Objects.requireNonNull(contact, "联系信息不能为空");
        this.employeeInfo = Objects.requireNonNull(employeeInfo, "员工信息不能为空");
        this.personalInfo = Objects.requireNonNull(personalInfo, "个人信息不能为空");
        this.bankInfo = Objects.requireNonNull(bankInfo, "银行信息不能为空");
    }

    /**
     * 工厂方法 - 从实体创建聚合根（重构版）
     * 支持完整的OaUserEntity字段映射
     */
    public static OaUser fromEntity(OaUserEntity entity) {
        // 基础信息映射（保持兼容）
        UserId userId = new UserId(entity.getId());
        UserState state = UserState.fromCode(entity.getState());

        UserBasicInfo basicInfo = new UserBasicInfo(
            entity.getUsername(),
            entity.getRealName(),
            entity.getGender(),
            entity.getAvatarUrl()
        );

        UserSecurity security = new UserSecurity(
            entity.getQyWechatUserId(),
            entity.getPassword(),
            entity.getEnablePasswordLogin(),
            entity.getLoginNumber(),
            entity.getLastLoginIp(),
            entity.getLastLoginTime()
        );

        UserCareer career = new UserCareer(
            entity.getUserCode(),
            entity.getLevel(),
            entity.getDataLimitRank(),
            entity.getIsDepartmentManage(),
            entity.getJoinTime(),
            entity.getLeaveTime() != null ? entity.getLeaveTime().intValue() : 0,
            entity.getLeaveNotes()
        );

        UserContact contact = new UserContact(
            entity.getEmail(),
            entity.getPhone(),
            entity.getProvince(),
            entity.getAddress(),
            entity.getIdentity()
        );

        // 新增值对象映射
        UserEmployeeInfo employeeInfo = new UserEmployeeInfo(
            entity.getDepartmentId(),
            entity.getDepartmentName(),
            entity.getPositionId(),
            entity.getPositionName(),
            entity.getSuperiorId(),
            entity.getSuperiorName(),
            entity.getEmployeeNumber(),
            entity.getEmployeeType(),
            entity.getEmployeeStatus(),
            entity.getEntryDate(),
            entity.getProbationEndDate(),
            entity.getRegularDate()
        );

        UserPersonalInfo personalInfo = new UserPersonalInfo(
            entity.getBirthday(),
            entity.getAge(),
            entity.getNationality(),
            entity.getEthnicity(),
            entity.getMaritalStatus(),
            entity.getPoliticalStatus(),
            entity.getIdCardType(),
            entity.getIdCardNumber(),
            entity.getIdCardIssueDate(),
            entity.getIdCardExpiryDate(),
            entity.getIdCardIssueAuthority(),
            entity.getHouseholdRegistration(),
            entity.getResidenceAddress()
        );

        UserBankInfo bankInfo = new UserBankInfo(
            entity.getBankName(),
            entity.getBankBranch(),
            entity.getBankAccountName(),
            entity.getBankAccountNumber(),
            entity.getSalaryPaymentMethod(),
            entity.getSocialSecurityAccount(),
            entity.getProvidentFundAccount(),
            entity.getTaxId()
        );

        // 使用完整构造函数创建聚合根
        return new OaUser(userId, state, basicInfo, security, career, contact,
                          employeeInfo, personalInfo, bankInfo);
    }

    /**
     * 工厂方法 - 创建新用户（简化版，保持兼容）
     */
    public static OaUser createNew(String username, String realName, String password,
                                  String email, String phone) {
        UserId userId = new UserId(0L); // 临时ID，保存时会生成
        UserState state = UserState.ACTIVE;

        UserBasicInfo basicInfo = new UserBasicInfo(
            username,
            realName,
            1, // 默认男性
            "" // 默认无头像
        );

        UserSecurity security = UserSecurity.createWithPassword(password);
        UserCareer career = UserCareer.createDefault();
        UserContact contact = new UserContact(email, phone, "", "", "");

        return new OaUser(userId, state, basicInfo, security, career, contact);
    }

    /**
     * 工厂方法 - 创建新用户（完整版）
     */
    public static OaUser createComplete(String username, String realName, Integer gender,
                                       String password, String email, String phone,
                                       Long departmentId, String departmentName,
                                       Long positionId, String positionName) {
        UserId userId = new UserId(0L); // 临时ID，保存时会生成
        UserState state = UserState.ACTIVE;

        UserBasicInfo basicInfo = new UserBasicInfo(
            username,
            realName,
            gender,
            "" // 默认无头像
        );

        UserSecurity security = UserSecurity.createWithPassword(password);
        UserCareer career = UserCareer.createDefault();
        UserContact contact = new UserContact(email, phone, "", "", "");

        UserEmployeeInfo employeeInfo = new UserEmployeeInfo(
            departmentId,
            departmentName,
            positionId,
            positionName,
            null, // 默认无上级
            "",
            "", // 员工编号待生成
            1, // 默认正式员工
            1, // 默认在职状态
            System.currentTimeMillis(), // 当前时间作为入职时间
            0L, // 默认无试用期结束时间
            0L  // 默认无转正时间
        );

        UserPersonalInfo personalInfo = UserPersonalInfo.createDefault();
        UserBankInfo bankInfo = UserBankInfo.createDefault();

        return new OaUser(userId, state, basicInfo, security, career, contact,
                         employeeInfo, personalInfo, bankInfo);
    }

    /**
     * 转换为基础设施层实体
     */
    public OaUserEntity toEntity() {
        OaUserEntity entity = new OaUserEntity();

        // 标识
        entity.setId(userId.getValue());

        // 状态
        entity.setState(state.getCode());

        // 基本信息
        entity.setUsername(basicInfo.getUsername());
        entity.setRealName(basicInfo.getRealName());
        entity.setGender(basicInfo.getGender());
        entity.setAvatarUrl(basicInfo.getAvatarUrl());

        // 安全信息
        entity.setPassword(security.getPassword().getHash());
        entity.setLoginNumber(security.getLoginNumber());
        entity.setLastLoginIp(security.getLastLoginIp());
        entity.setLastLoginTime(security.getLastLoginTime());

        // 职业信息
        entity.setUserCode(career.getUserCode());
        entity.setLevel(career.getLevel());
        entity.setDataLimitRank(career.getDataLimitRank());
        entity.setIsDepartmentManage(career.getIsDepartmentManage());
        entity.setJoinTime(career.getJoinTime());
        entity.setLeaveTime(career.getLeaveTime().longValue());
        entity.setLeaveNotes(career.getLeaveNotes());

        // 联系信息
        entity.setEmail(contact.getEmail());
        entity.setPhone(contact.getPhone());
        entity.setProvince(contact.getProvince());
        entity.setAddress(contact.getAddress());
        entity.setIdentity(contact.getIdentity());

        // 员工信息（新增映射）
        if (this.employeeInfo != null) {
            entity.setDepartmentId(this.employeeInfo.getDepartmentId());
            entity.setDepartmentName(this.employeeInfo.getDepartmentName());
            entity.setPositionId(this.employeeInfo.getPositionId());
            entity.setPositionName(this.employeeInfo.getPositionName());
            entity.setSuperiorId(this.employeeInfo.getSuperiorId());
            entity.setSuperiorName(this.employeeInfo.getSuperiorName());
            entity.setEmployeeNumber(this.employeeInfo.getEmployeeNumber());
            entity.setEmployeeType(this.employeeInfo.getEmployeeType());
            entity.setEmployeeStatus(this.employeeInfo.getEmployeeStatus());
            entity.setEntryDate(this.employeeInfo.getEntryDate());
            entity.setProbationEndDate(this.employeeInfo.getProbationEndDate());
            entity.setRegularDate(this.employeeInfo.getRegularDate());
        }

        // 个人信息（新增映射）
        if (this.personalInfo != null) {
            entity.setBirthday(this.personalInfo.getBirthday());
            entity.setAge(this.personalInfo.getAge());
            entity.setNationality(this.personalInfo.getNationality());
            entity.setEthnicity(this.personalInfo.getEthnicity());
            entity.setMaritalStatus(this.personalInfo.getMaritalStatus());
            entity.setPoliticalStatus(this.personalInfo.getPoliticalStatus());
            entity.setIdCardType(this.personalInfo.getIdCardType());
            entity.setIdCardNumber(this.personalInfo.getIdCardNumber());
            entity.setIdCardIssueDate(this.personalInfo.getIdCardIssueDate());
            entity.setIdCardExpiryDate(this.personalInfo.getIdCardExpiryDate());
            entity.setIdCardIssueAuthority(this.personalInfo.getIdCardIssueAuthority());
            entity.setHouseholdRegistration(this.personalInfo.getHouseholdRegistration());
            entity.setResidenceAddress(this.personalInfo.getResidenceAddress());
        }

        // 银行信息（新增映射）
        if (this.bankInfo != null) {
            entity.setBankName(this.bankInfo.getBankName());
            entity.setBankBranch(this.bankInfo.getBankBranch());
            entity.setBankAccountName(this.bankInfo.getBankAccountName());
            entity.setBankAccountNumber(this.bankInfo.getBankAccountNumber());
            entity.setSalaryPaymentMethod(this.bankInfo.getSalaryPaymentMethod());
            entity.setSocialSecurityAccount(this.bankInfo.getSocialSecurityAccount());
            entity.setProvidentFundAccount(this.bankInfo.getProvidentFundAccount());
            entity.setTaxId(this.bankInfo.getTaxId());
        }

        return entity;
    }


    /**
     * =============== 业务行为 ===============
     */

    /**
     * 更新基本信息
     */
    public void updateBasicInfo(UserBasicInfo newInfo) {
        this.basicInfo.update(newInfo);
    }

    /**
     * 更新联系信息
     */
    public void updateContactInfo(UserContact newContact) {
        this.contact.update(newContact);
    }

    // ========== 新增业务方法（支持新的值对象） ==========

    /**
     * 更新员工信息
     */
    public void updateEmployeeInfo(UserEmployeeInfo newEmployeeInfo) {
        this.employeeInfo = Objects.requireNonNull(newEmployeeInfo, "员工信息不能为空");
    }

    /**
     * 更新个人信息
     */
    public void updatePersonalInfo(UserPersonalInfo newPersonalInfo) {
        this.personalInfo = Objects.requireNonNull(newPersonalInfo, "个人信息不能为空");
    }

    /**
     * 更新银行信息
     */
    public void updateBankInfo(UserBankInfo newBankInfo) {
        this.bankInfo = Objects.requireNonNull(newBankInfo, "银行信息不能为空");
    }

    /**
     * 更新部门信息
     */
    public void updateDepartment(Long departmentId, String departmentName) {
        if (this.employeeInfo != null) {
            this.employeeInfo = this.employeeInfo.updateDepartment(departmentId, departmentName);
        }
    }

    /**
     * 更新职位信息
     */
    public void updatePosition(Long positionId, String positionName) {
        if (this.employeeInfo != null) {
            this.employeeInfo = this.employeeInfo.updatePosition(positionId, positionName);
        }
    }

    /**
     * 设置上级
     */
    public void setSuperior(Long superiorId, String superiorName) {
        if (this.employeeInfo != null) {
            this.employeeInfo = this.employeeInfo.updateSuperior(superiorId, superiorName);
        }
    }

    /**
     * 员工转正
     */
    public void regularEmployee() {
        if (this.employeeInfo != null && this.employeeInfo.canBeRegular()) {
            this.employeeInfo = this.employeeInfo.setRegular(System.currentTimeMillis());
            statusHistory.add(new UserStatusHistory(UserState.ACTIVE, System.currentTimeMillis(), "员工转正"));
        }
    }

    /**
     * 更新婚姻状况
     */
    public void updateMaritalStatus(Integer maritalStatus) {
        if (this.personalInfo != null) {
            this.personalInfo = this.personalInfo.updateMaritalStatus(maritalStatus);
        }
    }

    /**
     * 更新政治面貌
     */
    public void updatePoliticalStatus(Integer politicalStatus) {
        if (this.personalInfo != null) {
            this.personalInfo = this.personalInfo.updatePoliticalStatus(politicalStatus);
        }
    }

    /**
     * 更新银行账户
     */
    public void updateBankAccount(String bankName, String bankBranch,
                                 String bankAccountName, String bankAccountNumber) {
        if (this.bankInfo != null) {
            this.bankInfo = this.bankInfo.updateBankAccount(bankName, bankBranch,
                                                           bankAccountName, bankAccountNumber);
        }
    }

    /**
     * 重置密码
     */
    public void resetPassword(String newPassword) {
        this.security = security.withNewPassword(newPassword);
    }

    /**
     * 记录登录信息
     */
    public void recordLogin(String ip, int loginTime) {
        this.security = security.recordLogin(ip, loginTime);
    }

    /**
     * 激活用户（从无效状态激活）
     */
    public void activate() {
        state.transitionTo(UserState.ACTIVE, this);
        statusHistory.add(new UserStatusHistory(UserState.ACTIVE, System.currentTimeMillis()));
    }

    /**
     * 禁用用户（从正常状态禁用）
     */
    public void disable() {
        state.transitionTo(UserState.DISABLED, this);
        statusHistory.add(new UserStatusHistory(UserState.DISABLED, System.currentTimeMillis()));
    }

    /**
     * 启用用户（从禁用状态启用）
     */
    public void enable() {
        state.transitionTo(UserState.ACTIVE, this);
        statusHistory.add(new UserStatusHistory(UserState.ACTIVE, System.currentTimeMillis()));
    }

    /**
     * 标记为无效（从任何状态转为无效）
     */
    public void invalidate() {
        state.transitionTo(UserState.INVALID, this);
        statusHistory.add(new UserStatusHistory(UserState.INVALID, System.currentTimeMillis()));
    }


    /**
     * 离职处理
     */
    public void resign(String leaveNotes) {
        this.career = career.resign(leaveNotes);
        this.invalidate();
    }


    /**
     * 详情
     */
    public OaUserEntity detail() {
        return this.toEntity();
    }

    // ========== 查询方法（新增） ==========

    /**
     * 是否为正式员工
     */
    public boolean isFormalEmployee() {
        return this.employeeInfo != null && this.employeeInfo.isFormalEmployee();
    }

    /**
     * 是否在职
     */
    public boolean isActiveEmployee() {
        return this.employeeInfo != null && this.employeeInfo.isActive();
    }

    /**
     * 是否已转正
     */
    public boolean isRegularEmployee() {
        return this.employeeInfo != null && this.employeeInfo.isRegular();
    }

    /**
     * 是否在试用期
     */
    public boolean isInProbation() {
        return this.employeeInfo != null && this.employeeInfo.isInProbation();
    }

    /**
     * 是否有上级
     */
    public boolean hasSuperior() {
        return this.employeeInfo != null && this.employeeInfo.hasSuperior();
    }

    /**
     * 获取工作年限
     */
    public int getWorkYears() {
        return this.employeeInfo != null ? this.employeeInfo.getWorkYears() : 0;
    }

    /**
     * 是否已婚
     */
    public boolean isMarried() {
        return this.personalInfo != null && this.personalInfo.isMarried();
    }

    /**
     * 是否为党员
     */
    public boolean isPartyMember() {
        return this.personalInfo != null && this.personalInfo.isPartyMember();
    }

    /**
     * 证件是否即将过期
     */
    public boolean isIdCardExpiringSoon() {
        return this.personalInfo != null && this.personalInfo.isIdCardExpiringSoon();
    }

    /**
     * 是否可以银行转账发薪
     */
    public boolean canBankTransfer() {
        return this.bankInfo != null && this.bankInfo.canBankTransfer();
    }

    /**
     * 获取安全的银行信息
     */
    public UserBankInfo getSafeBankInfo() {
        return this.bankInfo != null ? this.bankInfo.getSafeInfo() : UserBankInfo.createDefault();
    }

    // ========== 验证方法（新增） ==========

    /**
     * 验证用户信息完整性
     */
    public boolean isUserInfoComplete() {
        return this.basicInfo != null
            && this.contact != null
            && this.employeeInfo != null && this.employeeInfo.isValid()
            && this.personalInfo != null && this.personalInfo.isValid()
            && this.bankInfo != null && this.bankInfo.isValid();
    }

    /**
     * 验证是否可以转正
     */
    public boolean canBeRegular() {
        return this.employeeInfo != null && this.employeeInfo.canBeRegular();
    }

    /**
     * 获取用户完整性评分（0-100）
     */
    public int getCompletenessScore() {
        int score = 0;

        // 基本信息 (20分)
        if (this.basicInfo != null) {
            score += 20;
        }

        // 联系信息 (20分)
        if (this.contact != null) {
            score += 20;
        }

        // 员工信息 (20分)
        if (this.employeeInfo != null && this.employeeInfo.isValid()) {
            score += 20;
        }

        // 个人信息 (20分)
        if (this.personalInfo != null && this.personalInfo.isValid()) {
            score += 20;
        }

        // 银行信息 (20分)
        if (this.bankInfo != null && this.bankInfo.isValid()) {
            score += 20;
        }

        return score;
    }

    /**
     * 编辑（重构版，支持新的值对象）
     */
    public void edit(OaUser oaUser) {
        // 保持原有的兼容性
        this.updateBasicInfo(oaUser.getBasicInfo());
        this.updateContactInfo(oaUser.getContact());

        // 新增的值对象更新
        if (oaUser.getEmployeeInfo() != null) {
            this.updateEmployeeInfo(oaUser.getEmployeeInfo());
        }
        if (oaUser.getPersonalInfo() != null) {
            this.updatePersonalInfo(oaUser.getPersonalInfo());
        }
        if (oaUser.getBankInfo() != null) {
            this.updateBankInfo(oaUser.getBankInfo());
        }
    }

    /**
     * 冻结
     */
    public void lock() {
        this.disable();
    }


    /**
     * 解冻
     */
    public void unlock() {
        this.enable();
    }


    /**
     * 验证密码
     */
    public boolean verifyPassword(String password) {
        return security.getPassword().verify(password);
    }
}

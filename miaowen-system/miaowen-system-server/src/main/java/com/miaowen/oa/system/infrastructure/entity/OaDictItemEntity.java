package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/15 14:07
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
@TableName("oa_dict_item")
public class OaDictItemEntity extends BaseDO {


    /**
     * 字典表主键id
     */
    private Long dictId;
    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 字典项名称
     */
    private String label;

    /**
     * 字典项值
     */
    private String itemValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 0-正常 1-禁用
     */
    private Integer state;


}

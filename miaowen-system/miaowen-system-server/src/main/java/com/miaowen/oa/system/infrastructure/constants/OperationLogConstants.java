package com.miaowen.oa.system.infrastructure.constants;

/**
 * 操作日志常量类
 * 
 * 功能说明：
 * 1. 定义操作日志相关的常量
 * 2. 统一管理枚举值和状态码
 * 3. 提供清晰的常量定义
 * 4. 便于维护和扩展
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
public class OperationLogConstants {

    // ========== 操作类型常量 ==========
    
    /**
     * 操作类型：查看
     */
    public static final Integer OPERATION_TYPE_VIEW = 1;
    
    /**
     * 操作类型：新增
     */
    public static final Integer OPERATION_TYPE_CREATE = 2;
    
    /**
     * 操作类型：修改
     */
    public static final Integer OPERATION_TYPE_UPDATE = 3;
    
    /**
     * 操作类型：删除
     */
    public static final Integer OPERATION_TYPE_DELETE = 4;
    
    /**
     * 操作类型：导出
     */
    public static final Integer OPERATION_TYPE_EXPORT = 5;
    
    /**
     * 操作类型：导入
     */
    public static final Integer OPERATION_TYPE_IMPORT = 6;
    
    /**
     * 操作类型：登录
     */
    public static final Integer OPERATION_TYPE_LOGIN = 7;
    
    /**
     * 操作类型：登出
     */
    public static final Integer OPERATION_TYPE_LOGOUT = 8;
    
    /**
     * 操作类型：其他
     */
    public static final Integer OPERATION_TYPE_OTHER = 9;

    // ========== 操作状态常量 ==========
    
    /**
     * 操作状态：失败
     */
    public static final Integer OPERATION_STATUS_FAILED = 0;
    
    /**
     * 操作状态：成功
     */
    public static final Integer OPERATION_STATUS_SUCCESS = 1;

    // ========== 风险等级常量 ==========
    
    /**
     * 风险等级：低风险
     */
    public static final Integer RISK_LEVEL_LOW = 1;
    
    /**
     * 风险等级：中风险
     */
    public static final Integer RISK_LEVEL_MEDIUM = 2;
    
    /**
     * 风险等级：高风险
     */
    public static final Integer RISK_LEVEL_HIGH = 3;

    // ========== 敏感操作常量 ==========
    
    /**
     * 非敏感操作
     */
    public static final Integer SENSITIVE_NO = 0;
    
    /**
     * 敏感操作
     */
    public static final Integer SENSITIVE_YES = 1;

    // ========== 操作来源常量 ==========
    
    /**
     * 操作来源：Web端
     */
    public static final Integer OPERATION_SOURCE_WEB = 1;
    
    /**
     * 操作来源：移动端
     */
    public static final Integer OPERATION_SOURCE_MOBILE = 2;
    
    /**
     * 操作来源：API调用
     */
    public static final Integer OPERATION_SOURCE_API = 3;
    
    /**
     * 操作来源：定时任务
     */
    public static final Integer OPERATION_SOURCE_SCHEDULE = 4;
    
    /**
     * 操作来源：系统内部
     */
    public static final Integer OPERATION_SOURCE_SYSTEM = 5;

    // ========== 删除状态常量 ==========
    
    /**
     * 未删除
     */
    public static final Integer DELETE_TIME_NOT_DELETED = 0;

    // ========== 数据版本常量 ==========
    
    /**
     * 默认数据版本
     */
    public static final Integer DEFAULT_DATA_VERSION = 1;

    // ========== 分页常量 ==========
    
    /**
     * 默认页码
     */
    public static final Integer DEFAULT_PAGE_NO = 1;
    
    /**
     * 默认页面大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 20;
    
    /**
     * 最大页面大小
     */
    public static final Integer MAX_PAGE_SIZE = 100;

    // ========== 导出常量 ==========
    
    /**
     * 最大导出数量
     */
    public static final Integer MAX_EXPORT_LIMIT = 10000;

    // ========== 清理常量 ==========
    
    /**
     * 默认保留天数
     */
    public static final Integer DEFAULT_RETENTION_DAYS = 90;

    // ========== 执行时间常量 ==========
    
    /**
     * 慢操作阈值（毫秒）
     */
    public static final Long SLOW_OPERATION_THRESHOLD = 3000L;
    
    /**
     * 超时操作阈值（毫秒）
     */
    public static final Long TIMEOUT_OPERATION_THRESHOLD = 10000L;

    // ========== 字符串长度限制常量 ==========
    
    /**
     * 操作描述最大长度
     */
    public static final Integer MAX_OPERATION_DESCRIPTION_LENGTH = 500;
    
    /**
     * 请求URL最大长度
     */
    public static final Integer MAX_REQUEST_URL_LENGTH = 500;
    
    /**
     * 用户代理最大长度
     */
    public static final Integer MAX_USER_AGENT_LENGTH = 1000;
    
    /**
     * 错误信息最大长度
     */
    public static final Integer MAX_ERROR_MESSAGE_LENGTH = 2000;

    // ========== 缓存相关常量 ==========
    
    /**
     * 请求体最大缓存大小（字节）
     */
    public static final Integer MAX_REQUEST_BODY_CACHE_SIZE = 10 * 1024 * 1024; // 10MB
    
    /**
     * 响应体最大缓存大小（字节）
     */
    public static final Integer MAX_RESPONSE_BODY_CACHE_SIZE = 5 * 1024 * 1024; // 5MB

    // ========== 业务模块常量 ==========
    
    /**
     * 用户管理模块
     */
    public static final String MODULE_USER = "user";
    
    /**
     * 角色管理模块
     */
    public static final String MODULE_ROLE = "role";
    
    /**
     * 菜单管理模块
     */
    public static final String MODULE_MENU = "menu";
    
    /**
     * 部门管理模块
     */
    public static final String MODULE_DEPT = "dept";
    
    /**
     * 字典管理模块
     */
    public static final String MODULE_DICT = "dict";
    
    /**
     * 配置管理模块
     */
    public static final String MODULE_CONFIG = "config";
    
    /**
     * 日志管理模块
     */
    public static final String MODULE_LOG = "log";
    
    /**
     * 文件管理模块
     */
    public static final String MODULE_FILE = "file";
    
    /**
     * 通知管理模块
     */
    public static final String MODULE_NOTICE = "notice";

    // ========== 业务类型常量 ==========
    
    /**
     * 业务类型：用户
     */
    public static final String BUSINESS_TYPE_USER = "user";
    
    /**
     * 业务类型：角色
     */
    public static final String BUSINESS_TYPE_ROLE = "role";
    
    /**
     * 业务类型：菜单
     */
    public static final String BUSINESS_TYPE_MENU = "menu";
    
    /**
     * 业务类型：部门
     */
    public static final String BUSINESS_TYPE_DEPT = "dept";
    
    /**
     * 业务类型：字典
     */
    public static final String BUSINESS_TYPE_DICT = "dict";
    
    /**
     * 业务类型：配置
     */
    public static final String BUSINESS_TYPE_CONFIG = "config";

    // ========== 敏感字段常量 ==========
    
    /**
     * 敏感字段：密码
     */
    public static final String SENSITIVE_FIELD_PASSWORD = "password";
    
    /**
     * 敏感字段：密钥
     */
    public static final String SENSITIVE_FIELD_SECRET = "secret";
    
    /**
     * 敏感字段：令牌
     */
    public static final String SENSITIVE_FIELD_TOKEN = "token";
    
    /**
     * 敏感字段：授权
     */
    public static final String SENSITIVE_FIELD_AUTH = "auth";

    // ========== 链路追踪常量 ==========
    
    /**
     * 链路追踪ID前缀
     */
    public static final String TRACE_ID_PREFIX = "OL";
    
    /**
     * 链路追踪ID长度
     */
    public static final Integer TRACE_ID_LENGTH = 21;

    // ========== 统计相关常量 ==========
    
    /**
     * 用户活跃度排行榜数量
     */
    public static final Integer USER_ACTIVITY_RANKING_LIMIT = 10;
    
    /**
     * 慢操作排行榜数量
     */
    public static final Integer SLOW_OPERATION_RANKING_LIMIT = 10;
    
    /**
     * 异常操作排行榜数量
     */
    public static final Integer EXCEPTION_OPERATION_RANKING_LIMIT = 10;

    // ========== 私有构造函数 ==========
    
    private OperationLogConstants() {
        // 防止实例化
    }
}

package com.miaowen.oa.system.domain.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.framework.common.util.stream.StreamUtil;
import com.miaowen.oa.system.domain.service.OaJobService;
import com.miaowen.oa.system.infrastructure.entity.*;
import com.miaowen.oa.system.infrastructure.mapper.OaJobMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaUserJobRelationMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaUserMapper;
import com.miaowen.oa.system.interfaces.req.job.JobPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.job.JobSaveRequest;
import com.miaowen.oa.system.interfaces.req.job.JobUpdateRequest;
import com.miaowen.oa.system.interfaces.res.job.OaJobDetailResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobPageRsep;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.miaowen.oa.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.DEPT_CODE_DUPLICATE;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.POST_CODE_DUPLICATE;

/**
 * <AUTHOR>
 * @Date 2025/7/15 10:16
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Service
@AllArgsConstructor
public class OaJobServiceImpl extends ServiceImpl<OaJobMapper, OaJobEntity> implements OaJobService {

    private OaUserJobRelationMapper oaUserJobRelationMapper;

    private OaUserMapper oaUserMapper;


    @Override
    public void addJob(JobSaveRequest jobSaveRequest) {
        validateDeptCodeUnique(jobSaveRequest.getJobCode());
        OaJobEntity oaJobEntity = BeanUtils.toBean(jobSaveRequest, OaJobEntity.class);
        baseMapper.insert(oaJobEntity);
    }

    @Override
    public void updateJob(JobUpdateRequest jobUpdateRequest) {
        OaJobEntity oaJobEntity = BeanUtils.toBean(jobUpdateRequest, OaJobEntity.class);
        baseMapper.updateById(oaJobEntity);
    }

    @Override
    public PageResult<OaJobPageRsep> getJobPageList(JobPageQueryRequest jobPageQueryRequest) {
        IPage<OaJobEntity> iPage = new Page<>(jobPageQueryRequest.getPage(), jobPageQueryRequest.getPageSize());

        IPage<OaJobEntity> oaJobEntityIPage = baseMapper.selectPage(iPage, Wrappers.lambdaQuery(OaJobEntity.class).eq(OaJobEntity::getDeleteTime, 0).like(StringUtils.hasText(jobPageQueryRequest.getJobName()), OaJobEntity::getJobName, jobPageQueryRequest.getJobName())
                .orderByAsc(OaJobEntity::getSort)
                .orderByDesc(OaJobEntity::getUpdateTime)
        );
        List<OaJobEntity> oaJobEntityList = oaJobEntityIPage.getRecords();
        List<OaJobPageRsep> oaJobPageRsepList = oaJobEntityList.stream().map(oaJobEntity -> BeanUtils.toBean(oaJobEntity, OaJobPageRsep.class)).toList();
        //查询所有岗位人员关联数据
        List<OaUserJobRelationEntity> userJobRelationEntityList = oaUserJobRelationMapper.selectList(Wrappers.lambdaQuery(OaUserJobRelationEntity.class).eq(OaUserJobRelationEntity::getDeleteTime, 0));
        Map<Long, List<OaUserJobRelationEntity>> relMap = StreamUtil.groupingToList(userJobRelationEntityList, OaUserJobRelationEntity::getJobId, Function.identity());
        //查询所有用户
        List<OaUserEntity> oaUserList = oaUserMapper.selectList(Wrappers.lambdaQuery(OaUserEntity.class).eq(OaUserEntity::getDeleteTime, 0));
        Map<Long, OaUserEntity> userMap = StreamUtil.map(oaUserList, OaUserEntity::getId);
        for (OaJobPageRsep oaJobPageRsep : oaJobPageRsepList) {
            if (oaJobPageRsep.getUpdater() != null) {
                OaUserEntity updaterUser = userMap.get(oaJobPageRsep.getUpdater());
                if (updaterUser != null) {
                    oaJobPageRsep.setUpdaterName(updaterUser.getRealName());
                }
            }
            //计算岗位人数
            List<OaUserJobRelationEntity> oaUserJobRelationEntityList = relMap.getOrDefault(oaJobPageRsep.getId(), Collections.emptyList());
            int userCount = oaUserJobRelationEntityList.stream().map(OaUserJobRelationEntity::getUserId).distinct().toList().size();
            oaJobPageRsep.setUserCount(userCount);
        }

        return new PageResult<>(oaJobPageRsepList, oaJobEntityIPage.getTotal());
    }

    @Override
    public OaJobDetailResp getJobById(Long id) {
        OaJobEntity oaJobEntity = baseMapper.selectOne(Wrappers.lambdaQuery(OaJobEntity.class).eq(OaJobEntity::getId, id).eq(OaJobEntity::getDeleteTime, 0));
        OaJobDetailResp oaJobDetailResp = BeanUtils.toBean(oaJobEntity, OaJobDetailResp.class);
        Long creatorId = oaJobEntity.getCreator();
        List<OaUserEntity> oaUserList = oaUserMapper.selectList(Wrappers.lambdaQuery(OaUserEntity.class).eq(OaUserEntity::getDeleteTime, 0));
        Map<Long, OaUserEntity> userMap = StreamUtil.map(oaUserList, OaUserEntity::getId);
        if (userMap.containsKey(creatorId)) {
            OaUserEntity oaUserEntity = userMap.get(creatorId);
            if (oaUserEntity != null) {
                oaJobDetailResp.setCreatorName(oaUserEntity.getRealName());
            }
        }
        return oaJobDetailResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeById(Long id) {
        // 1.删除岗位
        baseMapper.logicDeleteById(id);
        // 2.删除岗位关联的用户
        oaUserJobRelationMapper.logicDelete(Wrappers.lambdaQuery(OaUserJobRelationEntity.class).eq(OaUserJobRelationEntity::getJobId, id));
    }

    private void validateDeptCodeUnique(String code) {
        if (StrUtil.isEmpty(code)) {
            return;
        }
        OaJobEntity oaJobEntity = baseMapper.selectOne(Wrappers.lambdaQuery(OaJobEntity.class).eq(OaJobEntity::getJobCode, code).eq(OaJobEntity::getDeleteTime, 0));

        if (oaJobEntity != null) {
            throw exception(POST_CODE_DUPLICATE);
        }

    }
}

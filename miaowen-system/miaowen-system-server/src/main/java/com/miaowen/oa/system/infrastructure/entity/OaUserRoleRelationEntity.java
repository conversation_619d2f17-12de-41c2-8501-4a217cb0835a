package com.miaowen.oa.system.infrastructure.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value ="oa_user_role_relation")
public class OaUserRoleRelationEntity  extends BaseDO {


    /**
     * 用户id
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;



}

package com.miaowen.oa.system.interfaces.req.log;

import com.miaowen.oa.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * 操作日志分页查询请求
 * 
 * 功能说明：
 * 1. 支持多条件筛选查询
 * 2. 支持分页参数设置
 * 3. 支持时间范围查询
 * 4. 支持关键字模糊搜索
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "操作日志分页查询请求")
public class OperationLogPageRequest extends PageParam {


    // ========== 筛选条件 ==========
    
    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "操作类型", example = "2", 
            allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8", "9"})
    private Integer operationType;

    @Schema(description = "操作模块", example = "user")
    private String operationModule;

    @Schema(description = "操作功能", example = "创建用户")
    private String operationFunction;

    @Schema(description = "操作状态", example = "1", allowableValues = {"0", "1"})
    private Integer operationState;

    @Schema(description = "业务类型", example = "user")
    private String businessType;

    @Schema(description = "业务ID", example = "1001")
    private Long businessId;

    @Schema(description = "风险等级", example = "2", allowableValues = {"1", "2", "3"})
    private Integer riskLevel;

    @Schema(description = "是否敏感操作", example = "0", allowableValues = {"0", "1"})
    private Integer isSensitive;

    @Schema(description = "操作来源", example = "1", allowableValues = {"1", "2", "3", "4", "5"})
    private Integer operationSource;

    @Schema(description = "客户端IP", example = "*************")
    private String clientIp;

    // ========== 时间范围 ==========
    
    @Schema(description = "开始时间", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2025-01-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    // ========== 搜索条件 ==========
    
    @Schema(description = "关键字搜索", example = "用户管理")
    private String keyword;

    @Schema(description = "链路追踪ID", example = "OL1642752000000123456")
    private String traceId;

    // ========== 排序条件 ==========
    
    @Schema(description = "排序字段", example = "createTime", 
            allowableValues = {"createTime", "executionTime", "operationType", "riskLevel"})
    private String sortField;

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder;

    // ========== 高级筛选 ==========
    
    @Schema(description = "最小执行时间(毫秒)", example = "1000")
    private Long minExecutionTime;

    @Schema(description = "最大执行时间(毫秒)", example = "5000")
    private Long maxExecutionTime;

    @Schema(description = "是否包含数据变更", example = "true")
    private Boolean hasDataChange;

    @Schema(description = "是否包含异常", example = "false")
    private Boolean hasException;

    // ========== 辅助方法 ==========

    /**
     * 获取有效的排序字段
     */
    public String getSortField() {
        if (sortField == null || sortField.trim().isEmpty()) {
            return "createTime";
        }
        return sortField;
    }

    /**
     * 获取有效的排序方向
     */
    public String getSortOrder() {
        if (sortOrder == null || sortOrder.trim().isEmpty()) {
            return "desc";
        }
        return sortOrder.toLowerCase();
    }

    /**
     * 是否有时间范围筛选
     */
    public boolean hasTimeRange() {
        return startTime != null || endTime != null;
    }

    /**
     * 是否有用户筛选
     */
    public boolean hasUserFilter() {
        return userId != null || (username != null && !username.trim().isEmpty());
    }

    /**
     * 是否有业务筛选
     */
    public boolean hasBusinessFilter() {
        return (businessType != null && !businessType.trim().isEmpty()) || businessId != null;
    }

    /**
     * 是否有关键字搜索
     */
    public boolean hasKeywordSearch() {
        return keyword != null && !keyword.trim().isEmpty();
    }

    /**
     * 是否有执行时间筛选
     */
    public boolean hasExecutionTimeFilter() {
        return minExecutionTime != null || maxExecutionTime != null;
    }

    /**
     * 验证时间范围是否合理
     */
    public boolean isValidTimeRange() {
        if (startTime == null || endTime == null) {
            return true;
        }
        return !startTime.isAfter(endTime);
    }

    /**
     * 验证执行时间范围是否合理
     */
    public boolean isValidExecutionTimeRange() {
        if (minExecutionTime == null || maxExecutionTime == null) {
            return true;
        }
        return minExecutionTime <= maxExecutionTime;
    }
}

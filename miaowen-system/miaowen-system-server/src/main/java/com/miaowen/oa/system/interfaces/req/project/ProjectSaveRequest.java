package com.miaowen.oa.system.interfaces.req.project;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/18 14:53
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class ProjectSaveRequest {

    /**
     * 项目名字
     */
    @NotNull(message = "项目名不能为空")
    private String projectName;

    /**
     * 项目编码
     */
    @NotNull(message = "项目编码不能为空")
    private String projectCode;

    /**
     * 板块
     */
    @NotNull(message = "板块不能为空")
    private Integer plate;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 接口域名
     */
    @NotNull(message = "接口域名不能为空")
    private String apiDomain;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     */
    private Integer state;

}

package com.miaowen.oa.system.interfaces.req.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
/**
 * <AUTHOR>
 * @Date 2025/7/11 19:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@Schema(description = "更新角色请求")
public class RoleUpdateRequest {

    @NotNull(message = "角色id不能为空")
    private Long id;

    @NotNull(message = "上级分组ID不能为空")
    @Schema(description = "上级分组ID (0表示根节点)", example = "0")
    private Long roleGroupId;

    @NotBlank(message = "角色名称不能为空")
    @Schema(description = "角色名称", example = "管理员")
    private String roleName;


    @Schema(description = "描述", example = "test")
    private String description;

    @Schema(description = "排序", example = "10001")
    private Integer sort;
}
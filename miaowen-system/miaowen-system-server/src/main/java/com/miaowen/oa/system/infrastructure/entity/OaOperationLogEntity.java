package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志实体类
 * 
 * 功能说明：
 * 1. 对应数据库表 oa_operation_log
 * 2. 记录用户的查看、新增、修改、删除等操作
 * 3. 记录操作前后的数据变化
 * 4. 支持日志数据的筛选和查询
 * 5. 字段映射与数据库表结构完全匹配
 * 
 * 注意事项：
 * - 实体类字段名与数据库字段名保持一致
 * - 使用@TableField注解处理特殊字段映射
 * - 遵循MyBatis-Plus规范
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_operation_log")
public class OaOperationLogEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 操作用户名
     */
    @TableField("username")
    private String username;
    
    /**
     * 操作类型
     * 1-查看 2-新增 3-修改 4-删除 5-导出 6-导入 7-登录 8-登出 9-其他
     */
    @TableField("operation_type")
    private Integer operationType;
    
    /**
     * 操作模块
     */
    @TableField("operation_module")
    private String operationModule;
    
    /**
     * 操作功能
     */
    @TableField("operation_function")
    private String operationFunction;
    
    /**
     * 操作状态
     * 0-失败 1-成功
     * 注意：数据库字段名为operation_state
     */
    @TableField("operation_state")
    private Integer operationState;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 业务数据ID
     */
    @TableField("business_id")
    private Long businessId;
    
    /**
     * 业务数据类型
     */
    @TableField("business_type")
    private String businessType;
    
    /**
     * 操作描述
     */
    @TableField("operation_description")
    private String operationDescription;
    
    /**
     * 操作耗时(毫秒)
     */
    @TableField("execution_time")
    private Long executionTime;
    
    /**
     * 操作前数据(JSON格式)
     */
    @TableField("before_data")
    private String beforeData;
    
    /**
     * 操作后数据(JSON格式)
     */
    @TableField("after_data")
    private String afterData;
    
    /**
     * 变更字段信息(JSON格式)
     */
    @TableField("changed_fields")
    private String changedFields;
    
    /**
     * 数据版本
     */
    @TableField("data_version")
    private Integer dataVersion;
    
    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;
    
    /**
     * 请求URL
     */
    @TableField("request_url")
    private String requestUrl;
    
    /**
     * 请求参数(JSON格式)
     */
    @TableField("request_params")
    private String requestParams;
    
    /**
     * 响应结果
     */
    @TableField("response_result")
    private String responseResult;
    
    /**
     * 客户端IP地址
     */
    @TableField("client_ip")
    private String clientIp;
    
    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;
    
    /**
     * 链路追踪ID
     */
    @TableField("trace_id")
    private String traceId;
    
    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 操作来源
     * 1-Web端 2-移动端 3-API调用 4-定时任务 5-系统内部
     */
    @TableField("operation_source")
    private Integer operationSource;
    
    /**
     * 风险等级
     * 1-低风险 2-中风险 3-高风险
     */
    @TableField("risk_level")
    private Integer riskLevel;
    
    /**
     * 是否敏感操作
     * 0-否 1-是
     */
    @TableField("is_sensitive")
    private Integer isSensitive;
    
    /**
     * 审计标记
     */
    @TableField("audit_flag")
    private String auditFlag;
    
    /**
     * 扩展字段(JSON格式)
     */
    @TableField("extra_data")
    private String extraData;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    @TableField("creator")
    private Long creator;
    
    /**
     * 更新者
     */
    @TableField("updater")
    private Long updater;
    
    /**
     * 删除时间(0表示未删除)
     */
    @TableField("delete_time")
    private Integer deleteTime;

    // ========== 辅助方法 ==========

    /**
     * 是否操作成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return operationState != null && operationState == 1;
    }

    /**
     * 是否有异常
     * 
     * @return 是否有异常
     */
    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    /**
     * 是否有数据变更
     * 
     * @return 是否有数据变更
     */
    public boolean hasDataChange() {
        return (beforeData != null && !beforeData.trim().isEmpty()) || 
               (afterData != null && !afterData.trim().isEmpty());
    }

    /**
     * 是否为读操作
     * 
     * @return 是否为读操作
     */
    public boolean isReadOperation() {
        return operationType != null && (operationType == 1 || operationType == 5);
    }

    /**
     * 是否为写操作
     * 
     * @return 是否为写操作
     */
    public boolean isWriteOperation() {
        return operationType != null && (operationType == 2 || operationType == 3 || operationType == 4);
    }

    /**
     * 是否为高风险操作
     * 
     * @return 是否为高风险操作
     */
    public boolean isHighRisk() {
        return riskLevel != null && riskLevel >= 3;
    }

    /**
     * 是否为敏感操作
     * 
     * @return 是否为敏感操作
     */
    public boolean isSensitiveOperation() {
        return isSensitive != null && isSensitive == 1;
    }

    /**
     * 是否已删除
     * 
     * @return 是否已删除
     */
    public boolean isDeleted() {
        return deleteTime != null && deleteTime > 0;
    }

    /**
     * 获取操作类型名称
     * 
     * @return 操作类型名称
     */
    public String getOperationTypeName() {
        if (operationType == null) {
            return "未知";
        }
        switch (operationType) {
            case 1: return "查看";
            case 2: return "新增";
            case 3: return "修改";
            case 4: return "删除";
            case 5: return "导出";
            case 6: return "导入";
            case 7: return "登录";
            case 8: return "登出";
            case 9: return "其他";
            default: return "未知";
        }
    }

    /**
     * 获取风险等级名称
     * 
     * @return 风险等级名称
     */
    public String getRiskLevelName() {
        if (riskLevel == null) {
            return "未知";
        }
        switch (riskLevel) {
            case 1: return "低风险";
            case 2: return "中风险";
            case 3: return "高风险";
            default: return "未知";
        }
    }

    /**
     * 获取操作来源名称
     * 
     * @return 操作来源名称
     */
    public String getOperationSourceName() {
        if (operationSource == null) {
            return "未知";
        }
        switch (operationSource) {
            case 1: return "Web端";
            case 2: return "移动端";
            case 3: return "API调用";
            case 4: return "定时任务";
            case 5: return "系统内部";
            default: return "未知";
        }
    }

    /**
     * 获取执行时间描述
     * 
     * @return 执行时间描述
     */
    public String getExecutionTimeDesc() {
        if (executionTime == null) {
            return "未知";
        }
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else if (executionTime < 60000) {
            return String.format("%.2fs", executionTime / 1000.0);
        } else {
            return String.format("%.2fmin", executionTime / 60000.0);
        }
    }
}

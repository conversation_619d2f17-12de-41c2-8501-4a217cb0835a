package com.miaowen.oa.system.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.domain.service.OaDictItemService;
import com.miaowen.oa.system.interfaces.req.dict.DictItemBatchSaveRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictItemUpdateRequest;
import com.miaowen.oa.system.interfaces.res.dict.DictItemDetailResp;
import com.miaowen.oa.system.interfaces.res.dict.DictItemResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.miaowen.oa.framework.common.pojo.CommonResult.success;

/**
 * 字典值管理
 * <AUTHOR>
 * @Date 2025/7/15 15:03
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Tag(name = "字典值管理")
@Slf4j
@RequestMapping("/dict-item")
@RestController
@AllArgsConstructor
public class OaDictItemController {

    @Resource
    private OaDictItemService oaDictItemService;

    @PostMapping
    @Operation(summary = "批量新增字典值")
    public CommonResult<Void> batchCreateDictItem(@Valid @RequestBody DictItemBatchSaveRequest dictItemBatchSaveRequest) {
        oaDictItemService.batchCreateDictItem(dictItemBatchSaveRequest);
        return success(null);
    }

    @PutMapping
    @Operation(summary = "修改字典")
    public CommonResult<Void> updateDictItem(@Valid @RequestBody DictItemUpdateRequest dictItemUpdateRequest) {
        oaDictItemService.updateDictItem(dictItemUpdateRequest);
        return success(null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除字典")
    public CommonResult<Boolean> deleteDictData(@PathVariable("id") Long id) {
        oaDictItemService.deleteDictItem(id);
        return success(null);
    }

    @GetMapping
    @Operation(summary = "根据字典id，查询字典值")
    public CommonResult<List<DictItemResp>> getDictItemList(@RequestParam("dictId") Long dictId) {
        List<DictItemResp> dictItemRespList = oaDictItemService.getDictItemList(dictId);
        return success(dictItemRespList);
    }

    @GetMapping("/{id}")
    @Operation(summary = "字典值详情接口")
    public CommonResult<DictItemDetailResp> getDictItemDetail(@PathVariable("id") Long id) {
        DictItemDetailResp oaDictDetailResp = oaDictItemService.getDictItemDetail(id);
        return success(oaDictDetailResp);
    }


}

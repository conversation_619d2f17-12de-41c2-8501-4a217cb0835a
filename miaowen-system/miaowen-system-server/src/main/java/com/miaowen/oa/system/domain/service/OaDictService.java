package com.miaowen.oa.system.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.infrastructure.entity.OaDictEntity;
import com.miaowen.oa.system.interfaces.req.dict.DictPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictSaveRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictUpdateRequest;
import com.miaowen.oa.system.interfaces.res.dict.OaDictDetailResp;
import com.miaowen.oa.system.interfaces.res.dict.OaDictPageResp;

/**
 * <AUTHOR>
 * @Date 2025/7/15 14:55
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
public interface OaDictService extends IService<OaDictEntity> {
    void createDict(DictSaveRequest dictSaveRequest);

    void updateDict(DictUpdateRequest dictUpdateRequest);

    void deleteDict(Long id);

    PageResult<OaDictPageResp> getDictPage(DictPageQueryRequest dictPageQueryRequest);

    OaDictDetailResp getDictDetail(Long id);
}

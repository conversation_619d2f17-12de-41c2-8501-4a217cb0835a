package com.miaowen.oa.system.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.Period;
import java.util.Objects;

/**
 * 用户个人信息值对象
 *
 * 严格按照OaUserEntity中的实际字段设计，不添加不存在的字段
 *
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode
public class UserPersonalInfo {

    // ========== 基本个人信息（对应OaUserEntity实际字段） ==========

    /**
     * 出生日期
     * 对应OaUserEntity.birthday
     */
    private final LocalDate birthday;

    /**
     * 民族
     * 对应OaUserEntity.ethnicity
     */
    private final String ethnicity;

    /**
     * 政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众
     * 对应OaUserEntity.politicalState（注意是State不是Status）
     */
    private final Integer politicalState;

    /**
     * 婚姻状况（当前）：1-未婚 2-已婚 3-离异 4-丧偶
     * 对应OaUserEntity.maritalState（注意是State不是Status）
     */
    private final Integer maritalState;

    /**
     * 最高学历（当前）：1-小学 2-初中 3-高中/中专 4-大专 5-本科 6-硕士 7-博士
     * 对应OaUserEntity.education
     */
    private final Integer education;

    /**
     * 毕业院校（当前）
     * 对应OaUserEntity.graduateSchool
     */
    private final String graduateSchool;

    /**
     * 专业（当前）
     * 对应OaUserEntity.major
     */
    private final String major;

    // ========== 构造函数 ==========

    /**
     * 完整构造函数（严格按照OaUserEntity字段）
     */
    public UserPersonalInfo(LocalDate birthday, String ethnicity, Integer politicalState,
                           Integer maritalState, Integer education, String graduateSchool, String major) {
        this.birthday = birthday;
        this.ethnicity = ethnicity;
        this.politicalState = politicalState;
        this.maritalState = maritalState;
        this.education = education;
        this.graduateSchool = graduateSchool;
        this.major = major;
    }

    // ========== 工厂方法 ==========

    /**
     * 创建默认个人信息
     */
    public static UserPersonalInfo createDefault() {
        return new UserPersonalInfo(
            null,         // 默认生日
            "",           // 默认民族
            6,            // 默认群众
            1,            // 默认未婚
            0,            // 默认学历
            "",           // 默认毕业院校
            ""            // 默认专业
        );
    }

    /**
     * 创建基础个人信息
     */
    public static UserPersonalInfo createBasic(LocalDate birthday, String ethnicity, Integer education) {
        return new UserPersonalInfo(
            birthday,
            ethnicity,
            6,            // 默认群众
            1,            // 默认未婚
            education,
            "",           // 默认毕业院校
            ""            // 默认专业
        );
    }

    // ========== 业务方法 ==========

    /**
     * 是否已婚
     * 婚姻状况：1-未婚 2-已婚 3-离异 4-丧偶
     */
    public boolean isMarried() {
        return Objects.equals(this.maritalState, 2);
    }

    /**
     * 是否为党员
     * 政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众
     */
    public boolean isPartyMember() {
        return Objects.equals(this.politicalState, 1);
    }

    /**
     * 是否为预备党员
     */
    public boolean isPrePartyMember() {
        return Objects.equals(this.politicalState, 2);
    }

    /**
     * 是否为团员
     */
    public boolean isLeagueMember() {
        return Objects.equals(this.politicalState, 3);
    }

    /**
     * 是否为群众
     */
    public boolean isMass() {
        return Objects.equals(this.politicalState, 6);
    }

    /**
     * 获取婚姻状况描述
     */
    public String getMaritalStateDescription() {
        switch (this.maritalState) {
            case 1: return "未婚";
            case 2: return "已婚";
            case 3: return "离异";
            case 4: return "丧偶";
            default: return "未知";
        }
    }

    /**
     * 获取政治面貌描述
     */
    public String getPoliticalStateDescription() {
        switch (this.politicalState) {
            case 1: return "中共党员";
            case 2: return "中共预备党员";
            case 3: return "共青团员";
            case 4: return "民主党派";
            case 5: return "无党派人士";
            case 6: return "群众";
            default: return "未知";
        }
    }

    /**
     * 获取学历描述
     */
    public String getEducationDescription() {
        switch (this.education) {
            case 1: return "小学";
            case 2: return "初中";
            case 3: return "高中/中专";
            case 4: return "大专";
            case 5: return "本科";
            case 6: return "硕士";
            case 7: return "博士";
            default: return "未知";
        }
    }
    

}

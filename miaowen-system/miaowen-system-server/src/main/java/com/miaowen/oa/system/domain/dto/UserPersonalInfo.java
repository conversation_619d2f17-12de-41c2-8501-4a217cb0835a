package com.miaowen.oa.system.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.Period;
import java.util.Objects;

/**
 * 用户个人信息值对象
 * 
 * 包含用户的个人身份、证件、户籍等详细信息
 * 根据OaUserEntity中的个人信息相关字段设计
 * 
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode
public class UserPersonalInfo {

    // ========== 基本个人信息 ==========
    
    /**
     * 生日（时间戳）
     */
    private final Long birthday;
    
    /**
     * 年龄
     */
    private final Integer age;
    
    /**
     * 国籍
     */
    private final String nationality;
    
    /**
     * 民族
     */
    private final String ethnicity;
    
    /**
     * 婚姻状况
     * 1-未婚，2-已婚，3-离异，4-丧偶
     */
    private final Integer maritalStatus;
    
    /**
     * 政治面貌
     * 1-群众，2-团员，3-党员，4-民主党派，5-其他
     */
    private final Integer politicalStatus;

    // ========== 证件信息 ==========
    
    /**
     * 证件类型
     * 1-身份证，2-护照，3-军官证，4-其他
     */
    private final Integer idCardType;
    
    /**
     * 证件号码
     */
    private final String idCardNumber;
    
    /**
     * 证件签发日期（时间戳）
     */
    private final Long idCardIssueDate;
    
    /**
     * 证件有效期（时间戳）
     */
    private final Long idCardExpiryDate;
    
    /**
     * 证件签发机关
     */
    private final String idCardIssueAuthority;

    // ========== 户籍信息 ==========
    
    /**
     * 户籍所在地
     */
    private final String householdRegistration;
    
    /**
     * 现居住地址
     */
    private final String residenceAddress;

    // ========== 构造函数 ==========
    
    /**
     * 完整构造函数
     */
    public UserPersonalInfo(Long birthday, Integer age, String nationality, String ethnicity,
                           Integer maritalStatus, Integer politicalStatus, Integer idCardType,
                           String idCardNumber, Long idCardIssueDate, Long idCardExpiryDate,
                           String idCardIssueAuthority, String householdRegistration, String residenceAddress) {
        this.birthday = birthday;
        this.age = age;
        this.nationality = Objects.requireNonNull(nationality, "国籍不能为空");
        this.ethnicity = Objects.requireNonNull(ethnicity, "民族不能为空");
        this.maritalStatus = maritalStatus;
        this.politicalStatus = politicalStatus;
        this.idCardType = idCardType;
        this.idCardNumber = Objects.requireNonNull(idCardNumber, "证件号码不能为空");
        this.idCardIssueDate = idCardIssueDate;
        this.idCardExpiryDate = idCardExpiryDate;
        this.idCardIssueAuthority = Objects.requireNonNull(idCardIssueAuthority, "证件签发机关不能为空");
        this.householdRegistration = Objects.requireNonNull(householdRegistration, "户籍所在地不能为空");
        this.residenceAddress = Objects.requireNonNull(residenceAddress, "现居住地址不能为空");
    }

    // ========== 工厂方法 ==========
    
    /**
     * 创建默认个人信息
     */
    public static UserPersonalInfo createDefault() {
        return new UserPersonalInfo(
            0L,           // 默认生日
            0,            // 默认年龄
            "中国",        // 默认国籍
            "",           // 默认民族
            1,            // 默认未婚
            1,            // 默认群众
            1,            // 默认身份证
            "",           // 默认证件号码
            0L,           // 默认签发日期
            0L,           // 默认有效期
            "",           // 默认签发机关
            "",           // 默认户籍
            ""            // 默认居住地址
        );
    }
    
    /**
     * 创建基础个人信息
     */
    public static UserPersonalInfo createBasic(Long birthday, String nationality, String ethnicity,
                                              String idCardNumber, String householdRegistration) {
        // 根据生日计算年龄
        Integer age = calculateAge(birthday);
        
        return new UserPersonalInfo(
            birthday,
            age,
            nationality,
            ethnicity,
            1,            // 默认未婚
            1,            // 默认群众
            1,            // 默认身份证
            idCardNumber,
            0L,           // 默认签发日期
            0L,           // 默认有效期
            "",           // 默认签发机关
            householdRegistration,
            ""            // 默认居住地址
        );
    }

    // ========== 业务方法 ==========
    
    /**
     * 是否已婚
     */
    public boolean isMarried() {
        return Objects.equals(this.maritalStatus, 2);
    }
    
    /**
     * 是否为党员
     */
    public boolean isPartyMember() {
        return Objects.equals(this.politicalStatus, 3);
    }
    
    /**
     * 是否为团员
     */
    public boolean isLeagueMember() {
        return Objects.equals(this.politicalStatus, 2);
    }
    
    /**
     * 证件是否即将过期（30天内）
     */
    public boolean isIdCardExpiringSoon() {
        if (this.idCardExpiryDate == null || this.idCardExpiryDate <= 0) {
            return false;
        }
        long currentTime = System.currentTimeMillis();
        long thirtyDaysInMillis = 30L * 24 * 60 * 60 * 1000;
        return (this.idCardExpiryDate - currentTime) <= thirtyDaysInMillis;
    }
    
    /**
     * 证件是否已过期
     */
    public boolean isIdCardExpired() {
        if (this.idCardExpiryDate == null || this.idCardExpiryDate <= 0) {
            return false;
        }
        return System.currentTimeMillis() > this.idCardExpiryDate;
    }
    
    /**
     * 获取当前年龄（根据生日实时计算）
     */
    public int getCurrentAge() {
        if (this.birthday == null || this.birthday <= 0) {
            return this.age != null ? this.age : 0;
        }
        return calculateAge(this.birthday);
    }
    
    /**
     * 获取婚姻状况描述
     */
    public String getMaritalStatusDescription() {
        switch (this.maritalStatus) {
            case 1: return "未婚";
            case 2: return "已婚";
            case 3: return "离异";
            case 4: return "丧偶";
            default: return "未知";
        }
    }
    
    /**
     * 获取政治面貌描述
     */
    public String getPoliticalStatusDescription() {
        switch (this.politicalStatus) {
            case 1: return "群众";
            case 2: return "团员";
            case 3: return "党员";
            case 4: return "民主党派";
            case 5: return "其他";
            default: return "未知";
        }
    }
    
    /**
     * 获取证件类型描述
     */
    public String getIdCardTypeDescription() {
        switch (this.idCardType) {
            case 1: return "身份证";
            case 2: return "护照";
            case 3: return "军官证";
            case 4: return "其他";
            default: return "未知";
        }
    }
    
    /**
     * 从身份证号码提取性别
     */
    public Integer getGenderFromIdCard() {
        if (this.idCardNumber == null || this.idCardNumber.length() < 17) {
            return null;
        }
        try {
            // 身份证第17位数字，奇数为男性，偶数为女性
            int genderDigit = Integer.parseInt(this.idCardNumber.substring(16, 17));
            return genderDigit % 2 == 1 ? 1 : 2; // 1-男，2-女
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 从身份证号码提取出生日期
     */
    public Long getBirthdayFromIdCard() {
        if (this.idCardNumber == null || this.idCardNumber.length() < 14) {
            return null;
        }
        try {
            String birthStr = this.idCardNumber.substring(6, 14);
            int year = Integer.parseInt(birthStr.substring(0, 4));
            int month = Integer.parseInt(birthStr.substring(4, 6));
            int day = Integer.parseInt(birthStr.substring(6, 8));
            
            LocalDate birthDate = LocalDate.of(year, month, day);
            return birthDate.toEpochDay() * 24 * 60 * 60 * 1000; // 转换为时间戳
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 验证身份证号码格式
     */
    public boolean isValidIdCard() {
        if (this.idCardNumber == null || this.idCardNumber.length() != 18) {
            return false;
        }
        
        // 简单的身份证号码格式验证
        String regex = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        return this.idCardNumber.matches(regex);
    }

    // ========== 更新方法 ==========
    
    /**
     * 更新婚姻状况
     */
    public UserPersonalInfo updateMaritalStatus(Integer maritalStatus) {
        return new UserPersonalInfo(
            this.birthday,
            this.age,
            this.nationality,
            this.ethnicity,
            maritalStatus,
            this.politicalStatus,
            this.idCardType,
            this.idCardNumber,
            this.idCardIssueDate,
            this.idCardExpiryDate,
            this.idCardIssueAuthority,
            this.householdRegistration,
            this.residenceAddress
        );
    }
    
    /**
     * 更新政治面貌
     */
    public UserPersonalInfo updatePoliticalStatus(Integer politicalStatus) {
        return new UserPersonalInfo(
            this.birthday,
            this.age,
            this.nationality,
            this.ethnicity,
            this.maritalStatus,
            politicalStatus,
            this.idCardType,
            this.idCardNumber,
            this.idCardIssueDate,
            this.idCardExpiryDate,
            this.idCardIssueAuthority,
            this.householdRegistration,
            this.residenceAddress
        );
    }
    
    /**
     * 更新证件信息
     */
    public UserPersonalInfo updateIdCard(Integer idCardType, String idCardNumber,
                                        Long idCardIssueDate, Long idCardExpiryDate,
                                        String idCardIssueAuthority) {
        return new UserPersonalInfo(
            this.birthday,
            this.age,
            this.nationality,
            this.ethnicity,
            this.maritalStatus,
            this.politicalStatus,
            idCardType,
            idCardNumber,
            idCardIssueDate,
            idCardExpiryDate,
            idCardIssueAuthority,
            this.householdRegistration,
            this.residenceAddress
        );
    }
    
    /**
     * 更新地址信息
     */
    public UserPersonalInfo updateAddress(String householdRegistration, String residenceAddress) {
        return new UserPersonalInfo(
            this.birthday,
            this.age,
            this.nationality,
            this.ethnicity,
            this.maritalStatus,
            this.politicalStatus,
            this.idCardType,
            this.idCardNumber,
            this.idCardIssueDate,
            this.idCardExpiryDate,
            this.idCardIssueAuthority,
            householdRegistration,
            residenceAddress
        );
    }

    // ========== 静态工具方法 ==========
    
    /**
     * 根据生日计算年龄
     */
    private static Integer calculateAge(Long birthday) {
        if (birthday == null || birthday <= 0) {
            return 0;
        }
        
        try {
            LocalDate birthDate = LocalDate.ofEpochDay(birthday / (24 * 60 * 60 * 1000));
            LocalDate currentDate = LocalDate.now();
            return Period.between(birthDate, currentDate).getYears();
        } catch (Exception e) {
            return 0;
        }
    }

    // ========== 验证方法 ==========
    
    /**
     * 验证个人信息是否有效
     */
    public boolean isValid() {
        return this.nationality != null && !this.nationality.trim().isEmpty()
            && this.ethnicity != null && !this.ethnicity.trim().isEmpty()
            && this.idCardNumber != null && !this.idCardNumber.trim().isEmpty()
            && this.isValidIdCard();
    }
}

package com.miaowen.oa.system.domain.dto;



import com.miaowen.oa.system.domain.model.OaUser;
import lombok.Getter;


/**
 * <AUTHOR>
 * @Date 2025/7/14 15:14
 * @Company 武汉妙闻网络科技有限公司
 * @Description 用户状态枚举（领域对象）
 */
@Getter
public enum UserState {
    INVALID(0, "无效") {
        @Override
        public boolean canTransitionTo(UserState newState, OaUser user) {
            return newState == ACTIVE;
        }
    },
    ACTIVE(1, "正常") {
        @Override
        public boolean canTransitionTo(UserState newState, OaUser user) {
            return newState == DISABLED || newState == INVALID;
        }
    },
    DISABLED(2, "禁用") {
        @Override
        public boolean canTransitionTo(UserState newState, OaUser user) {
            return newState == ACTIVE || newState == INVALID;
        }
    };

    private final int code;

    private final String description;

    UserState(int code, String description) {
        this.code = code;
        this.description = description;
    }


    /**
     * 执行状态转换
     */
    public void transitionTo(UserState newState, OaUser user) {
        if (!canTransitionTo(newState, user)) {
            throw new IllegalStateException(
                    String.format("无法从[%s]状态转换到[%s]状态", this.description, newState.description)
            );
        }
        user.setState(newState);
    }

    public static UserState fromCode(int code) {
        for (UserState state : values()) {
            if (state.code == code) {
                return state;
            }
        }
        throw new IllegalArgumentException("未知的用户状态代码: " + code);
    }

    public abstract boolean canTransitionTo(UserState newState, OaUser user);
}
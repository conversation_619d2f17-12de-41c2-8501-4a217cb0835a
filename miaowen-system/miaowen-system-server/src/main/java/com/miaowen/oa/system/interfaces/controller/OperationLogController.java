package com.miaowen.oa.system.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.domain.service.OperationLogService;
import com.miaowen.oa.system.interfaces.req.log.OperationLogPageRequest;
import com.miaowen.oa.system.interfaces.res.log.OperationLogPageResp;
import com.miaowen.oa.system.interfaces.res.log.OperationLogStatisticsResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志管理控制器
 * 
 * 功能说明：
 * 1. 提供操作日志的查询和统计接口
 * 2. 支持分页查询和条件筛选
 * 3. 提供用户操作历史查询
 * 4. 支持高风险操作监控
 * 5. 提供统计分析功能
 * 6. 支持日志数据导出
 * 
 * 权限控制：
 * - 查看操作日志：system:operation-log:query
 * - 导出操作日志：system:operation-log:export
 * - 查看统计信息：system:operation-log:statistics
 * - 数据恢复：system:operation-log:recover
 * 
 * 性能优化：
 * - 分页查询避免大数据量返回
 * - 异步导出避免长时间等待
 * - 缓存统计结果提高响应速度
 * - 合理的权限控制保证安全性
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "操作日志管理", description = "操作日志的查询、统计和管理功能")
@RestController
@RequestMapping("/operation-log")
@Validated
@Slf4j
public class OperationLogController {

    @Resource
    private OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     * 
     * 功能说明：
     * - 支持多条件筛选查询
     * - 支持关键字模糊搜索
     * - 按时间倒序排列
     * - 分页返回结果
     * 
     * 性能优化：
     * - 利用数据库索引提高查询效率
     * - 限制单页最大数据量
     * - 时间范围查询优化
     */
    @Operation(summary = "分页查询操作日志", description = "支持多条件筛选的分页查询")
    @GetMapping("/page")
    @PreAuthorize("hasPermission('system:operation-log:query')")
    public CommonResult<PageResult<OperationLogPageResp>> pageOperationLog(
            @Validated OperationLogPageRequest request) {
        
        log.info("分页查询操作日志，请求参数：{}", request);
        
        try {
            PageResult<OperationLogPageResp> result = operationLogService.pageOperationLog(request);
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("分页查询操作日志失败", e);
            return CommonResult.error("查询失败，请稍后重试");
        }
    }

    /**
     * 根据ID查询操作日志详情
     * 
     * 功能说明：
     * - 查询单条日志的详细信息
     * - 包含操作前后数据对比
     * - 显示完整的操作上下文
     */
    @Operation(summary = "查询操作日志详情", description = "根据ID查询操作日志的详细信息")
    @GetMapping("/{id}")
    @PreAuthorize("hasPermission('system:operation-log:query')")
    public CommonResult<OperationLogPageResp> getOperationLogById(
            @Parameter(description = "日志ID", required = true) @PathVariable Long id) {
        
        log.info("查询操作日志详情，ID：{}", id);
        
        try {
            OperationLogPageResp result = operationLogService.getOperationLogById(id);
            if (result == null) {
                return CommonResult.error("操作日志不存在");
            }
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("查询操作日志详情失败，ID：{}", id, e);
            return CommonResult.error("查询失败，请稍后重试");
        }
    }

    /**
     * 查询用户最近操作记录
     * 
     * 功能说明：
     * - 查询指定用户的最近操作
     * - 按时间倒序排列
     * - 限制返回数量
     * 
     * 应用场景：
     * - 用户操作历史查看
     * - 异常行为分析
     * - 操作轨迹追踪
     */
    @Operation(summary = "查询用户最近操作", description = "查询指定用户的最近操作记录")
    @GetMapping("/user/{userId}/recent")
    @PreAuthorize("hasPermission('system:operation-log:query')")
    public CommonResult<List<OperationLogPageResp>> getUserRecentOperations(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "限制数量", example = "10") @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("查询用户最近操作记录，用户ID：{}，限制数量：{}", userId, limit);
        
        try {
            List<OperationLogPageResp> result = operationLogService.getUserRecentOperations(userId, limit);
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("查询用户最近操作记录失败，用户ID：{}", userId, e);
            return CommonResult.error("查询失败，请稍后重试");
        }
    }

    /**
     * 查询业务数据操作历史
     * 
     * 功能说明：
     * - 查询指定业务数据的所有操作记录
     * - 用于数据变更追踪
     * - 支持数据审计
     * 
     * 应用场景：
     * - 数据变更历史查看
     * - 审计追踪
     * - 问题排查
     */
    @Operation(summary = "查询业务数据操作历史", description = "查询指定业务数据的操作历史记录")
    @GetMapping("/business/{businessType}/{businessId}/history")
    @PreAuthorize("hasPermission('system:operation-log:query')")
    public CommonResult<List<OperationLogPageResp>> getBusinessOperationHistory(
            @Parameter(description = "业务类型", required = true) @PathVariable String businessType,
            @Parameter(description = "业务ID", required = true) @PathVariable Long businessId) {
        
        log.info("查询业务数据操作历史，业务类型：{}，业务ID：{}", businessType, businessId);
        
        try {
            List<OperationLogPageResp> result = operationLogService.getBusinessOperationHistory(businessType, businessId);
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("查询业务数据操作历史失败，业务类型：{}，业务ID：{}", businessType, businessId, e);
            return CommonResult.error("查询失败，请稍后重试");
        }
    }

    /**
     * 查询高风险操作记录
     * 
     * 功能说明：
     * - 查询指定风险等级以上的操作
     * - 支持时间范围筛选
     * - 用于安全监控
     * 
     * 应用场景：
     * - 安全审计
     * - 风险监控
     * - 异常操作检测
     */
    @Operation(summary = "查询高风险操作", description = "查询高风险等级的操作记录")
    @GetMapping("/high-risk")
    @PreAuthorize("hasPermission('system:operation-log:query')")
    public CommonResult<List<OperationLogPageResp>> getHighRiskOperations(
            @Parameter(description = "风险等级", example = "3") @RequestParam(defaultValue = "3") Integer riskLevel,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        log.info("查询高风险操作记录，风险等级：{}，时间范围：{} - {}", riskLevel, startTime, endTime);
        
        try {
            List<OperationLogPageResp> result = operationLogService.getHighRiskOperations(riskLevel, startTime, endTime);
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("查询高风险操作记录失败，风险等级：{}", riskLevel, e);
            return CommonResult.error("查询失败，请稍后重试");
        }
    }

    /**
     * 统计用户操作次数
     * 
     * 功能说明：
     * - 统计指定用户在时间范围内的操作次数
     * - 用于用户活跃度分析
     * - 支持自定义时间范围
     */
    @Operation(summary = "统计用户操作次数", description = "统计指定用户的操作次数")
    @GetMapping("/user/{userId}/count")
    @PreAuthorize("hasPermission('system:operation-log:query')")
    public CommonResult<Long> countUserOperations(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        log.info("统计用户操作次数，用户ID：{}，时间范围：{} - {}", userId, startTime, endTime);
        
        try {
            Long count = operationLogService.countUserOperations(userId, startTime, endTime);
            return CommonResult.success(count);
            
        } catch (Exception e) {
            log.error("统计用户操作次数失败，用户ID：{}", userId, e);
            return CommonResult.error("统计失败，请稍后重试");
        }
    }

    /**
     * 获取操作日志统计信息
     * 
     * 功能说明：
     * - 提供全面的统计分析数据
     * - 包含操作类型分布、用户活跃度等
     * - 支持时间范围筛选
     * - 用于数据分析和报表展示
     */
    @Operation(summary = "获取统计信息", description = "获取操作日志的统计分析数据")
    @GetMapping("/statistics")
    @PreAuthorize("hasPermission('system:operation-log:statistics')")
    public CommonResult<OperationLogStatisticsResp> getStatistics(
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        log.info("获取操作日志统计信息，时间范围：{} - {}", startTime, endTime);
        
        try {
            OperationLogStatisticsResp result = operationLogService.getOperationLogStatistics(startTime, endTime);
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("获取操作日志统计信息失败", e);
            return CommonResult.error("获取统计信息失败，请稍后重试");
        }
    }

    /**
     * 导出操作日志
     * 
     * 功能说明：
     * - 根据查询条件导出日志数据
     * - 支持多种格式导出
     * - 限制导出数量防止性能问题
     */
    @Operation(summary = "导出操作日志", description = "根据条件导出操作日志数据")
    @PostMapping("/export")
    @PreAuthorize("hasPermission('system:operation-log:export')")
    public CommonResult<String> exportOperationLogs(@RequestBody @Validated OperationLogPageRequest request) {
        
        log.info("导出操作日志，请求参数：{}", request);
        
        try {
            String result = operationLogService.exportOperationLogs(request);
            if (result == null) {
                return CommonResult.error("没有符合条件的数据");
            }
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("导出操作日志失败", e);
            return CommonResult.error("导出失败，请稍后重试");
        }
    }

    /**
     * 清理过期日志
     * 
     * 功能说明：
     * - 清理超过保留期限的日志数据
     * - 异步执行，不阻塞请求
     * - 支持自定义保留天数
     */
    @Operation(summary = "清理过期日志", description = "清理超过保留期限的操作日志")
    @PostMapping("/clean")
    @PreAuthorize("hasPermission('system:operation-log:clean')")
    public CommonResult<String> cleanExpiredLogs(
            @Parameter(description = "保留天数", example = "90") @RequestParam(defaultValue = "90") Integer retentionDays) {
        
        log.info("清理过期日志，保留天数：{}", retentionDays);
        
        try {
            operationLogService.cleanExpiredLogsAsync(retentionDays);
            return CommonResult.success("清理任务已启动，请稍后查看结果");
            
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return CommonResult.error("清理失败，请稍后重试");
        }
    }

    /**
     * 根据日志恢复数据
     * 
     * 功能说明：
     * - 根据操作日志中的数据恢复业务数据
     * - 用于误操作恢复
     * - 需要管理员权限
     */
    @Operation(summary = "数据恢复", description = "根据操作日志恢复数据")
    @PostMapping("/{id}/recover")
    @PreAuthorize("hasPermission('system:operation-log:recover')")
    public CommonResult<String> recoverDataByLog(
            @Parameter(description = "日志ID", required = true) @PathVariable Long id) {
        
        log.info("根据日志恢复数据，日志ID：{}", id);
        
        try {
            Boolean result = operationLogService.recoverDataByLog(id);
            if (result) {
                return CommonResult.success("数据恢复成功");
            } else {
                return CommonResult.error("数据恢复失败，请检查日志记录");
            }
            
        } catch (Exception e) {
            log.error("根据日志恢复数据失败，日志ID：{}", id, e);
            return CommonResult.error("恢复失败，请稍后重试");
        }
    }

    /**
     * 获取数据变更摘要
     * 
     * 功能说明：
     * - 获取操作日志的数据变更摘要
     * - 用于快速了解数据变更情况
     * - 支持变更对比展示
     */
    @Operation(summary = "获取数据变更摘要", description = "获取操作日志的数据变更摘要")
    @GetMapping("/{id}/change-summary")
    @PreAuthorize("hasPermission('system:operation-log:query')")
    public CommonResult<String> getDataChangeSummary(
            @Parameter(description = "日志ID", required = true) @PathVariable Long id) {
        
        log.info("获取数据变更摘要，日志ID：{}", id);
        
        try {
            String result = operationLogService.getDataChangeSummary(id);
            if (result == null) {
                return CommonResult.error("日志记录不存在或无变更数据");
            }
            return CommonResult.success(result);
            
        } catch (Exception e) {
            log.error("获取数据变更摘要失败，日志ID：{}", id, e);
            return CommonResult.error("获取失败，请稍后重试");
        }
    }
}

package com.miaowen.oa.system.interfaces.req.dept;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 11:28
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class DeptUpdateRequest {

    private Long id;

    // 部门名称
    @NotNull(message = "部门名称不能为空")
    private String departmentName;
    //排序
    private Integer sort;
    //描述
    private String description;

    //上级部门id
    private Long parentId;

    //部门负责人
    private List<Long> leadUserList;
}

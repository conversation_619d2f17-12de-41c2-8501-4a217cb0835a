package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/17 14:02
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
@TableName(value ="oa_application")
public class OaApplicationEntity extends BaseDO {
    private Long id;


    /**
     * 名称
     */
    private String name;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 电脑端链接
     */
    private String pcUrl;

    /**
     * 移动端链接
     */
    private String mobileUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 - 正常 1 - 禁用
     */
    private Integer state;



}

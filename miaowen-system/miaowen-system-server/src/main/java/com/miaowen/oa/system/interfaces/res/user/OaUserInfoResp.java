package com.miaowen.oa.system.interfaces.res.user;


import com.miaowen.oa.system.interfaces.res.dept.UserDepartmentResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobResp;
import com.miaowen.oa.system.interfaces.res.job.OaUserJobExperienceResp;
import com.miaowen.oa.system.interfaces.res.project.OaProjectGroupResp;
import com.miaowen.oa.system.interfaces.res.role.OaRoleResp;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaUserInfoResp {

    //--------------个人信息-----------------

    /**
     * 用户id
     */

    private Long id;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 真实名称
     */
    private String realName;


    /**
     * 0未知 1男 2女
     */
    private Integer gender;


    /**
     * 出生年月
     */
    private String birthday;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 住址
     */
    private String address;


    /**
     * 个人邮箱
     */
    private String email;

    /**
     * 身份证号
     */
    private String identity;



    //--------------企业信息-----------------

    /**
     * 员工编号(工号):固定四位数字,例如0001, 0002
     */
    private String userCode;



    /**
     * 职级
     */
    private String level;



    /**
     * 岗位
     */
    private List<OaJobResp> jobs;


    /**
     * 所属部门
     */
    private List<UserDepartmentResp> departments;


    /**
     * 直属上级
     */
    private List<OaLeaderResp> leaders;


    /**
     * 管理部门
     */
    private List<UserDepartmentResp> managerDepartments;


    /**
     * 所属项目组
     */
    private List<OaProjectGroupResp> projectGroups;


    /**
     * 角色
     */
    private List<OaRoleResp> roles;

    /**
     * 个性签名
     */
    private String signature;


}

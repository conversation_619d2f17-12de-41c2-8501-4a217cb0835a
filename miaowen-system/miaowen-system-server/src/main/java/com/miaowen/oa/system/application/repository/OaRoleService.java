package com.miaowen.oa.system.application.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.system.enums.permission.RoleCodeEnum;
import com.miaowen.oa.system.infrastructure.entity.*;
import com.miaowen.oa.system.infrastructure.mapper.*;
import com.miaowen.oa.system.interfaces.req.role.*;
import com.miaowen.oa.system.interfaces.res.dept.UserDepartmentResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobResp;
import com.miaowen.oa.system.interfaces.res.project.OaProjectGroupResp;
import com.miaowen.oa.system.interfaces.res.role.OaRoleGroupListResp;
import com.miaowen.oa.system.interfaces.res.role.OaRolePageResp;

import com.miaowen.oa.system.interfaces.res.role.OaRoleResp;
import com.miaowen.oa.system.interfaces.res.user.OaUserDetailResp;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.miaowen.oa.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:50
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Repository
public class OaRoleService {

    @Resource
    private OaUserService oaUserService;

    @Resource
    private OaRoleMapper oaRoleMapper;

    @Resource
    private OaRoleGroupMapper oaRoleGroupMapper;

    @Resource
    private OaUserMapper oaUserMapper;


    @Resource
    private OaDepartmentMapper oaDepartmentMapper;
    @Resource
    private OaDepartmentUserRelationMapper oaDepartmentUserRelationMapper;

    @Resource
    private OaProjectGroupMapper oaProjectGroupMapper;
    @Resource
    private OaProjectGroupUserRelationMapper oaProjectGroupUserRelationMapper;

    @Resource
    private OaUserRoleRelationMapper oaUserRoleRelationMapper;

    @Resource
    private OaJobMapper oaJobMapper;


    @Resource
    private OaUserJobRelationMapper oaUserJobRelationMapper;

    @Resource
    private OaMenuRoleRelationMapper oaMenuRoleRelationMapper;

    public PageResult<OaRolePageResp> page(com.miaowen.oa.system.server.interfaces.req.user.RolePageRequest request) {


        PageResult<OaRolePageResp> page = new PageResult<>();


        //1 根据条件查询 <用户的数据>
        List<OaUserEntity> userList = getOaUserList(request);
        if (CollectionUtils.isEmpty(userList)) {
            return page;
        }
        List<OaRolePageResp> oaUserPageRespList = BeanUtils.toBean(userList, OaRolePageResp.class);

        //  提取用户ID集合用于批量查询
        List<Long> userIds = oaUserPageRespList.stream().map(OaRolePageResp::getId).toList();

        // 批量查询关联数据的 userId->resp的键值对map
        Map<Long, List<UserDepartmentResp>> deptMap = getDepartmentMap(userIds);
        Map<Long, List<OaProjectGroupResp>> projectGroupMap = getProjectGroupMap(userIds);
        Map<Long, List<OaJobResp>> jobMap = getJobMap(userIds);

        for (OaRolePageResp resp : oaUserPageRespList) {
            resp.setJobs(jobMap.getOrDefault(resp.getId(), new ArrayList<>()));
            resp.setDepartments(deptMap.getOrDefault(resp.getId(), new ArrayList<>()));
            resp.setProjectGroups(projectGroupMap.getOrDefault(resp.getId(), new ArrayList<>()));
        }

        // 根据请求参数过滤结果
        List<OaRolePageResp> filteredList = oaUserPageRespList;
        if (!CollectionUtils.isEmpty(request.getDepartmentIds())) {
            filteredList = filteredList.stream()
                    .filter(resp -> resp.getDepartments().stream()
                            .anyMatch(dept -> request.getDepartmentIds().contains(dept.getDeptId())))
                    .collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(request.getJobIds())) {
            filteredList = filteredList.stream()
                    .filter(resp -> resp.getJobs().stream()
                            .anyMatch(job -> request.getJobIds().contains(job.getJobId())))
                    .collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(request.getProjectGroupIds())) {
            filteredList = filteredList.stream()
                    .filter(resp -> resp.getProjectGroups().stream()
                            .anyMatch(pg -> request.getProjectGroupIds().contains(pg.getProjectGroupId())))
                    .collect(Collectors.toList());
        }

        // 计算内存分页参数
        int totalRecords = filteredList.size();
        int pageSize = request.getPageSize();
        int currentPage = request.getPage();
        int totalPages = (int) Math.ceil((double) totalRecords / pageSize);

        // 应用内存分页
        int startIndex = (currentPage - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalRecords);
        List<OaRolePageResp> pagedList = filteredList.subList(startIndex, endIndex);

        // 设置分页信息
        page.setList(pagedList);
        page.setTotal((long) totalRecords);

        return page;
    }


    /**
     * @param request 请求参数
     * @return 根据条件筛选后的数据
     */
    private List<OaUserEntity> getOaUserList(com.miaowen.oa.system.server.interfaces.req.user.RolePageRequest request) {
        LambdaQueryWrapper<OaUserRoleRelationEntity> userRoleRelationEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userRoleRelationEntityLambdaQueryWrapper.eq(Objects.nonNull(request.getRoleId()), OaUserRoleRelationEntity::getRoleId, request.getRoleId())
                .eq(OaUserRoleRelationEntity::getDeleteTime, 0);
        List<OaUserRoleRelationEntity> relations = oaUserRoleRelationMapper.selectList(userRoleRelationEntityLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(relations)){
            return Collections.emptyList();
        }
        Map<Long, OaUserRoleRelationEntity> relationMap = relations.stream().collect(Collectors.toMap(OaUserRoleRelationEntity::getUserId, Function.identity()));
        Set<Long> userIdSet = relations.stream().map(OaUserRoleRelationEntity::getUserId).collect(Collectors.toSet());
        LambdaQueryWrapper<OaUserEntity> eqUser = Wrappers.lambdaQuery(OaUserEntity.class)
                .in(!CollectionUtils.isEmpty(userIdSet), OaUserEntity::getId, userIdSet)
                .eq(OaUserEntity::getDeleteTime, 0)
                .eq(OaUserEntity::getLeaveTime, 0);

        // 合并二个 LIKE 条件为 OR 关系
        if (StringUtils.hasText(request.getPrimaryKey())) {
            String keyword = request.getPrimaryKey();
            eqUser.and(wrapper -> wrapper.like(OaUserEntity::getUsername, keyword).or().like(OaUserEntity::getPhone, keyword));
        }

        List<OaUserEntity> oaUserEntities = oaUserMapper.selectList(eqUser);
        for (OaUserEntity oaUserEntity : oaUserEntities) {
            OaUserRoleRelationEntity relation = relationMap.getOrDefault(oaUserEntity.getId(), new OaUserRoleRelationEntity());
            oaUserEntity.setUpdateTime(relation.getUpdateTime());
        }
        return oaUserEntities;
    }




    // 批量获取部门数据（其他关联数据类似）
    private Map<Long, List<UserDepartmentResp>> getDepartmentMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<OaUserDepartmentRelationEntity> relations = oaDepartmentUserRelationMapper.findByUserIds(userIds);

        // 提取部门ID
        Set<Long> deptIds = relations.stream().map(OaUserDepartmentRelationEntity::getDepartmentId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }
        // 批量查询部门详情
        Map<Long, OaDepartmentEntity> deptDetailMap = oaDepartmentMapper.selectByIds(deptIds).stream().collect(Collectors.toMap(OaDepartmentEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<UserDepartmentResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            OaDepartmentEntity dept = deptDetailMap.get(relation.getDepartmentId());
            if (dept != null) {
                UserDepartmentResp resp = new UserDepartmentResp();
                resp.setName(dept.getDepartmentName());
                resp.setDeptId(dept.getId());
                resp.setCode(dept.getDepartmentCode());
                resultMap.computeIfAbsent(relation.getUserId(), k -> new ArrayList<>()).add(resp);
            }
        });

        return resultMap;
    }


    // 批量获取项目组数据（其他关联数据类似）
    private Map<Long, List<OaProjectGroupResp>> getProjectGroupMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<OaProjectGroupUserRelationEntity> relations = oaProjectGroupUserRelationMapper.findByUserIds(userIds);

        // 提取部门ID
        Set<Long> projectGroupIds = relations.stream().map(OaProjectGroupUserRelationEntity::getProjectGroupId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(projectGroupIds)) {
            return Collections.emptyMap();
        }
        // 批量查询部门详情
        Map<Long, OaProjectGroupEntity> projectGroupDetailMap = oaProjectGroupMapper.selectByIds(projectGroupIds).stream().collect(Collectors.toMap(OaProjectGroupEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<OaProjectGroupResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            OaProjectGroupEntity projectGroupEntity = projectGroupDetailMap.get(relation.getUserId());
            if (projectGroupEntity != null) {
                OaProjectGroupResp resp = new OaProjectGroupResp();
                resp.setName(projectGroupEntity.getGroupName());
                resp.setProjectGroupId(projectGroupEntity.getId());
                resultMap.computeIfAbsent(relation.getUserId(), k -> new ArrayList<>()).add(resp);
            }
        });

        return resultMap;
    }

    // 批量获取权限数据（其他关联数据类似）
    private Map<Long, List<OaRoleResp>> getRoleMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<OaUserRoleRelationEntity> relations = oaUserRoleRelationMapper.findByUserIds(userIds);

        // 提取权限ID
        Set<Long> roleIds = relations.stream().map(OaUserRoleRelationEntity::getId).collect(Collectors.toSet());

        // 批量查询部门详情
        Map<Long, OaRoleEntity> roleDetailMap = oaRoleMapper.selectByIds(roleIds).stream().collect(Collectors.toMap(OaRoleEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<OaRoleResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            OaRoleEntity roleEntity = roleDetailMap.get(relation.getId());
            if (roleEntity != null) {
                OaRoleResp resp = new OaRoleResp();
                resp.setRoleName(roleEntity.getRoleName());
                resp.setRoleId(roleEntity.getId());
                resultMap.computeIfAbsent(relation.getId(), k -> new ArrayList<>()).add(resp);
            }
        });

        return resultMap;
    }


    // 批量获取岗位数据（其他关联数据类似）
    private Map<Long, List<OaJobResp>> getJobMap(List<Long> userIds) {
        //  检查空值
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<OaUserJobRelationEntity> relations = oaUserJobRelationMapper.findByUserIds(userIds);

        // 提取岗位ID
        Set<Long> jobIds = relations.stream().map(OaUserJobRelationEntity::getJobId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(jobIds)) {
            return Collections.emptyMap();
        }
        // 批量查询岗位详情
        Map<Long, OaJobEntity> jobDetailMap = oaJobMapper.selectByIds(jobIds).stream().collect(Collectors.toMap(OaJobEntity::getId, Function.identity()));

        // 按用户ID分组
        Map<Long, List<OaJobResp>> resultMap = new HashMap<>();
        relations.forEach(relation -> {
            OaJobEntity jobEntity = jobDetailMap.get(relation.getJobId());
            if (jobEntity != null) {
                OaJobResp resp = new OaJobResp();
                resp.setName(jobEntity.getJobName());
                resp.setJobId(jobEntity.getId());
                resultMap.computeIfAbsent(relation.getUserId(), k -> new ArrayList<>()).add(resp);
            }
        });

        return resultMap;
    }

    public Long createRole(RoleCreateRequest request) {
        if (Objects.isNull(request)) {
            throw exception(ROLE_NOT_EXISTS);
        }

        if (Objects.isNull(request.getRoleGroupId())|| Objects.equals(0L, request.getRoleGroupId())) {
            request.setRoleGroupId(0L);
        }else {
            OaRoleGroupEntity oaRoleGroupEntity = oaRoleGroupMapper.selectById(request.getRoleGroupId());
            if (oaRoleGroupEntity == null) {
                throw exception(ROLE_GROUP_NOT_EXISTS);
            }
        }

        OaRoleEntity entity = BeanUtils.toBean(request, OaRoleEntity.class);
        if (Objects.isNull(entity)) {
            throw exception(ROLE_NOT_EXISTS);
        }

        oaRoleMapper.insert(entity);
        return entity.getId();
    }

    public void updateRole(RoleUpdateRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw exception(ROLE_NOT_EXISTS);
        }

        LambdaQueryWrapper<OaRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaRoleEntity::getId, request.getId())
                .eq(OaRoleEntity::getDeleteTime, 0);
        OaRoleEntity entity = oaRoleMapper.selectOne(wrapper);
        if (entity == null) {
            throw exception(ROLE_NOT_EXISTS);
        }

        entity = BeanUtils.toBean(request, OaRoleEntity.class);
        if (Objects.isNull(entity)) {
            throw exception(ROLE_NOT_EXISTS);
        }

        oaRoleMapper.updateById(entity);
    }

    public void deleteRole(Long id) {
        if (Objects.isNull(id)) {
            throw exception(ROLE_NOT_EXISTS);
        }

        OaRoleEntity entity = oaRoleMapper.selectById(id);
        if (entity == null) {
            throw exception(ROLE_NOT_EXISTS);
        }

        oaRoleMapper.logicDeleteById(id);

        //对应的用户角色关系数据也会被删除
        oaUserRoleRelationMapper.logicDelete(Wrappers.lambdaQuery(OaUserRoleRelationEntity.class).eq(OaUserRoleRelationEntity::getRoleId, id));

        //对应的角色菜单关系数据也会被删除
        oaMenuRoleRelationMapper.logicDelete(Wrappers.lambdaQuery(OaMenuRoleRelationEntity.class).eq(OaMenuRoleRelationEntity::getRoleId, id));
    }

    public OaRoleResp getRoleDetail(Long id) {
        if (Objects.isNull(id)) {
            throw exception(ROLE_NOT_EXISTS);
        }

        LambdaQueryWrapper<OaRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaRoleEntity::getId, id)
                .eq(OaRoleEntity::getDeleteTime, 0);
        OaRoleEntity oaRoleEntity = oaRoleMapper.selectOne(wrapper);
        if (oaRoleEntity == null) {
            throw exception(ROLE_NOT_EXISTS);
        }

        OaRoleResp resp = BeanUtils.toBean(oaRoleEntity, OaRoleResp.class);
        if (Objects.isNull(resp)) {
            throw exception(ROLE_NOT_EXISTS);
        }

        OaUserDetailResp detail = oaUserService.detail(oaRoleEntity.getCreator());

        resp.setRoleId(oaRoleEntity.getId());
        resp.setCreateName(detail.getRealName());
        return resp;
    }

    public List<OaRoleResp> getRoles(Long roleGroupId) {
        List<OaRoleResp> roles = new ArrayList<>();
        LambdaQueryWrapper<OaRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(roleGroupId), OaRoleEntity::getRoleGroupId, roleGroupId).eq(OaRoleEntity::getDeleteTime, 0);
        List<OaRoleEntity> oaRoleEntities = oaRoleMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(oaRoleEntities)) {
            return roles;
        }
        for (OaRoleEntity oaRoleEntity : oaRoleEntities) {
            OaRoleResp resp = BeanUtils.toBean(oaRoleEntity, OaRoleResp.class);
            resp.setRoleId(oaRoleEntity.getId());
            roles.add(resp);
        }
        return roles;
    }

    public void addUser(RoleAddUserRequest request) {
        List<OaUserRoleRelationEntity> relations = new ArrayList<>();
        if (CollectionUtils.isEmpty(request.getUserIds())){
            return;
        }
        List<OaUserEntity> oaUserEntities = oaUserMapper.selectByIds(request.getUserIds());
        if (CollectionUtils.isEmpty(oaUserEntities)) {
            throw exception(USER_NOT_EXISTS);
        }
        OaRoleEntity oaRoleEntity = oaRoleMapper.selectById(request.getRoleId());
        if (oaRoleEntity == null) {
            throw exception(ROLE_NOT_EXISTS);
        }

        for (OaUserEntity oaUserEntity : oaUserEntities) {
            OaUserRoleRelationEntity relation = new OaUserRoleRelationEntity();
            relation.setUserId(oaUserEntity.getId());
            relation.setRoleId(request.getRoleId());
            relations.add(relation);
        }

        oaUserRoleRelationMapper.insertOrUpdate(relations);
    }

    public void addDept(RoleAddDepartmentRequest request) {
        List<OaUserRoleRelationEntity> relations = new ArrayList<>();
        if (CollectionUtils.isEmpty(request.getDeptIds())){
            return;
        }

        List<OaDepartmentEntity> oaDepartmentEntities = oaDepartmentMapper.selectByIds(request.getDeptIds());
        if (CollectionUtils.isEmpty(oaDepartmentEntities)) {
            throw exception(DEPT_NOT_FOUND);
        }

        OaRoleEntity oaRoleEntity = oaRoleMapper.selectById(request.getRoleId());
        if (oaRoleEntity == null) {
            throw exception(ROLE_NOT_EXISTS);
        }

        List<OaUserDetailResp> users = oaUserService.getUsers(request.getDeptIds());
        if (CollectionUtils.isEmpty(users)) {
            return;
        }


        for (OaUserDetailResp oaUserEntity : users) {
            OaUserRoleRelationEntity relation = new OaUserRoleRelationEntity();
            relation.setUserId(oaUserEntity.getId());
            relation.setRoleId(request.getRoleId());
            relations.add(relation);
        }

        oaUserRoleRelationMapper.insertOrUpdate(relations);
    }

    public void authority(RoleAuthorityRequest request) {
        LambdaQueryWrapper<OaRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaRoleEntity::getId, request.getRoleId());
        wrapper.eq(OaRoleEntity::getDeleteTime, 0);
        OaRoleEntity oaRoleEntity = oaRoleMapper.selectOne(wrapper);
        if (Objects.isNull(oaRoleEntity)) {
            throw exception(ROLE_AUTHORITY_FAILED);
        }

        if (CollectionUtils.isEmpty(request.getMenuIds())){
            throw exception(ROLE_MENU_IDS_EMPTY);
        }



        List<OaMenuRoleRelationEntity> relations = new ArrayList<>();
        List<OaMenuRoleRelationEntity> oaMenuRoleRelationEntities = oaMenuRoleRelationMapper.selectList(Wrappers.lambdaQuery(OaMenuRoleRelationEntity.class)
                .eq(OaMenuRoleRelationEntity::getRoleId, request.getRoleId())
                .eq(OaMenuRoleRelationEntity::getDeleteTime, 0)
        );
        List<Long> existMenuId = oaMenuRoleRelationEntities.stream().map(OaMenuRoleRelationEntity::getMenuId).toList();
        for (Long menuId : request.getMenuIds()) {
            if (existMenuId.contains(menuId)) {
                continue;
            }
            OaMenuRoleRelationEntity relation = new OaMenuRoleRelationEntity();
            relation.setMenuId(menuId);
            relation.setRoleId(request.getRoleId());
            relations.add(relation);
        }
        oaMenuRoleRelationMapper.insertOrUpdate(relations);

    }


    public Long createRoleGroup(OaRoleGroupCreateRequest request) {
        if (Objects.isNull(request)) {
            throw exception(ROLE_GROUP_NOT_EXISTS);
        }

        OaRoleGroupEntity entity = BeanUtils.toBean(request, OaRoleGroupEntity.class);
        if (Objects.isNull(entity)) {
            throw exception(ROLE_GROUP_NOT_EXISTS);
        }

        oaRoleGroupMapper.insert(entity);
        return entity.getId();
    }

    public void removeUser(RoleBatchRemoveRequest request) {

        LambdaQueryWrapper<OaRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaRoleEntity::getId, request.getRoleId());
        wrapper.eq(OaRoleEntity::getDeleteTime, 0);
        OaRoleEntity oaRoleEntity = oaRoleMapper.selectOne(wrapper);
        if (Objects.isNull(oaRoleEntity)) {
            throw exception(ROLE_NOT_EXISTS);
        }
        List<OaUserEntity> entities = oaUserMapper.selectByIds(request.getUserIds());
        if (CollectionUtils.isEmpty(entities)) {
            throw exception(USER_NOT_EXISTS);
        }

        //对应的用户角色关系数据也会被删除
        oaUserRoleRelationMapper.logicDelete(Wrappers.lambdaQuery(OaUserRoleRelationEntity.class).in(OaUserRoleRelationEntity::getUserId, request.getUserIds()).eq(OaUserRoleRelationEntity::getRoleId, request.getRoleId()));

    }

    /**
     *  从缓存中获取角色列表
     */
    public List<OaRoleEntity> getRoleListFromCache(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        // 这里采用 for 循环从缓存中获取，主要考虑 Spring CacheManager 无法批量操作的问题
        OaRoleService self = getSelf();
        return com.miaowen.oa.framework.common.util.collection.CollectionUtils.convertList(ids, self::getRoleFromCache);
    }

    /**
     * 从缓存中获取角色
     */
    public OaRoleEntity getRoleFromCache(Long id) {
        return oaRoleMapper.selectById(id);
    }


    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     */
    private OaRoleService getSelf() {
        return SpringUtil.getBean(getClass());
    }

    public boolean hasAnySuperAdmin(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return false;
        }
        OaRoleService self = getSelf();
        return ids.stream().anyMatch(id -> {
            OaRoleEntity role = self.getRoleFromCache(id);
            return role != null && RoleCodeEnum.isSuperAdmin(role.getRoleCode());
        });


    }
}

package com.miaowen.oa.system.infrastructure.mapper;


import com.miaowen.oa.framework.mybatis.core.mapper.CustomBaseMapper;
import com.miaowen.oa.system.infrastructure.entity.OaUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;


/**
 * <AUTHOR>
 * @Date 2025/7/14 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Mapper
public interface OaUserMapper extends CustomBaseMapper<OaUserEntity> {

    /**
     * 根据用户名查找用户
     */
    @Select("SELECT * FROM oa_user WHERE username = #{username} or phone = #{username}")
    OaUserEntity selectByUsername(@Param("username") String username);


    /**
     * 根据企业微信用户ID查找用户
     * 注意：需要在用户表中添加 qy_wechat_user_id 字段
     */
    @Select("SELECT * FROM oa_user WHERE qy_wechat_user_id = #{wechatUserId} AND deleted = 0")
    OaUserEntity selectByWechatUserId(@Param("wechatUserId") String wechatUserId);

    /**
     * 更新最后登录信息
     */
    @Update("UPDATE oa_user SET last_login_ip = #{loginIp}, last_login_time = #{loginTime} WHERE id = #{userId}")
    void updateLastLoginInfo(@Param("userId") Long userId,
                           @Param("loginIp") String loginIp,
                           @Param("loginTime") Integer loginTime);
}

package com.miaowen.oa.system.interfaces.res.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/18 15:11
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaProjectDetailResp {

    private Long id;

    /**
     * 项目名字
     */
    private String projectName;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 板块
     */
    private Integer plate;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 接口域名
     */
    private String apiDomain;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 更新者
     */
    private Long updater;

}

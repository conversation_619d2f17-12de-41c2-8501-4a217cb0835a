package com.miaowen.oa.system.interfaces.res.menu;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data

public class OaMenuDetailResp  {


    private Long id;

    /**
     * 角色id
     */
    private Long roleId;


    /**
     * 上级菜单id
     */
    private Long parentId;

    /**
     * 上级菜单名称
     */
    private String parentMenuName;


    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 菜单类型：1-目录，2-菜单，3-权限点
     */
    private Integer menuType;

    /**
     * 菜单路径
     */
    private String menuUrl;


    /**
     * 路由路径
     */
    private String routeUrl;


    /**
     * 权限标识
     */
    private String authoritySign;

    /**
     * 图标
     */
    private String icon;


    /**
     * (禁用)状态: 0正常 1禁用
     */
    private Integer state;


    /**
     * 排序
     */
    private Integer sort;




    /**
     * 其他设置
     */
    private List<String> settings;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long creator;

    private List<OaMenuDetailResp> children;

}

package com.miaowen.oa.system.interfaces.res.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 操作日志统计响应
 * 
 * 功能说明：
 * 1. 提供全面的统计分析数据
 * 2. 包含操作类型分布、用户活跃度等
 * 3. 支持时间趋势分析
 * 4. 用于数据分析和报表展示
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "操作日志统计响应")
public class OperationLogStatisticsResp {

    // ========== 基础统计 ==========
    
    @Schema(description = "总操作次数", example = "10000")
    private Long totalOperations;

    @Schema(description = "成功操作次数", example = "9500")
    private Long successOperations;

    @Schema(description = "失败操作次数", example = "500")
    private Long failedOperations;

    @Schema(description = "成功率(%)", example = "95.0")
    private Double successRate;

    @Schema(description = "今日操作次数", example = "150")
    private Long todayOperations;

    @Schema(description = "活跃用户数", example = "50")
    private Long activeUsers;

    @Schema(description = "平均执行时间(毫秒)", example = "120")
    private Double avgExecutionTime;

    @Schema(description = "高风险操作次数", example = "25")
    private Long highRiskOperations;

    @Schema(description = "敏感操作次数", example = "15")
    private Long sensitiveOperations;

    // ========== 分布统计 ==========
    
    @Schema(description = "操作类型分布")
    private List<StatisticItem> operationTypeDistribution;

    @Schema(description = "操作模块分布")
    private List<StatisticItem> operationModuleDistribution;

    @Schema(description = "风险等级分布")
    private List<StatisticItem> riskLevelDistribution;

    @Schema(description = "操作来源分布")
    private List<StatisticItem> operationSourceDistribution;

    // ========== 用户活跃度 ==========
    
    @Schema(description = "用户活跃度排行")
    private List<UserActivityItem> userActivityRanking;

    @Schema(description = "部门活跃度排行")
    private List<DeptActivityItem> deptActivityRanking;

    // ========== 时间趋势 ==========
    
    @Schema(description = "每日操作趋势")
    private List<TrendItem> dailyOperationTrend;

    @Schema(description = "每小时操作趋势")
    private List<TrendItem> hourlyOperationTrend;

    // ========== 性能统计 ==========
    
    @Schema(description = "执行时间分布")
    private List<StatisticItem> executionTimeDistribution;

    @Schema(description = "慢操作排行")
    private List<SlowOperationItem> slowOperationRanking;

    // ========== 异常统计 ==========
    
    @Schema(description = "异常类型分布")
    private List<StatisticItem> exceptionTypeDistribution;

    @Schema(description = "异常操作排行")
    private List<ExceptionOperationItem> exceptionOperationRanking;

    /**
     * 统计项
     */
    @Data
    @Schema(description = "统计项")
    public static class StatisticItem {
        
        @Schema(description = "名称", example = "新增操作")
        private String name;

        @Schema(description = "值", example = "2")
        private String value;

        @Schema(description = "数量", example = "1500")
        private Long count;

        @Schema(description = "百分比", example = "15.0")
        private Double percentage;

        @Schema(description = "颜色", example = "#1890ff")
        private String color;
    }

    /**
     * 用户活跃度项
     */
    @Data
    @Schema(description = "用户活跃度项")
    public static class UserActivityItem {
        
        @Schema(description = "用户ID", example = "1001")
        private Long userId;

        @Schema(description = "用户名", example = "admin")
        private String username;

        @Schema(description = "真实姓名", example = "管理员")
        private String realName;

        @Schema(description = "部门名称", example = "技术部")
        private String deptName;

        @Schema(description = "操作次数", example = "500")
        private Long operationCount;

        @Schema(description = "成功次数", example = "480")
        private Long successCount;

        @Schema(description = "失败次数", example = "20")
        private Long failedCount;

        @Schema(description = "成功率", example = "96.0")
        private Double successRate;

        @Schema(description = "平均执行时间", example = "150")
        private Double avgExecutionTime;

        @Schema(description = "最后操作时间", example = "2025-01-18 10:30:00")
        private String lastOperationTime;
    }

    /**
     * 部门活跃度项
     */
    @Data
    @Schema(description = "部门活跃度项")
    public static class DeptActivityItem {
        
        @Schema(description = "部门ID", example = "1001")
        private Long deptId;

        @Schema(description = "部门名称", example = "技术部")
        private String deptName;

        @Schema(description = "用户数量", example = "10")
        private Long userCount;

        @Schema(description = "操作次数", example = "2000")
        private Long operationCount;

        @Schema(description = "人均操作次数", example = "200")
        private Double avgOperationCount;

        @Schema(description = "活跃度排名", example = "1")
        private Integer ranking;
    }

    /**
     * 趋势项
     */
    @Data
    @Schema(description = "趋势项")
    public static class TrendItem {
        
        @Schema(description = "时间标签", example = "2025-01-18")
        private String timeLabel;

        @Schema(description = "操作次数", example = "150")
        private Long operationCount;

        @Schema(description = "成功次数", example = "140")
        private Long successCount;

        @Schema(description = "失败次数", example = "10")
        private Long failedCount;

        @Schema(description = "平均执行时间", example = "120")
        private Double avgExecutionTime;

        @Schema(description = "活跃用户数", example = "25")
        private Long activeUserCount;
    }

    /**
     * 慢操作项
     */
    @Data
    @Schema(description = "慢操作项")
    public static class SlowOperationItem {
        
        @Schema(description = "操作功能", example = "批量导入用户")
        private String operationFunction;

        @Schema(description = "操作模块", example = "user")
        private String operationModule;

        @Schema(description = "平均执行时间", example = "5000")
        private Double avgExecutionTime;

        @Schema(description = "最大执行时间", example = "10000")
        private Long maxExecutionTime;

        @Schema(description = "操作次数", example = "50")
        private Long operationCount;

        @Schema(description = "慢操作次数", example = "10")
        private Long slowOperationCount;

        @Schema(description = "慢操作比例", example = "20.0")
        private Double slowOperationRate;
    }

    /**
     * 异常操作项
     */
    @Data
    @Schema(description = "异常操作项")
    public static class ExceptionOperationItem {
        
        @Schema(description = "操作功能", example = "删除用户")
        private String operationFunction;

        @Schema(description = "操作模块", example = "user")
        private String operationModule;

        @Schema(description = "异常次数", example = "5")
        private Long exceptionCount;

        @Schema(description = "总操作次数", example = "100")
        private Long totalOperationCount;

        @Schema(description = "异常率", example = "5.0")
        private Double exceptionRate;

        @Schema(description = "主要异常类型", example = "权限不足")
        private String mainExceptionType;

        @Schema(description = "最近异常时间", example = "2025-01-18 10:30:00")
        private String lastExceptionTime;
    }

    // ========== 辅助方法 ==========

    /**
     * 计算失败率
     */
    public Double getFailureRate() {
        if (totalOperations == null || totalOperations == 0) {
            return 0.0;
        }
        return (failedOperations * 100.0) / totalOperations;
    }

    /**
     * 获取操作效率描述
     */
    public String getEfficiencyDescription() {
        if (successRate == null) {
            return "未知";
        }
        if (successRate >= 95.0) {
            return "优秀";
        } else if (successRate >= 90.0) {
            return "良好";
        } else if (successRate >= 80.0) {
            return "一般";
        } else {
            return "较差";
        }
    }

    /**
     * 获取性能描述
     */
    public String getPerformanceDescription() {
        if (avgExecutionTime == null) {
            return "未知";
        }
        if (avgExecutionTime <= 100) {
            return "优秀";
        } else if (avgExecutionTime <= 500) {
            return "良好";
        } else if (avgExecutionTime <= 1000) {
            return "一般";
        } else {
            return "较慢";
        }
    }

    /**
     * 获取安全等级描述
     */
    public String getSecurityLevelDescription() {
        if (highRiskOperations == null || totalOperations == null || totalOperations == 0) {
            return "未知";
        }
        double riskRate = (highRiskOperations * 100.0) / totalOperations;
        if (riskRate <= 1.0) {
            return "安全";
        } else if (riskRate <= 5.0) {
            return "一般";
        } else if (riskRate <= 10.0) {
            return "注意";
        } else {
            return "高风险";
        }
    }
}

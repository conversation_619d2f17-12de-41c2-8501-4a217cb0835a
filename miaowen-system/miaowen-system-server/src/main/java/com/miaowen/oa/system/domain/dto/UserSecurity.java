package com.miaowen.oa.system.domain.dto;

import lombok.Data;

/**
 * 用户安全信息值对象
 *
 * <AUTHOR>
 */
@Data
public class UserSecurity {
    private String qyWechatUserId;
    private Password password;
    private Integer enablePasswordLogin;
    private Integer loginNumber;
    private String lastLoginIp;
    private Integer lastLoginTime;

    public UserSecurity(String qyWechatUserId, String password, Integer enablePasswordLogin, Integer loginNumber, String lastLoginIp, Integer lastLoginTime) {
        this.qyWechatUserId = qyWechatUserId;
        this.enablePasswordLogin = enablePasswordLogin;
        this.password = Password.create(password);
        this.loginNumber = loginNumber != null ? loginNumber : 0;
        this.lastLoginIp = lastLoginIp;
        this.lastLoginTime = lastLoginTime;
    }

    /**
     * 创建新密码版本
     */
    public UserSecurity withNewPassword(String newPassword) {
        return new UserSecurity(qyWechatUserId, newPassword, enablePasswordLogin, loginNumber, lastLoginIp,
            lastLoginTime);
    }

    /**
     * 记录登录信息
     */
    public UserSecurity recordLogin(String ip, int loginTime) {
        return new UserSecurity(
            qyWechatUserId,
            password.getHash(),
            enablePasswordLogin,
            loginNumber + 1, 
            ip, 
            loginTime
        );
    }
}
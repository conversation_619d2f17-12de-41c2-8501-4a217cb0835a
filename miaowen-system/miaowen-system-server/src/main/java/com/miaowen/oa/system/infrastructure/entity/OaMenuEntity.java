package com.miaowen.oa.system.infrastructure.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value ="oa_menu")
public class OaMenuEntity  extends BaseDO {


    /**
     * 上级菜单id
     */
    private Long parentId;


    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 菜单类型：1-目录，2-菜单，3-权限点
     */
    private Integer menuType;

    /**
     * 菜单路径
     */
    private String menuUrl;


    /**
     * 路由路径
     */
    private String routeUrl;


    /**
     * 权限标识
     */
    private String authoritySign;



    /**
     * 状态0正常 1禁用
     */
    private Integer state;


    /**
     * 图标
     */
    private String icon;

    /**
     * 密码
     */
    private Integer sort;

    /**
     * 其他配置
     */
    private String settings;


}

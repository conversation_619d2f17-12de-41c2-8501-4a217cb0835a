package com.miaowen.oa.system.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 用户员工信息值对象
 *
 * 严格按照OaUserEntity中的实际字段设计，不添加不存在的字段
 *
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode
public class UserEmployeeInfo {

    // ========== 员工基本信息（对应OaUserEntity实际字段） ==========

    /**
     * 员工类型：1-正式员工（全职） 2-实习生 3-外包员工 4-其他
     * 对应OaUserEntity.employeeType
     */
    private final Integer employeeType;

    /**
     * 员工状态：1-试用期，2-准正式，3-正式 4-待离职，5-已离职
     * 对应OaUserEntity.employeeState（注意是State不是Status）
     */
    private final Integer employeeState;

    // ========== 时间信息（对应OaUserEntity实际字段） ==========

    /**
     * 入职日期（当前）
     * 对应OaUserEntity.entryDate
     */
    private final LocalDate entryDate;

    /**
     * 试用期开始日期（当前）
     * 对应OaUserEntity.probationStartDate
     */
    private final LocalDate probationStartDate;

    /**
     * 试用期结束日期（当前）
     * 对应OaUserEntity.probationEndDate
     */
    private final LocalDate probationEndDate;

    /**
     * 转正日期（当前）
     * 对应OaUserEntity.regularDate
     */
    private final LocalDate regularDate;

    /**
     * 转正说明
     * 对应OaUserEntity.regularNotes
     */
    private final String regularNotes;

    // ========== 入职状态信息 ==========

    /**
     * 入职状态：0-无效 1-待入职 2-已入职
     * 对应OaUserEntity.entryState
     */
    private final Integer entryState;

    // ========== 构造函数 ==========

    /**
     * 完整构造函数（严格按照OaUserEntity字段）
     */
    public UserEmployeeInfo(Integer employeeType, Integer employeeState, LocalDate entryDate,
                           LocalDate probationStartDate, LocalDate probationEndDate,
                           LocalDate regularDate, String regularNotes, Integer entryState) {
        this.employeeType = employeeType;
        this.employeeState = employeeState;
        this.entryDate = entryDate;
        this.probationStartDate = probationStartDate;
        this.probationEndDate = probationEndDate;
        this.regularDate = regularDate;
        this.regularNotes = regularNotes;
        this.entryState = entryState;
    }

    // ========== 工厂方法 ==========

    /**
     * 创建默认员工信息
     */
    public static UserEmployeeInfo createDefault() {
        return new UserEmployeeInfo(
            1,            // 默认正式员工
            1,            // 默认试用期状态
            null,         // 默认入职日期
            null,         // 默认试用期开始日期
            null,         // 默认试用期结束日期
            null,         // 默认转正日期
            "",           // 默认转正说明
            1             // 默认待入职状态
        );
    }

    /**
     * 创建基础员工信息
     */
    public static UserEmployeeInfo createBasic(Integer employeeType, Integer employeeState, LocalDate entryDate) {
        return new UserEmployeeInfo(
            employeeType,
            employeeState,
            entryDate,
            null,         // 默认试用期开始日期
            null,         // 默认试用期结束日期
            null,         // 默认转正日期
            "",           // 默认转正说明
            2             // 已入职状态
        );
    }

    // ========== 业务方法 ==========

    /**
     * 是否为正式员工（全职）
     * 员工类型：1-正式员工（全职） 2-实习生 3-外包员工 4-其他
     */
    public boolean isFormalEmployee() {
        return Objects.equals(this.employeeType, 1);
    }

    /**
     * 是否为实习生
     */
    public boolean isIntern() {
        return Objects.equals(this.employeeType, 2);
    }

    /**
     * 是否为外包员工
     */
    public boolean isOutsourced() {
        return Objects.equals(this.employeeType, 3);
    }

    /**
     * 是否在试用期
     * 员工状态：1-试用期，2-准正式，3-正式 4-待离职，5-已离职
     */
    public boolean isInProbation() {
        return Objects.equals(this.employeeState, 1);
    }

    /**
     * 是否为准正式员工
     */
    public boolean isPreFormal() {
        return Objects.equals(this.employeeState, 2);
    }

    /**
     * 是否已转正（正式员工）
     */
    public boolean isRegular() {
        return Objects.equals(this.employeeState, 3);
    }

    /**
     * 是否待离职
     */
    public boolean isPendingLeave() {
        return Objects.equals(this.employeeState, 4);
    }

    /**
     * 是否已离职
     */
    public boolean hasLeft() {
        return Objects.equals(this.employeeState, 5);
    }

    /**
     * 是否在职（未离职）
     */
    public boolean isActive() {
        return !hasLeft();
    }

    
    /**
     * 验证是否可以转正
     */
    public boolean canBeRegular() {
        return this.isActive() && this.isFormalEmployee() && !this.isRegular();
    }
}

package com.miaowen.oa.system.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 用户员工信息值对象
 * 
 * 包含员工的组织架构、职位、入职等相关信息
 * 根据OaUserEntity中的员工相关字段设计
 * 
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode
public class UserEmployeeInfo {

    // ========== 组织架构信息 ==========
    
    /**
     * 部门ID
     */
    private final Long departmentId;
    
    /**
     * 部门名称
     */
    private final String departmentName;
    
    /**
     * 职位ID
     */
    private final Long positionId;
    
    /**
     * 职位名称
     */
    private final String positionName;
    
    /**
     * 上级ID
     */
    private final Long superiorId;
    
    /**
     * 上级姓名
     */
    private final String superiorName;

    // ========== 员工基本信息 ==========
    
    /**
     * 员工编号
     */
    private final String employeeNumber;
    
    /**
     * 员工类型
     * 1-正式员工，2-实习生，3-外包，4-顾问，5-其他
     */
    private final Integer employeeType;
    
    /**
     * 员工状态
     * 1-在职，2-离职，3-停薪留职，4-退休，5-其他
     */
    private final Integer employeeStatus;

    // ========== 时间信息 ==========
    
    /**
     * 入职时间（时间戳）
     */
    private final Long entryDate;
    
    /**
     * 试用期结束时间（时间戳）
     */
    private final Long probationEndDate;
    
    /**
     * 转正时间（时间戳）
     */
    private final Long regularDate;

    // ========== 构造函数 ==========
    
    /**
     * 完整构造函数
     */
    public UserEmployeeInfo(Long departmentId, String departmentName, Long positionId, String positionName,
                           Long superiorId, String superiorName, String employeeNumber, Integer employeeType,
                           Integer employeeStatus, Long entryDate, Long probationEndDate, Long regularDate) {
        this.departmentId = departmentId;
        this.departmentName = Objects.requireNonNull(departmentName, "部门名称不能为空");
        this.positionId = positionId;
        this.positionName = Objects.requireNonNull(positionName, "职位名称不能为空");
        this.superiorId = superiorId;
        this.superiorName = Objects.requireNonNull(superiorName, "上级姓名不能为空");
        this.employeeNumber = Objects.requireNonNull(employeeNumber, "员工编号不能为空");
        this.employeeType = Objects.requireNonNull(employeeType, "员工类型不能为空");
        this.employeeStatus = Objects.requireNonNull(employeeStatus, "员工状态不能为空");
        this.entryDate = entryDate;
        this.probationEndDate = probationEndDate;
        this.regularDate = regularDate;
    }

    // ========== 工厂方法 ==========
    
    /**
     * 创建默认员工信息
     */
    public static UserEmployeeInfo createDefault() {
        return new UserEmployeeInfo(
            0L,           // 默认部门ID
            "",           // 默认部门名称
            0L,           // 默认职位ID
            "",           // 默认职位名称
            null,         // 默认无上级
            "",           // 默认上级姓名
            "",           // 默认员工编号
            1,            // 默认正式员工
            1,            // 默认在职状态
            0L,           // 默认入职时间
            0L,           // 默认试用期结束时间
            0L            // 默认转正时间
        );
    }
    
    /**
     * 创建基础员工信息
     */
    public static UserEmployeeInfo createBasic(Long departmentId, String departmentName,
                                              Long positionId, String positionName,
                                              String employeeNumber) {
        return new UserEmployeeInfo(
            departmentId,
            departmentName,
            positionId,
            positionName,
            null,         // 默认无上级
            "",           // 默认上级姓名
            employeeNumber,
            1,            // 默认正式员工
            1,            // 默认在职状态
            System.currentTimeMillis(), // 当前时间作为入职时间
            0L,           // 默认试用期结束时间
            0L            // 默认转正时间
        );
    }

    // ========== 业务方法 ==========
    
    /**
     * 是否为正式员工
     */
    public boolean isFormalEmployee() {
        return Objects.equals(this.employeeType, 1);
    }
    
    /**
     * 是否在职
     */
    public boolean isActive() {
        return Objects.equals(this.employeeStatus, 1);
    }
    
    /**
     * 是否有上级
     */
    public boolean hasSuperior() {
        return this.superiorId != null && this.superiorId > 0;
    }
    
    /**
     * 是否已转正
     */
    public boolean isRegular() {
        return this.regularDate != null && this.regularDate > 0;
    }
    
    /**
     * 是否在试用期
     */
    public boolean isInProbation() {
        if (this.probationEndDate == null || this.probationEndDate <= 0) {
            return false;
        }
        return System.currentTimeMillis() < this.probationEndDate;
    }
    
    /**
     * 获取工作年限（年）
     */
    public int getWorkYears() {
        if (this.entryDate == null || this.entryDate <= 0) {
            return 0;
        }
        long currentTime = System.currentTimeMillis();
        long workDays = (currentTime - this.entryDate) / (1000 * 60 * 60 * 24);
        return (int) (workDays / 365);
    }
    
    /**
     * 获取工作月数
     */
    public int getWorkMonths() {
        if (this.entryDate == null || this.entryDate <= 0) {
            return 0;
        }
        long currentTime = System.currentTimeMillis();
        long workDays = (currentTime - this.entryDate) / (1000 * 60 * 60 * 24);
        return (int) (workDays / 30);
    }
    
    /**
     * 获取员工类型描述
     */
    public String getEmployeeTypeDescription() {
        switch (this.employeeType) {
            case 1: return "正式员工";
            case 2: return "实习生";
            case 3: return "外包";
            case 4: return "顾问";
            case 5: return "其他";
            default: return "未知";
        }
    }
    
    /**
     * 获取员工状态描述
     */
    public String getEmployeeStatusDescription() {
        switch (this.employeeStatus) {
            case 1: return "在职";
            case 2: return "离职";
            case 3: return "停薪留职";
            case 4: return "退休";
            case 5: return "其他";
            default: return "未知";
        }
    }
    
    /**
     * 更新部门信息
     */
    public UserEmployeeInfo updateDepartment(Long departmentId, String departmentName) {
        return new UserEmployeeInfo(
            departmentId,
            departmentName,
            this.positionId,
            this.positionName,
            this.superiorId,
            this.superiorName,
            this.employeeNumber,
            this.employeeType,
            this.employeeStatus,
            this.entryDate,
            this.probationEndDate,
            this.regularDate
        );
    }
    
    /**
     * 更新职位信息
     */
    public UserEmployeeInfo updatePosition(Long positionId, String positionName) {
        return new UserEmployeeInfo(
            this.departmentId,
            this.departmentName,
            positionId,
            positionName,
            this.superiorId,
            this.superiorName,
            this.employeeNumber,
            this.employeeType,
            this.employeeStatus,
            this.entryDate,
            this.probationEndDate,
            this.regularDate
        );
    }
    
    /**
     * 更新上级信息
     */
    public UserEmployeeInfo updateSuperior(Long superiorId, String superiorName) {
        return new UserEmployeeInfo(
            this.departmentId,
            this.departmentName,
            this.positionId,
            this.positionName,
            superiorId,
            superiorName,
            this.employeeNumber,
            this.employeeType,
            this.employeeStatus,
            this.entryDate,
            this.probationEndDate,
            this.regularDate
        );
    }
    
    /**
     * 设置转正
     */
    public UserEmployeeInfo setRegular(Long regularDate) {
        return new UserEmployeeInfo(
            this.departmentId,
            this.departmentName,
            this.positionId,
            this.positionName,
            this.superiorId,
            this.superiorName,
            this.employeeNumber,
            this.employeeType,
            this.employeeStatus,
            this.entryDate,
            this.probationEndDate,
            regularDate
        );
    }
    
    /**
     * 更新员工状态
     */
    public UserEmployeeInfo updateStatus(Integer employeeStatus) {
        return new UserEmployeeInfo(
            this.departmentId,
            this.departmentName,
            this.positionId,
            this.positionName,
            this.superiorId,
            this.superiorName,
            this.employeeNumber,
            this.employeeType,
            employeeStatus,
            this.entryDate,
            this.probationEndDate,
            this.regularDate
        );
    }

    // ========== 验证方法 ==========
    
    /**
     * 验证员工信息是否有效
     */
    public boolean isValid() {
        return this.departmentId != null && this.departmentId > 0
            && this.positionId != null && this.positionId > 0
            && this.employeeNumber != null && !this.employeeNumber.trim().isEmpty()
            && this.employeeType != null && this.employeeType > 0
            && this.employeeStatus != null && this.employeeStatus > 0;
    }
    
    /**
     * 验证是否可以转正
     */
    public boolean canBeRegular() {
        return this.isActive() && this.isFormalEmployee() && !this.isRegular();
    }
}

package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/11 18:58
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
@TableName("oa_dept")
public class OaDepartmentEntity extends BaseDO {

    // 部门名称
    private String departmentName;
    // 上级部门ID
    private Long parentId;
    //排序
    private Integer sort;
    //描述
    private String description;
    //部门编码
    private String departmentCode;
}

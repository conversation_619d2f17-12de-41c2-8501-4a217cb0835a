package com.miaowen.oa.system.framework.rpc.config;

import com.miaowen.oa.infra.api.config.ConfigApi;
import com.miaowen.oa.infra.api.file.FileApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(value = "systemRpcConfiguration", proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, ConfigApi.class})
public class RpcConfiguration {
}

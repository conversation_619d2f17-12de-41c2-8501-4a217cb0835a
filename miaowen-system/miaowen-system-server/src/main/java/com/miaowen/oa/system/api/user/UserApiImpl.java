package com.miaowen.oa.system.api.user;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.system.api.user.dto.UserInfoDTO;
import com.miaowen.oa.system.api.user.dto.UserSaveDTO;
import com.miaowen.oa.system.domain.dto.UserId;
import com.miaowen.oa.system.domain.model.OaUser;
import com.miaowen.oa.system.domain.repository.OaUserRepository;
import com.miaowen.oa.system.infrastructure.convertor.UserConvertor;
import com.miaowen.oa.system.infrastructure.entity.OaUserEntity;
import lombok.AllArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

import static com.miaowen.oa.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;

/**
 * UserApiImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */

@RestController
@Validated
@AllArgsConstructor
public class UserApiImpl implements AdminUserApi{
    private final OaUserRepository oaUserRepository;
    @Override
    public CommonResult<UserInfoDTO> getUserId(Long userId) {
        if (Objects.isNull(userId)){
            return CommonResult.error(USER_NOT_EXISTS);
        }
        OaUser user = oaUserRepository.findById(new UserId(userId));
        return CommonResult.success(UserConvertor.toDTO(user));
    }

    @Override
    public CommonResult<UserInfoDTO> getUserByUsernameOrPhone(String username) {
        if (!StringUtils.hasText(username)){
            return CommonResult.error(USER_NOT_EXISTS);
        }
        OaUserEntity user = oaUserRepository.getByUsernameOrPhone(username);
        return CommonResult.success(UserConvertor.entityToDTO(user));
    }

    @Override
    public CommonResult<UserInfoDTO> getUserByQyWechatUserId(String qyWechatUserId) {
        if (!StringUtils.hasText(qyWechatUserId)){
            return CommonResult.error(USER_NOT_EXISTS);
        }
        OaUserEntity user = oaUserRepository.getUserByQyWechatUserId(qyWechatUserId);
        return CommonResult.success(UserConvertor.entityToDTO(user));
    }

    @Override
    public CommonResult<Void> save(UserSaveDTO dto) {
        UserId id = new UserId(dto.getUserId());
        OaUser user = oaUserRepository.findById(id);
        OaUserEntity entity = user.toEntity();
        BeanUtils.copyProperties(dto, OaUserEntity.class);
        OaUser oaUser = OaUser.fromEntity(entity);
        oaUserRepository.save(oaUser);
        return CommonResult.success();
    }
}

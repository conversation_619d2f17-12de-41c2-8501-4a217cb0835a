package com.miaowen.oa.system.interfaces.controller;


import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.application.repository.OaRoleGroupService;
import com.miaowen.oa.system.interfaces.req.role.OaRoleGroupCreateRequest;
import com.miaowen.oa.system.interfaces.req.role.OaRoleGroupUpdateRequest;
import com.miaowen.oa.system.interfaces.res.role.OaRoleGroupListResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @Date 2025/7/10 19:57
 * @Company 武汉市妙闻网络科技有限公司
 * @Description /api/$version(/in)?/$service
 */
@Tag(name = "角色分组管理")
@Slf4j
@RequestMapping("/roleGroup")
@RestController
public class OaRoleGroupController {

    @Resource
    private OaRoleGroupService oaRoleGroupService;


    @Operation(summary = "角色分组列表")
    @PermitAll
    @GetMapping("")
    public CommonResult<?> page() {
        return CommonResult.success(oaRoleGroupService.roleGroupList());
    }


    @Operation(summary = "创建角色分组")
    @PermitAll
    @PostMapping("")
    public CommonResult<Long> createRoleGroup(@Validated @RequestBody OaRoleGroupCreateRequest request) {
        Long roleId = oaRoleGroupService.createRoleGroup(request);
        return CommonResult.success(roleId);
    }

    @Operation(summary = "编辑角色分组")
    @PermitAll
    @PutMapping("/{id}")
    public CommonResult<Void> editRoleGroup(@PathVariable("id") Long id, @Validated @RequestBody OaRoleGroupUpdateRequest request) {
        // 确保路径ID和请求体ID一致
        request.setId(id);
        oaRoleGroupService.editRoleGroup(request);
        return CommonResult.success();
    }


    @Operation(summary = "删除角色分组")
    @PermitAll
    @DeleteMapping("/{id}")
    public CommonResult<Void> deleteRoleGroup(@PathVariable("id") Long id) {
        oaRoleGroupService.deleteRoleGroup(id);
        return CommonResult.success();
    }

    @Operation(summary = "查询角色分组详情")
    @PermitAll
    @GetMapping("/{id}")
    public CommonResult<OaRoleGroupListResp> getRoleGroupDetail(@PathVariable("id") Long id) {
        OaRoleGroupListResp roleGroup = oaRoleGroupService.getRoleGroupDetail(id);
        return CommonResult.success(roleGroup);
    }


}

package com.miaowen.oa.system.domain.dto;

import lombok.Data;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

/**
 * 用户状态历史记录
 *
 * 重构内容：
 * 1. 增加消息字段，记录状态变更的原因或描述
 * 2. 增加操作人ID字段，便于追踪操作者
 * 3. 完善构造函数，支持更多参数组合
 * 4. 保持向后兼容性
 *
 * <AUTHOR>
 * @since 2025-07-29 (重构)
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
public class UserStatusHistory {

    /**
     * 用户状态
     */
    private final UserState state;

    /**
     * 时间戳
     */
    private final long timestamp;

    /**
     * 操作人姓名
     */
    private final String operator;

    /**
     * 操作人ID
     */
    private final Long operatorId;

    /**
     * 状态变更消息/原因
     */
    private final String message;

    // ========== 构造函数 ==========

    /**
     * 原有构造函数（保持兼容）
     */
    public UserStatusHistory(UserState state, long timestamp) {
        this.state = Objects.requireNonNull(state, "用户状态不能为空");
        this.timestamp = timestamp;
        this.operator = "system";
        this.operatorId = null;
        this.message = "";
    }

    /**
     * 带消息的构造函数（新增）
     */
    public UserStatusHistory(UserState state, long timestamp, String message) {
        this.state = Objects.requireNonNull(state, "用户状态不能为空");
        this.timestamp = timestamp;
        this.operator = "system";
        this.operatorId = null;
        this.message = Objects.requireNonNull(message, "消息不能为空");
    }

    /**
     * 带操作人的构造函数（新增）
     */
    public UserStatusHistory(UserState state, long timestamp, String operator, Long operatorId) {
        this.state = Objects.requireNonNull(state, "用户状态不能为空");
        this.timestamp = timestamp;
        this.operator = Objects.requireNonNull(operator, "操作人不能为空");
        this.operatorId = operatorId;
        this.message = "";
    }

    /**
     * 完整构造函数（新增）
     */
    public UserStatusHistory(UserState state, long timestamp, String operator, Long operatorId, String message) {
        this.state = Objects.requireNonNull(state, "用户状态不能为空");
        this.timestamp = timestamp;
        this.operator = Objects.requireNonNull(operator, "操作人不能为空");
        this.operatorId = operatorId;
        this.message = Objects.requireNonNull(message, "消息不能为空");
    }

    // ========== 工厂方法 ==========

    /**
     * 创建系统操作的状态历史
     */
    public static UserStatusHistory createSystemHistory(UserState state, String message) {
        return new UserStatusHistory(state, System.currentTimeMillis(), "system", null, message);
    }

    /**
     * 创建用户操作的状态历史
     */
    public static UserStatusHistory createUserHistory(UserState state, String operator, Long operatorId, String message) {
        return new UserStatusHistory(state, System.currentTimeMillis(), operator, operatorId, message);
    }

    // ========== 业务方法 ==========

    /**
     * 获取日期时间对象
     */
    public LocalDateTime getDateTime() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }

    /**
     * 是否为系统操作
     */
    public boolean isSystemOperation() {
        return "system".equals(this.operator);
    }

    /**
     * 是否有操作人ID
     */
    public boolean hasOperatorId() {
        return this.operatorId != null && this.operatorId > 0;
    }

    /**
     * 是否有消息
     */
    public boolean hasMessage() {
        return this.message != null && !this.message.trim().isEmpty();
    }

    /**
     * 获取格式化的历史记录描述
     */
    public String getFormattedDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(getDateTime().toString());
        sb.append(" - ");
        sb.append(this.operator);
        if (hasOperatorId()) {
            sb.append("(ID:").append(this.operatorId).append(")");
        }
        sb.append(" 将状态变更为：").append(this.state.getDescription());
        if (hasMessage()) {
            sb.append("，原因：").append(this.message);
        }
        return sb.toString();
    }

    /**
     * 获取简短描述
     */
    public String getShortDescription() {
        return this.state.getDescription() + (hasMessage() ? " - " + this.message : "");
    }
}
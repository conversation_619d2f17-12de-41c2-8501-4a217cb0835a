package com.miaowen.oa.system.infrastructure.entity;

/**
 * @ClassName OaUserEntity
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value ="oa_project_group")
public class OaProjectGroupEntity  extends BaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;



    private String groupName;




}

package com.miaowen.oa.system.infrastructure.config;

import com.miaowen.oa.system.domain.service.OperationLogService;
import com.miaowen.oa.system.infrastructure.filter.OperationLogFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 操作日志配置类
 * 
 * 功能说明：
 * 1. 配置操作日志相关的线程池
 * 2. 配置异步执行器
 * 3. 配置操作日志过滤器
 * 4. 支持通过配置文件开关功能
 * 
 * 配置说明：
 * - operation.log.filter.enabled: 是否启用过滤器方式（默认true）
 * - operation.log.async.enabled: 是否启用异步处理（默认true）
 * 
 * 性能优化：
 * - 线程池参数根据实际负载调优
 * - 过滤器可配置开关，灵活控制
 * - 异步处理避免阻塞主线程
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Configuration
public class OperationLogConfig {

    /**
     * 操作日志异步执行器
     * 
     * 用于异步记录操作日志，避免阻塞主线程
     * 
     * 性能优化：
     * - 核心线程数设置为CPU核心数
     * - 最大线程数根据实际负载调整
     * - 队列容量适中，避免内存溢出
     * - 使用CallerRunsPolicy保证任务不丢失
     */
    @Bean("operationLogExecutor")
    public Executor operationLogExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数（建议设置为CPU核心数）
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        
        // 最大线程数（核心线程数的2倍）
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        
        // 队列容量（适中的队列大小）
        executor.setQueueCapacity(200);
        
        // 线程名前缀
        executor.setThreadNamePrefix("operation-log-");
        
        // 拒绝策略：由调用线程执行，保证任务不丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程空闲时间
        executor.setKeepAliveSeconds(60);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 等待任务完成后再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("操作日志异步执行器初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 操作日志清理异步执行器
     * 
     * 用于异步清理过期的操作日志
     * 
     * 性能优化：
     * - 单线程处理，避免并发冲突
     * - 较大的队列容量，应对清理任务堆积
     * - 使用DiscardOldestPolicy，丢弃最老的任务
     */
    @Bean("operationLogCleanExecutor")
    public Executor operationLogCleanExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数（清理任务使用单线程即可）
        executor.setCorePoolSize(1);
        
        // 最大线程数
        executor.setMaxPoolSize(2);
        
        // 队列容量（清理任务可能会堆积）
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("operation-log-clean-");
        
        // 拒绝策略：丢弃最老的任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        
        // 线程空闲时间（清理任务间隔较长）
        executor.setKeepAliveSeconds(300);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 等待任务完成后再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("操作日志清理异步执行器初始化完成");
        
        return executor;
    }

    /**
     * 注册操作日志过滤器
     *
     * 通过配置 operation.log.filter.enabled=true 启用（默认启用）
     *
     * 功能说明：
     * - 自动拦截所有API请求
     * - 记录操作前后的数据变更
     * - 支持数据脱敏和安全处理
     * - 异步记录日志，不影响业务性能
     *
     * 配置优化：
     * - 只拦截API请求，过滤静态资源
     * - 设置合适的过滤器顺序
     * - 支持配置开关，便于调试
     */
    @Bean("operationLogFilterRegistration")
    @ConditionalOnProperty(name = "operation.log.filter.enabled", havingValue = "true", matchIfMissing = true)
    public FilterRegistrationBean<OperationLogFilter> operationLogFilterRegistration(ApplicationContext applicationContext) {
        log.info("开始注册操作日志过滤器");

        FilterRegistrationBean<OperationLogFilter> registration = new FilterRegistrationBean<>();

        // 创建过滤器实例
        OperationLogFilter filter = new OperationLogFilter();
        // 设置ApplicationContext，让过滤器可以获取Spring Bean
        filter.setApplicationContext(applicationContext);

        registration.setFilter(filter);

        // 设置过滤器名称
        registration.setName("operationLogFilter");

        // 设置URL模式（只拦截API请求）
        registration.addUrlPatterns("/api/*");

        // 设置过滤器顺序（在安全过滤器之后）
        registration.setOrder(100);

        // 设置是否启用
        registration.setEnabled(true);

        log.info("操作日志过滤器注册完成，拦截模式：/api/*，顺序：100");

        return registration;
    }

    /**
     * 操作日志统计执行器
     * 
     * 用于异步执行统计任务
     */
    @Bean("operationLogStatExecutor")
    public Executor operationLogStatExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(1);
        
        // 最大线程数
        executor.setMaxPoolSize(3);
        
        // 队列容量
        executor.setQueueCapacity(50);
        
        // 线程名前缀
        executor.setThreadNamePrefix("operation-log-stat-");
        
        // 拒绝策略：丢弃任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        
        // 线程空闲时间
        executor.setKeepAliveSeconds(120);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 等待任务完成后再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        
        log.info("操作日志统计执行器初始化完成");
        
        return executor;
    }
}

package com.miaowen.oa.system.domain.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.oa.system.domain.service.OaDepartmentLeaderRelationService;
import com.miaowen.oa.system.infrastructure.entity.OaDepartmentLeaderRelationEntity;
import com.miaowen.oa.system.infrastructure.mapper.OaDepartmentLeaderRelationMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 10:53
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Service
public class OaDepartmentLeaderRelationServiceImpl extends ServiceImpl<OaDepartmentLeaderRelationMapper, OaDepartmentLeaderRelationEntity> implements OaDepartmentLeaderRelationService {


    @Override
    public void logicDeleteBatchIds(Long deptId, List<Long> toDelete) {

        baseMapper.logicDelete(Wrappers.lambdaQuery(OaDepartmentLeaderRelationEntity.class).eq(OaDepartmentLeaderRelationEntity::getDeptId, deptId).in(OaDepartmentLeaderRelationEntity::getUserId, toDelete));
    }

    @Override
    public void logicDeleteByDeptIds(List<Long> allDeptIds) {
        baseMapper.logicDelete(Wrappers.lambdaQuery(OaDepartmentLeaderRelationEntity.class).in(OaDepartmentLeaderRelationEntity::getDeptId, allDeptIds));
    }
}

package com.miaowen.oa.system.infrastructure.mapper;

import com.miaowen.oa.framework.mybatis.core.mapper.CustomBaseMapper;
import com.miaowen.oa.system.infrastructure.entity.OaMenuRoleRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Mapper
public interface OaMenuRoleRelationMapper extends CustomBaseMapper<OaMenuRoleRelationEntity> {

    @Select("<script>" +
            "SELECT role_id AS roleId, menu_id AS menuId " +
            "FROM oa_menu_role_relation " +
            "WHERE delete_time = 0 and  role_id IN " +
            "<foreach item='id' collection='roleIds' open='(' separator=',' close=')'>" +
            "   #{id}" +
            "</foreach>" +
            "</script>")
    List<OaMenuRoleRelationEntity> findByUserIds(@Param("roleIds") List<Long> roleIds);
}

-- =====================================================
-- OA用户表字段更新DDL
-- 基于OaUserEntity.java中新增的字段生成
-- =====================================================

-- 使用数据库
USE `oa_system`;

-- =====================================================
-- 为oa_user表添加新字段
-- =====================================================

-- 1. 转正日期
ALTER TABLE `oa_user` 
ADD COLUMN `regular_date` date DEFAULT NULL COMMENT '转正日期：记录员工从试用期转为正式员工的日期，用于计算试用期时长和转正后工龄' AFTER `leave_notes`;

-- 2. 企业邮箱
ALTER TABLE `oa_user` 
ADD COLUMN `company_email` varchar(100) DEFAULT NULL COMMENT '企业邮箱：用于正式业务沟通的企业邮箱，格式如********************' AFTER `regular_date`;

-- 3. 员工类型
ALTER TABLE `oa_user` 
ADD COLUMN `employee_type` tinyint(1) DEFAULT 1 COMMENT '员工类型：1-正式员工，2-实习生，3-外包员工，4-其他' AFTER `company_email`;

-- 4. 员工状态
ALTER TABLE `oa_user` 
ADD COLUMN `employee_state` tinyint(1) DEFAULT 1 COMMENT '员工状态：1-试用期，2-准正式，3-待离职，4-已离职' AFTER `employee_type`;

-- 5. 入职日期
ALTER TABLE `oa_user` 
ADD COLUMN `entry_date` date DEFAULT NULL COMMENT '入职日期：工龄计算基础，试用期管理的起始时间' AFTER `employee_state`;

-- 6. 试用期开始日期
ALTER TABLE `oa_user` 
ADD COLUMN `probation_start_date` date DEFAULT NULL COMMENT '试用期开始日期：通常等于入职日期，用于精确的试用期管理' AFTER `entry_date`;

-- 7. 试用期结束日期
ALTER TABLE `oa_user` 
ADD COLUMN `probation_end_date` date DEFAULT NULL COMMENT '试用期结束日期：转正决策节点，试用期考核的截止时间' AFTER `probation_start_date`;

-- 8. 生日
ALTER TABLE `oa_user` 
ADD COLUMN `birthday` date DEFAULT NULL COMMENT '生日：用于员工关怀功能、年龄计算和统计分析' AFTER `probation_end_date`;

-- 9. 民族
ALTER TABLE `oa_user` 
ADD COLUMN `ethnicity` varchar(20) DEFAULT NULL COMMENT '民族：支持多元化管理和民族政策，使用国家标准民族代码' AFTER `birthday`;

-- 10. 政治面貌
ALTER TABLE `oa_user` 
ADD COLUMN `political_status` tinyint(1) DEFAULT NULL COMMENT '政治面貌：1-中共党员，2-中共预备党员，3-共青团员，4-民主党派，5-无党派人士，6-群众' AFTER `ethnicity`;

-- 11. 婚姻状况
ALTER TABLE `oa_user` 
ADD COLUMN `marital_status` tinyint(1) DEFAULT NULL COMMENT '婚姻状况：1-未婚，2-已婚，3-离异，4-丧偶' AFTER `political_status`;

-- 12. 学历
ALTER TABLE `oa_user` 
ADD COLUMN `education` tinyint(1) DEFAULT NULL COMMENT '学历：1-小学，2-初中，3-高中/中专，4-大专，5-本科，6-硕士，7-博士' AFTER `marital_status`;

-- 13. 毕业院校
ALTER TABLE `oa_user` 
ADD COLUMN `graduate_school` varchar(100) DEFAULT NULL COMMENT '毕业院校：教育背景详情，用于招聘质量评估和培养发展规划' AFTER `education`;

-- 14. 专业
ALTER TABLE `oa_user` 
ADD COLUMN `major` varchar(50) DEFAULT NULL COMMENT '专业：专业背景管理，用于专业匹配度分析和人才结构优化' AFTER `graduate_school`;

-- 15. 紧急联系人姓名（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `emergency_contact_name` varchar(50) DEFAULT NULL COMMENT '紧急联系人姓名：紧急情况处理的联系人' AFTER `major`;

-- 16. 紧急联系人电话（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `emergency_contact_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系人电话：24小时可达的紧急联系方式' AFTER `emergency_contact_name`;

-- 17. 银行卡号（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `bank_card_number` varchar(30) DEFAULT NULL COMMENT '银行卡号：薪资发放渠道，需要加密存储' AFTER `emergency_contact_phone`;

-- 18. 开户行（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `bank_name` varchar(100) DEFAULT NULL COMMENT '开户行：银行信息管理，用于财务处理效率优化' AFTER `bank_card_number`;

-- 19. 社保账号（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `social_security_number` varchar(30) DEFAULT NULL COMMENT '社保账号：社保缴纳管理的唯一标识' AFTER `bank_name`;

-- 20. 公积金账号（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `housing_fund_number` varchar(30) DEFAULT NULL COMMENT '公积金账号：公积金缴存管理' AFTER `social_security_number`;

-- 21. 工作地点（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `work_location` varchar(100) DEFAULT NULL COMMENT '工作地点：办公地点管理，支持总部、分支机构、客户现场、远程办公' AFTER `housing_fund_number`;

-- 22. 备注信息（注释掉的字段，根据需要可以启用）
-- ALTER TABLE `oa_user` 
-- ADD COLUMN `remarks` text DEFAULT NULL COMMENT '备注信息：扩展信息管理，记录特殊情况和管理决策支持信息' AFTER `work_location`;

-- =====================================================
-- 创建相关索引
-- =====================================================

-- 员工类型索引
CREATE INDEX `idx_employee_type` ON `oa_user` (`employee_type`);

-- 员工状态索引
CREATE INDEX `idx_employee_state` ON `oa_user` (`employee_state`);

-- 入职日期索引
CREATE INDEX `idx_entry_date` ON `oa_user` (`entry_date`);

-- 转正日期索引
CREATE INDEX `idx_regular_date` ON `oa_user` (`regular_date`);

-- 试用期日期范围索引
CREATE INDEX `idx_probation_period` ON `oa_user` (`probation_start_date`, `probation_end_date`);

-- 生日索引（用于生日提醒）
CREATE INDEX `idx_birthday` ON `oa_user` (`birthday`);

-- 学历索引
CREATE INDEX `idx_education` ON `oa_user` (`education`);

-- 政治面貌索引
CREATE INDEX `idx_political_status` ON `oa_user` (`political_status`);

-- 婚姻状况索引
CREATE INDEX `idx_marital_status` ON `oa_user` (`marital_status`);

-- 企业邮箱唯一索引
CREATE UNIQUE INDEX `uk_company_email` ON `oa_user` (`company_email`);

-- 毕业院校索引
CREATE INDEX `idx_graduate_school` ON `oa_user` (`graduate_school`);

-- 专业索引
CREATE INDEX `idx_major` ON `oa_user` (`major`);

-- 民族索引
CREATE INDEX `idx_ethnicity` ON `oa_user` (`ethnicity`);

-- 复合索引：员工类型+员工状态（常用组合查询）
CREATE INDEX `idx_employee_type_state` ON `oa_user` (`employee_type`, `employee_state`);

-- 复合索引：入职日期+员工状态（入职统计查询）
CREATE INDEX `idx_entry_date_state` ON `oa_user` (`entry_date`, `employee_state`);

-- 复合索引：学历+专业（教育背景查询）
CREATE INDEX `idx_education_major` ON `oa_user` (`education`, `major`);

-- =====================================================
-- 字段说明和使用建议
-- =====================================================

/*
新增字段说明：

1. 基础信息字段：
   - regular_date: 转正日期，用于试用期管理
   - company_email: 企业邮箱，区别于个人邮箱
   - employee_type: 员工类型，支持不同用工形式
   - employee_state: 员工状态，详细的状态管理

2. 时间管理字段：
   - entry_date: 入职日期，工龄计算基础
   - probation_start_date: 试用期开始日期
   - probation_end_date: 试用期结束日期
   - birthday: 生日，员工关怀功能

3. 个人信息字段：
   - ethnicity: 民族，多元化管理
   - political_status: 政治面貌，组织管理
   - marital_status: 婚姻状况，福利政策关联

4. 教育背景字段：
   - education: 学历，人才结构分析
   - graduate_school: 毕业院校，教育背景详情
   - major: 专业，专业匹配度分析

使用建议：

1. 数据迁移：
   - 对于现有用户，新字段默认为NULL
   - 可以通过数据导入或用户自主更新完善信息
   - 建议分批次更新，避免影响系统性能

2. 业务逻辑：
   - 试用期管理：probation_start_date + probation_end_date + regular_date
   - 员工生命周期：employee_state字段的状态流转
   - 权限控制：不同employee_type的权限差异化

3. 性能优化：
   - 已创建相关索引，支持常用查询场景
   - 复合索引优化多条件查询性能
   - 建议定期分析查询模式，优化索引策略

4. 数据安全：
   - 敏感信息字段（如银行卡号、社保账号）建议加密存储
   - 个人隐私信息需要严格的访问权限控制
   - 符合《个人信息保护法》的合规要求

5. 扩展性：
   - 注释掉的字段可根据业务需要启用
   - 预留了足够的扩展空间
   - 支持未来业务需求的变化
*/

-- =====================================================
-- 验证脚本
-- =====================================================

-- 查看表结构
-- DESCRIBE `oa_user`;

-- 查看索引
-- SHOW INDEX FROM `oa_user`;

-- 统计新字段的数据分布（执行更新后）
-- SELECT 
--     employee_type,
--     employee_state,
--     COUNT(*) as count
-- FROM oa_user 
-- WHERE delete_time = 0
-- GROUP BY employee_type, employee_state;

-- =====================================================
-- 回滚脚本（如需回滚，请谨慎执行）
-- =====================================================

/*
-- 删除新增的索引
DROP INDEX `idx_employee_type` ON `oa_user`;
DROP INDEX `idx_employee_state` ON `oa_user`;
DROP INDEX `idx_entry_date` ON `oa_user`;
DROP INDEX `idx_regular_date` ON `oa_user`;
DROP INDEX `idx_probation_period` ON `oa_user`;
DROP INDEX `idx_birthday` ON `oa_user`;
DROP INDEX `idx_education` ON `oa_user`;
DROP INDEX `idx_political_status` ON `oa_user`;
DROP INDEX `idx_marital_status` ON `oa_user`;
DROP INDEX `uk_company_email` ON `oa_user`;
DROP INDEX `idx_graduate_school` ON `oa_user`;
DROP INDEX `idx_major` ON `oa_user`;
DROP INDEX `idx_ethnicity` ON `oa_user`;
DROP INDEX `idx_employee_type_state` ON `oa_user`;
DROP INDEX `idx_entry_date_state` ON `oa_user`;
DROP INDEX `idx_education_major` ON `oa_user`;

-- 删除新增的字段
ALTER TABLE `oa_user` DROP COLUMN `regular_date`;
ALTER TABLE `oa_user` DROP COLUMN `company_email`;
ALTER TABLE `oa_user` DROP COLUMN `employee_type`;
ALTER TABLE `oa_user` DROP COLUMN `employee_state`;
ALTER TABLE `oa_user` DROP COLUMN `entry_date`;
ALTER TABLE `oa_user` DROP COLUMN `probation_start_date`;
ALTER TABLE `oa_user` DROP COLUMN `probation_end_date`;
ALTER TABLE `oa_user` DROP COLUMN `birthday`;
ALTER TABLE `oa_user` DROP COLUMN `ethnicity`;
ALTER TABLE `oa_user` DROP COLUMN `political_status`;
ALTER TABLE `oa_user` DROP COLUMN `marital_status`;
ALTER TABLE `oa_user` DROP COLUMN `education`;
ALTER TABLE `oa_user` DROP COLUMN `graduate_school`;
ALTER TABLE `oa_user` DROP COLUMN `major`;
*/

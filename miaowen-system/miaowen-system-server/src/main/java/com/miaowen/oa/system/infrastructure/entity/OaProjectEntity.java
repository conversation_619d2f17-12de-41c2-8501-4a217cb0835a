package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:48
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
@TableName("oa_project")
public class OaProjectEntity extends BaseDO {
    /**
     * 项目名字
     */
    private String projectName;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 板块
     */
    private Integer plate;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 接口域名
     */
    private String apiDomain;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     */
    private Integer state;




}

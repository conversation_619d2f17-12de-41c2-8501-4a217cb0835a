package com.miaowen.oa.system.interfaces.res.menu;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data

public class OaMenuPageResp {


    private Long id;

    /**
     * 角色id
     */
    private Long roleId;


    /**
     * 上级菜单id
     */
    private Long parentId;


    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 菜单类型：1-目录，2-菜单，3-权限点
     */
    private Integer menuType;

    /**
     * 菜单路径
     */
    private String menuUrl;


    /**
     * 路由路径
     */
    private String routeUrl;


    /**
     * 权限标识
     */
    private String authoritySign;



    /**
     * (禁用)状态: 0正常 1禁用
     */
    private Integer state;


    /**
     * 图标
     */
    private String icon;

    /**
     * 密码
     */
    private Integer sort;

    /**
     * 其他设置
     */
    private List<String> settings;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


    private List<OaMenuPageResp> children;

}

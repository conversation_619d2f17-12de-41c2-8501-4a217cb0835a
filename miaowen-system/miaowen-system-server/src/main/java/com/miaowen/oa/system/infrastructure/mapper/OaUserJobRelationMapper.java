package com.miaowen.oa.system.infrastructure.mapper;

import com.miaowen.oa.framework.mybatis.core.mapper.CustomBaseMapper;
import com.miaowen.oa.system.infrastructure.entity.OaUserJobRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 19:22
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Mapper
public interface OaUserJobRelationMapper extends CustomBaseMapper<OaUserJobRelationEntity> {

    @Select("<script>" +
            "SELECT user_id AS userId, job_id AS jobId " +
            "FROM oa_user_job_relation " +
            "WHERE  delete_time = 0 and user_id IN " +
            "<foreach item='id' collection='userIds' open='(' separator=',' close=')'>" +
            "   #{id}" +
            "</foreach>" +
            "</script>")
    List<OaUserJobRelationEntity> findByUserIds(@Param("userIds") List<Long> userIds);
}

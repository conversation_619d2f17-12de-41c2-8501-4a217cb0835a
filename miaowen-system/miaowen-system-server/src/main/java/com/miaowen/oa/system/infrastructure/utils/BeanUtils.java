package com.miaowen.oa.system.infrastructure.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 
 * BeanUtils :
 * 
 *
 * <AUTHOR>
 * @since 2025-03-22
 */
public class BeanUtils {

    public final static CopyOptions COPY_OPTIONS = CopyOptions.create()
        .setIgnoreError(true)
        .setIgnoreNullValue(true);

    public static  <T> List<T> copyList(List<?> list , Class<T> aClass){
        if (CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
       return list.stream().map(item -> BeanUtil.copyProperties(item, aClass)).collect(Collectors.toList());
    }



    public static <T> T copy(Object source, Class<T> target) {
        try {
            T t = target.newInstance();
            if (Objects.isNull(source)) {
                return t;
            }

            BeanUtil.copyProperties(source, t, COPY_OPTIONS);
            return t;
        } catch (Exception e) {

        }
        return null;
    }
}

package com.miaowen.oa.system.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.annotations.VisibleForTesting;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.framework.common.util.stream.StreamUtil;
import com.miaowen.oa.system.domain.service.OaDictItemService;
import com.miaowen.oa.system.domain.service.OaDictService;
import com.miaowen.oa.system.infrastructure.entity.*;
import com.miaowen.oa.system.infrastructure.mapper.OaDictMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaUserMapper;
import com.miaowen.oa.system.interfaces.req.dict.DictPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictSaveRequest;
import com.miaowen.oa.system.interfaces.req.dict.DictUpdateRequest;
import com.miaowen.oa.system.interfaces.res.dict.OaDictDetailResp;
import com.miaowen.oa.system.interfaces.res.dict.OaDictPageResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.miaowen.oa.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @Date 2025/7/15 14:56
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Service
public class OaDictServiceImpl extends ServiceImpl<OaDictMapper, OaDictEntity> implements OaDictService {

    @Autowired
    private OaDictItemService oaDictItemService;

    @Autowired
    private OaUserMapper oaUserMapper;


    @Override
    public void createDict(DictSaveRequest dictSaveRequest) {
        // 校验字典类型的名字的唯一性
        validateDictTypeNameUnique(null, dictSaveRequest.getDictName());
        // 校验字典类型的类型的唯一性
        validateDictCodeUnique(dictSaveRequest.getDictCode());

        // 插入字典类型
        OaDictEntity oaDictEntity = BeanUtils.toBean(dictSaveRequest, OaDictEntity.class);
        baseMapper.insert(oaDictEntity);
    }

    @Override
    public void updateDict(DictUpdateRequest dictUpdateRequest) {
        // 校验自己存在
        validateDictTypeExists(dictUpdateRequest.getId());
        // 校验字典类型的名字的唯一性
        validateDictTypeNameUnique(dictUpdateRequest.getId(), dictUpdateRequest.getDictName());
        // 更新字典类型
        OaDictEntity oaDictEntity = BeanUtils.toBean(dictUpdateRequest, OaDictEntity.class);
        baseMapper.updateById(oaDictEntity);
    }

    @Override
    public void deleteDict(Long id) {
        // 校验是否存在
        OaDictEntity oaDictEntity = validateDictTypeExists(id);
        // 删除字典
        baseMapper.logicDeleteById(id);
        // 校验是否有字典值数据，如果有的话，将字典值数据一起删除
        if (oaDictItemService.lambdaQuery().eq(OaDictItemEntity::getDictId, oaDictEntity.getId()).eq(OaDictItemEntity::getDeleteTime, 0).count() > 0) {
            oaDictItemService.logicDeleteByDictId(oaDictEntity.getId());
        }

    }

    @Override
    public PageResult<OaDictPageResp> getDictPage(DictPageQueryRequest dictPageQueryRequest) {
        IPage<OaDictEntity> iPage = new Page<>(dictPageQueryRequest.getPage(), dictPageQueryRequest.getPageSize());

        IPage<OaDictEntity> oaDictEntityIPage = baseMapper.selectPage(iPage, Wrappers.lambdaQuery(OaDictEntity.class).eq(OaDictEntity::getDeleteTime, 0).like(StrUtil.isNotEmpty(dictPageQueryRequest.getDictName()), OaDictEntity::getDictName, dictPageQueryRequest.getDictName())
                .eq(ObjectUtil.isNotNull(dictPageQueryRequest.getState()), OaDictEntity::getState, dictPageQueryRequest.getState())
                .orderByAsc(OaDictEntity::getSort)
                .orderByDesc(OaDictEntity::getUpdateTime)
        );

        List<OaDictPageResp> oaDictPageRespList = oaDictEntityIPage.getRecords().stream().map(oaDict -> BeanUtils.toBean(oaDict, OaDictPageResp.class)).toList();

        List<OaUserEntity> oaUserList = oaUserMapper.selectList(Wrappers.lambdaQuery(OaUserEntity.class).eq(OaUserEntity::getDeleteTime, 0));
        Map<Long, OaUserEntity> userMap = StreamUtil.map(oaUserList, OaUserEntity::getId);

        for (OaDictPageResp oaDictPageResp : oaDictPageRespList) {
            OaUserEntity oaUserEntity = userMap.get(oaDictPageResp.getUpdater());
            if (oaUserEntity != null) {
                oaDictPageResp.setUpdaterName(oaUserEntity.getRealName());
            }
        }
        return new PageResult<>(oaDictPageRespList, oaDictEntityIPage.getTotal());

    }

    @Override
    public OaDictDetailResp getDictDetail(Long id) {
        OaDictEntity oaDictEntity = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictEntity.class).eq(OaDictEntity::getId, id).eq(OaDictEntity::getDeleteTime, 0));
        return BeanUtils.toBean(oaDictEntity, OaDictDetailResp.class);
    }

    private void validateDictTypeNameUnique(Long id, String name) {
        OaDictEntity dictType = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictEntity.class).eq(OaDictEntity::getDictName, name).eq(OaDictEntity::getDeleteTime, 0));
        if (dictType == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的字典类型
        if (id == null) {
            throw exception(DICT_TYPE_NAME_DUPLICATE);
        }
        if (!dictType.getId().equals(id)) {
            throw exception(DICT_TYPE_NAME_DUPLICATE);
        }
    }

    private void validateDictCodeUnique(String code) {
        if (StrUtil.isEmpty(code)) {
            return;
        }
        OaDictEntity dictType = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictEntity.class).eq(OaDictEntity::getDictCode, code).eq(OaDictEntity::getDeleteTime, 0));

        if (dictType != null) {
            throw exception(DICT_TYPE_TYPE_DUPLICATE);
        }

    }

    private OaDictEntity validateDictTypeExists(Long id) {
        OaDictEntity oaDict = baseMapper.selectOne(Wrappers.lambdaQuery(OaDictEntity.class).eq(OaDictEntity::getId, id).eq(OaDictEntity::getDeleteTime, 0));
        if (oaDict == null) {
            throw exception(DICT_TYPE_NOT_EXISTS);
        }
        return oaDict;
    }
}

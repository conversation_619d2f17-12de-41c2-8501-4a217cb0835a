package com.miaowen.oa.system.infrastructure.filter;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * 可缓存请求体的HttpServletRequest包装类
 * 
 * 功能说明：
 * 1. 缓存请求体内容，支持多次读取
 * 2. 解决InputStream只能读取一次的问题
 * 3. 用于在过滤器中记录请求参数
 * 4. 遵循空间局部性原理，优化内存访问
 * 
 * 性能优化：
 * - 一次性读取并缓存，避免重复IO操作
 * - 使用字节数组存储，减少内存碎片
 * - 延迟创建Reader，节省内存
 * - 支持大文件的流式处理
 * 
 * 设计原则：
 * - 透明性：对业务代码完全透明
 * - 兼容性：完全兼容原有的Servlet API
 * - 性能：最小化性能影响
 * - 安全性：防止内存泄漏
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
public class CachedBodyHttpServletRequest extends HttpServletRequestWrapper {

    /**
     * 缓存的请求体数据
     * 使用byte[]而不是String，避免编码转换的性能损耗
     */
    private final byte[] cachedBody;

    /**
     * 最大缓存大小（10MB）
     * 防止大文件上传导致内存溢出
     */
    private static final int MAX_CACHE_SIZE = 10 * 1024 * 1024;

    /**
     * 构造函数
     * 
     * 空间局部性优化：一次性读取所有数据到连续内存
     * 
     * @param request 原始请求对象
     * @throws IOException IO异常
     */
    public CachedBodyHttpServletRequest(HttpServletRequest request) throws IOException {
        super(request);
        
        // 获取Content-Length，用于预分配内存大小
        int contentLength = request.getContentLength();
        
        // 检查内容长度，防止内存溢出
        if (contentLength > MAX_CACHE_SIZE) {
            throw new IOException("请求体过大，超过最大缓存限制: " + MAX_CACHE_SIZE + " bytes");
        }
        
        // 读取请求体数据
        InputStream requestInputStream = request.getInputStream();
        if (requestInputStream != null) {
            // 使用Spring的工具类，性能更好
            this.cachedBody = StreamUtils.copyToByteArray(requestInputStream);
        } else {
            this.cachedBody = new byte[0];
        }
    }

    /**
     * 重写getInputStream方法
     * 
     * 时间局部性优化：返回基于缓存数据的新流
     * 
     * @return ServletInputStream
     * @throws IOException IO异常
     */
    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new CachedBodyServletInputStream(this.cachedBody);
    }

    /**
     * 重写getReader方法
     * 
     * @return BufferedReader
     * @throws IOException IO异常
     */
    @Override
    public BufferedReader getReader() throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.cachedBody);
        return new BufferedReader(new InputStreamReader(byteArrayInputStream, getCharacterEncodingOrDefault()));
    }

    /**
     * 获取缓存的请求体内容（字符串形式）
     * 
     * 性能优化：延迟转换为字符串，只在需要时才转换
     * 
     * @return 请求体字符串
     */
    public String getCachedBody() {
        return new String(this.cachedBody, getCharacterEncodingOrDefault());
    }

    /**
     * 获取缓存的请求体内容（字节数组形式）
     * 
     * @return 请求体字节数组
     */
    public byte[] getCachedBodyBytes() {
        // 返回副本，防止外部修改
        return this.cachedBody.clone();
    }

    /**
     * 获取请求体大小
     * 
     * @return 请求体大小（字节）
     */
    public int getCachedBodySize() {
        return this.cachedBody.length;
    }

    /**
     * 检查是否有请求体内容
     * 
     * @return 是否有内容
     */
    public boolean hasCachedBody() {
        return this.cachedBody.length > 0;
    }

    /**
     * 获取字符编码，如果未设置则使用UTF-8
     * 
     * @return 字符编码
     */
    private java.nio.charset.Charset getCharacterEncodingOrDefault() {
        String encoding = getCharacterEncoding();
        if (encoding != null) {
            try {
                return java.nio.charset.Charset.forName(encoding);
            } catch (Exception e) {
                // 如果编码不支持，使用默认编码
            }
        }
        return StandardCharsets.UTF_8;
    }

    /**
     * 缓存的ServletInputStream实现
     * 
     * 功能说明：
     * 1. 基于字节数组实现的输入流
     * 2. 支持Servlet 3.0的异步读取API
     * 3. 性能优化的流实现
     */
    private static class CachedBodyServletInputStream extends ServletInputStream {

        /**
         * 基于字节数组的输入流
         */
        private final ByteArrayInputStream cachedBodyInputStream;

        /**
         * 构造函数
         * 
         * @param cachedBody 缓存的请求体数据
         */
        public CachedBodyServletInputStream(byte[] cachedBody) {
            this.cachedBodyInputStream = new ByteArrayInputStream(cachedBody);
        }

        /**
         * 检查是否已读取完毕
         * 
         * @return 是否已完成
         */
        @Override
        public boolean isFinished() {
            try {
                return cachedBodyInputStream.available() == 0;
            } catch (Exception e) {
                return true;
            }
        }

        /**
         * 检查是否准备好读取
         * 
         * @return 是否准备好
         */
        @Override
        public boolean isReady() {
            return true;
        }

        /**
         * 设置读取监听器（Servlet 3.0异步API）
         * 
         * @param readListener 读取监听器
         */
        @Override
        public void setReadListener(ReadListener readListener) {
            throw new UnsupportedOperationException("不支持异步读取监听器");
        }

        /**
         * 读取单个字节
         * 
         * @return 字节值，如果到达流末尾则返回-1
         * @throws IOException IO异常
         */
        @Override
        public int read() throws IOException {
            return cachedBodyInputStream.read();
        }

        /**
         * 读取字节数组
         * 
         * @param b 目标字节数组
         * @return 实际读取的字节数
         * @throws IOException IO异常
         */
        @Override
        public int read(byte[] b) throws IOException {
            return cachedBodyInputStream.read(b);
        }

        /**
         * 读取字节数组的指定部分
         * 
         * @param b 目标字节数组
         * @param off 起始偏移量
         * @param len 读取长度
         * @return 实际读取的字节数
         * @throws IOException IO异常
         */
        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            return cachedBodyInputStream.read(b, off, len);
        }

        /**
         * 跳过指定数量的字节
         * 
         * @param n 要跳过的字节数
         * @return 实际跳过的字节数
         * @throws IOException IO异常
         */
        @Override
        public long skip(long n) throws IOException {
            return cachedBodyInputStream.skip(n);
        }

        /**
         * 获取可读取的字节数
         * 
         * @return 可读取的字节数
         * @throws IOException IO异常
         */
        @Override
        public int available() throws IOException {
            return cachedBodyInputStream.available();
        }

        /**
         * 关闭流
         * 
         * @throws IOException IO异常
         */
        @Override
        public void close() throws IOException {
            cachedBodyInputStream.close();
        }

        /**
         * 标记当前位置
         * 
         * @param readlimit 标记的有效范围
         */
        @Override
        public synchronized void mark(int readlimit) {
            cachedBodyInputStream.mark(readlimit);
        }

        /**
         * 重置到标记位置
         * 
         * @throws IOException IO异常
         */
        @Override
        public synchronized void reset() throws IOException {
            cachedBodyInputStream.reset();
        }

        /**
         * 检查是否支持标记
         * 
         * @return 是否支持标记
         */
        @Override
        public boolean markSupported() {
            return cachedBodyInputStream.markSupported();
        }
    }
}

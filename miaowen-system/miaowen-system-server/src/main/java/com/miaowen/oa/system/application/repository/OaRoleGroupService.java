package com.miaowen.oa.system.application.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.system.infrastructure.entity.OaMenuRoleRelationEntity;
import com.miaowen.oa.system.infrastructure.entity.OaRoleEntity;
import com.miaowen.oa.system.infrastructure.entity.OaRoleGroupEntity;
import com.miaowen.oa.system.infrastructure.entity.OaUserRoleRelationEntity;
import com.miaowen.oa.system.infrastructure.mapper.OaRoleGroupMapper;
import com.miaowen.oa.system.infrastructure.mapper.OaRoleMapper;
import com.miaowen.oa.system.interfaces.req.role.OaRoleGroupCreateRequest;
import com.miaowen.oa.system.interfaces.req.role.OaRoleGroupUpdateRequest;
import com.miaowen.oa.system.interfaces.res.role.OaRoleGroupListResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.miaowen.oa.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.miaowen.oa.system.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:50
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Slf4j
@Repository
public class OaRoleGroupService {


    @Resource
    private OaRoleGroupMapper oaRoleGroupMapper;


    @Resource
    private OaRoleMapper oaRoleMapper;

    public List<OaRoleGroupListResp> roleGroupList() {
        try {
            LambdaQueryWrapper<OaRoleGroupEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaRoleGroupEntity::getDeleteTime, 0)
                    .orderByAsc(OaRoleGroupEntity::getSort)
                    .orderByAsc(OaRoleGroupEntity::getId);

            List<OaRoleGroupEntity> oaRoleGroupEntities = oaRoleGroupMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(oaRoleGroupEntities)) {
                return new ArrayList<>();
            }

            // 过滤空对象并转换
            List<OaRoleGroupListResp> result = oaRoleGroupEntities.stream()
                    .filter(Objects::nonNull)
                    .map(entity -> BeanUtils.toBean(entity, OaRoleGroupListResp.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return result;
        } catch (Exception e) {
            // 记录日志并返回空列表，不抛出异常（列表接口）
            log.error("查询角色分组列表失败", e);
            return new ArrayList<>();
        }
    }

    public Long createRoleGroup(OaRoleGroupCreateRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw exception(ROLE_GROUP_CREATE_FAILED);
        }

        // 校验分组名称不能为空
        if (Objects.isNull(request.getGroupName()) || request.getGroupName().trim().isEmpty()) {
            throw exception(ROLE_GROUP_CREATE_FAILED);
        }

        // 校验分组名称是否重复
        validateGroupNameNotDuplicate(request.getGroupName(), null);

        OaRoleGroupEntity entity = BeanUtils.toBean(request, OaRoleGroupEntity.class);
        if (Objects.isNull(entity)) {
            throw exception(ROLE_GROUP_CREATE_FAILED);
        }

        try {
            oaRoleGroupMapper.insert(entity);
            return entity.getId();
        } catch (Exception e) {
            throw exception(ROLE_GROUP_CREATE_FAILED);
        }
    }

    public void editRoleGroup(OaRoleGroupUpdateRequest request) {
        // 参数校验
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw exception(ROLE_GROUP_UPDATE_FAILED);
        }

        // 校验分组名称不能为空
        if (Objects.isNull(request.getGroupName()) || request.getGroupName().trim().isEmpty()) {
            throw exception(ROLE_GROUP_UPDATE_FAILED);
        }

        // 校验角色分组是否存在
        OaRoleGroupEntity existingEntity = oaRoleGroupMapper.selectById(request.getId());
        if (existingEntity == null) {
            throw exception(ROLE_GROUP_NOT_EXISTS);
        }

        // 校验分组名称是否重复（排除自己）
        validateGroupNameNotDuplicate(request.getGroupName(), request.getId());

        OaRoleGroupEntity entity = BeanUtils.toBean(request, OaRoleGroupEntity.class);
        if (Objects.isNull(entity)) {
            throw exception(ROLE_GROUP_UPDATE_FAILED);
        }

        try {
            oaRoleGroupMapper.updateById(entity);
        } catch (Exception e) {
            throw exception(ROLE_GROUP_UPDATE_FAILED);
        }
    }

    public void deleteRoleGroup(Long id) {
        // 参数校验
        if (Objects.isNull(id)) {
            throw exception(ROLE_GROUP_DELETE_FAILED);
        }

        // 校验角色分组是否存在
        OaRoleGroupEntity entity = oaRoleGroupMapper.selectById(id);
        if (entity == null) {
            throw exception(ROLE_GROUP_NOT_EXISTS);
        }

        // 校验角色分组下是否存在角色
        validateRoleGroupHasNoRoles(id);

        try {
            // 删除角色分组
            oaRoleGroupMapper.logicDeleteById(id);
        } catch (Exception e) {
            throw exception(ROLE_GROUP_DELETE_FAILED);
        }
    }

    /**
     * 获取角色分组详情
     */
    public OaRoleGroupListResp getRoleGroupDetail(Long id) {
        if (Objects.isNull(id)) {
            throw exception(ROLE_GROUP_NOT_EXISTS);
        }

        LambdaQueryWrapper<OaRoleGroupEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaRoleGroupEntity::getId, id)
                .eq(OaRoleGroupEntity::getDeleteTime, 0);
        OaRoleGroupEntity entity = oaRoleGroupMapper.selectOne(wrapper);
        if (entity == null) {
            throw exception(ROLE_GROUP_NOT_EXISTS);
        }

        return BeanUtils.toBean(entity, OaRoleGroupListResp.class);
    }

    /**
     * 校验分组名称是否重复
     */
    private void validateGroupNameNotDuplicate(String groupName, Long excludeId) {
        if (Objects.isNull(groupName) || groupName.trim().isEmpty()) {
            return;
        }

        LambdaQueryWrapper<OaRoleGroupEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaRoleGroupEntity::getGroupName, groupName.trim())
                .eq(OaRoleGroupEntity::getDeleteTime, 0);

        // 排除自己（用于更新时的校验）
        if (Objects.nonNull(excludeId)) {
            wrapper.ne(OaRoleGroupEntity::getId, excludeId);
        }

        List<OaRoleGroupEntity> existingGroups = oaRoleGroupMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(existingGroups)) {
            throw exception(ROLE_GROUP_NAME_DUPLICATE);
        }
    }

    /**
     * 校验角色分组下是否存在角色
     */
    private void validateRoleGroupHasNoRoles(Long roleGroupId) {
        if (Objects.isNull(roleGroupId)) {
            return;
        }

        LambdaQueryWrapper<OaRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaRoleEntity::getRoleGroupId, roleGroupId)
                .eq(OaRoleEntity::getDeleteTime, 0);

        List<OaRoleEntity> roles = oaRoleMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(roles)) {
            throw exception(ROLE_GROUP_HAS_ROLES);
        }
    }
}

package com.miaowen.oa.system.interfaces.res.application;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/17 15:36
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaApplicationPageResp {

    private Long id;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 电脑端链接
     */
    private String pcUrl;

    /**
     * 移动端链接
     */
    private String mobileUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 - 正常 1 - 禁用
     */
    private Integer state;


    /**
     * 最后更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新者
     */
    private Long updater;

    /**
     * 更新人姓名
     */
    private String updaterName;
}

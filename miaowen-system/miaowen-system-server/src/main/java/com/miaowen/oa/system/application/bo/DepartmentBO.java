package com.miaowen.oa.system.application.bo;


import lombok.Data;

import java.util.List;

/**
 * 部门业务对象
 * 用于企业微信数据同步
 *
 * <AUTHOR>
 * @since 2025-07-24
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
public class DepartmentBO {
    
    /**
     * 部门ID
     */
    private Integer id;
    
    /**
     * 部门名称
     */
    private String name;
    
    /**
     * 英文名称
     */
    private String name_en;
    
    /**
     * 部门负责人的UserID列表
     */
    private List<String> department_leader;
    
    /**
     * 父部门ID，根部门为1
     */
    private Integer parentid;
    
    /**
     * 在父部门中的次序值，order值大的排序靠前
     */
    private Integer order;
    
    /**
     * 用户列表（员工列表）
     */
//    private List<DepartmentStaffDTO> userlist;
}

package com.miaowen.oa.system.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.infrastructure.entity.OaJobEntity;
import com.miaowen.oa.system.interfaces.req.job.JobPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.job.JobSaveRequest;
import com.miaowen.oa.system.interfaces.req.job.JobUpdateRequest;
import com.miaowen.oa.system.interfaces.res.job.OaJobDetailResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobPageRsep;

/**
 * <AUTHOR>
 * @Date 2025/7/15 10:16
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
public interface OaJobService extends IService<OaJobEntity> {
    void addJob(JobSaveRequest jobSaveRequest);

    void updateJob(JobUpdateRequest jobUpdateRequest);

    PageResult<OaJobPageRsep> getJobPageList(JobPageQueryRequest jobPageQueryRequest);

    OaJobDetailResp getJobById(Long id);

    void removeById(Long id);
}

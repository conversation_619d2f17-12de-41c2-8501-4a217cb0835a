package com.miaowen.oa.system.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.miaowen.oa.system.infrastructure.entity.OaOperationLogEntity;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志Mapper接口
 * 
 * 功能说明：
 * 1. 继承BaseMapper提供基础CRUD操作
 * 2. 提供自定义查询方法
 * 3. 支持复杂的业务查询需求
 * 4. 优化的SQL查询，利用数据库索引
 * 5. 支持统计分析查询
 * 
 * 设计原则：
 * - 基础操作使用MyBatis-Plus的BaseMapper
 * - 复杂查询使用自定义SQL
 * - 统计查询使用原生SQL提高性能
 * - 合理利用数据库索引
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Mapper
public interface OaOperationLogMapper extends BaseMapper<OaOperationLogEntity> {
    
    // ========== 统计查询方法 ==========

    /**
     * 统计指定时间范围内的操作次数
     * 
     * 利用索引：idx_create_time, idx_delete_create_time
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select("SELECT COUNT(*) FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime}")
    Long countOperationsByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定用户在时间范围内的操作次数
     * 
     * 利用索引：idx_user_type_time
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select("SELECT COUNT(*) FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND user_id = #{userId} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime}")
    Long countUserOperations(@Param("userId") Long userId,
                            @Param("startTime") LocalDateTime startTime,
                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计操作类型分布
     * 
     * 利用索引：idx_create_time
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作类型分布
     */
    @Select("SELECT operation_type, COUNT(*) as count " +
            "FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY operation_type " +
            "ORDER BY count DESC")
    List<Map<String, Object>> countOperationTypeDistribution(@Param("startTime") LocalDateTime startTime,
                                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户活跃度排行
     * 
     * 利用索引：idx_user_type_time
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 用户活跃度排行
     */
    @Select("SELECT user_id, username, COUNT(*) as operation_count, " +
            "SUM(CASE WHEN operation_state = 1 THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN operation_state = 0 THEN 1 ELSE 0 END) as failed_count, " +
            "AVG(execution_time) as avg_execution_time, " +
            "MAX(create_time) as last_operation_time " +
            "FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY user_id, username " +
            "ORDER BY operation_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getUserActivityRanking(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime,
                                                     @Param("limit") Integer limit);

    /**
     * 统计风险等级分布
     * 
     * 利用索引：idx_risk_time
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 风险等级分布
     */
    @Select("SELECT risk_level, COUNT(*) as count " +
            "FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY risk_level " +
            "ORDER BY risk_level DESC")
    List<Map<String, Object>> countRiskLevelDistribution(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每日操作趋势
     * 
     * 利用索引：idx_create_time
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每日操作趋势
     */
    @Select("SELECT DATE(create_time) as operation_date, " +
            "COUNT(*) as operation_count, " +
            "SUM(CASE WHEN operation_state = 1 THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN operation_state = 0 THEN 1 ELSE 0 END) as failed_count, " +
            "AVG(execution_time) as avg_execution_time, " +
            "COUNT(DISTINCT user_id) as active_user_count " +
            "FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE(create_time) " +
            "ORDER BY operation_date ASC")
    List<Map<String, Object>> getDailyOperationTrend(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计操作模块分布
     * 
     * 利用索引：idx_module_time
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作模块分布
     */
    @Select("SELECT operation_module, COUNT(*) as count " +
            "FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY operation_module " +
            "ORDER BY count DESC")
    List<Map<String, Object>> countOperationModuleDistribution(@Param("startTime") LocalDateTime startTime,
                                                              @Param("endTime") LocalDateTime endTime);

    // ========== 业务查询方法 ==========

    /**
     * 查询用户最近操作记录
     * 
     * 利用索引：idx_user_type_time
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近操作记录
     */
    @Select("SELECT * FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND user_id = #{userId} " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<OaOperationLogEntity> selectUserRecentOperations(@Param("userId") Long userId,
                                                          @Param("limit") Integer limit);

    /**
     * 查询业务数据操作历史
     * 
     * 利用索引：idx_business_time
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 业务数据操作历史
     */
    @Select("SELECT * FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND business_type = #{businessType} " +
            "AND business_id = #{businessId} " +
            "ORDER BY create_time DESC")
    List<OaOperationLogEntity> selectBusinessOperationHistory(@Param("businessType") String businessType,
                                                              @Param("businessId") Long businessId);

    /**
     * 查询高风险操作记录
     * 
     * 利用索引：idx_risk_time
     * 
     * @param riskLevel 风险等级
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 高风险操作记录
     */
    @Select("SELECT * FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND risk_level >= #{riskLevel} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY create_time DESC")
    List<OaOperationLogEntity> selectHighRiskOperations(@Param("riskLevel") Integer riskLevel,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询慢操作记录
     * 
     * @param threshold 执行时间阈值（毫秒）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 慢操作记录
     */
    @Select("SELECT * FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND execution_time >= #{threshold} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY execution_time DESC " +
            "LIMIT #{limit}")
    List<OaOperationLogEntity> selectSlowOperations(@Param("threshold") Long threshold,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime,
                                                    @Param("limit") Integer limit);

    /**
     * 查询异常操作记录
     * 
     * 利用索引：idx_status_time
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 异常操作记录
     */
    @Select("SELECT * FROM oa_operation_log " +
            "WHERE delete_time = 0 " +
            "AND operation_state = 0 " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<OaOperationLogEntity> selectFailedOperations(@Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime,
                                                      @Param("limit") Integer limit);

    // ========== 数据清理方法 ==========

    /**
     * 软删除过期日志
     * 
     * 利用索引：idx_delete_create_time
     * 
     * @param expireTime 过期时间
     * @param deleteTime 删除时间戳
     * @return 清理的记录数
     */
    @Update("UPDATE oa_operation_log " +
            "SET delete_time = #{deleteTime}, update_time = NOW() " +
            "WHERE delete_time = 0 " +
            "AND create_time < #{expireTime}")
    Integer softDeleteExpiredLogs(@Param("expireTime") LocalDateTime expireTime,
                                 @Param("deleteTime") Integer deleteTime);

    /**
     * 物理删除已标记删除的日志
     * 
     * @param beforeTime 删除时间之前的记录
     * @return 删除的记录数
     */
    @Delete("DELETE FROM oa_operation_log " +
            "WHERE delete_time > 0 " +
            "AND delete_time < #{beforeTime}")
    Integer physicalDeleteLogs(@Param("beforeTime") Integer beforeTime);

    // ========== 批量操作方法 ==========

    /**
     * 批量插入操作日志
     * 
     * @param operationLogs 操作日志列表
     * @return 插入的记录数
     */
    @Insert("<script>" +
            "INSERT INTO oa_operation_log (" +
            "user_id, username, operation_type, operation_module, operation_function, " +
            "operation_description, business_type, business_id, operation_state, " +
            "request_method, request_url, request_params, response_result, client_ip, " +
            "user_agent, execution_time, risk_level, is_sensitive, operation_source, " +
            "trace_id, error_message, before_data, after_data, changed_fields, " +
            "data_version, audit_flag, extra_data, create_time, update_time, creator" +
            ") VALUES " +
            "<foreach collection='operationLogs' item='item' separator=','>" +
            "(" +
            "#{item.userId}, #{item.username}, #{item.operationType}, #{item.operationModule}, #{item.operationFunction}, " +
            "#{item.operationDescription}, #{item.businessType}, #{item.businessId}, #{item.operationState}, " +
            "#{item.requestMethod}, #{item.requestUrl}, #{item.requestParams}, #{item.responseResult}, #{item.clientIp}, " +
            "#{item.userAgent}, #{item.executionTime}, #{item.riskLevel}, #{item.isSensitive}, #{item.operationSource}, " +
            "#{item.traceId}, #{item.errorMessage}, #{item.beforeData}, #{item.afterData}, #{item.changedFields}, " +
            "#{item.dataVersion}, #{item.auditFlag}, #{item.extraData}, #{item.createTime}, #{item.updateTime}, #{item.creator}" +
            ")" +
            "</foreach>" +
            "</script>")
    Integer batchInsert(@Param("operationLogs") List<OaOperationLogEntity> operationLogs);
}

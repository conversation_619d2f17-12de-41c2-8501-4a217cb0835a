package com.miaowen.oa.system.interfaces.res.job;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/15 10:51
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaJobPageRsep {

    private Long id;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 岗位编码
     */
    private String jobCode;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    private Long updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 岗位人数
     */
    private Integer userCount;

}

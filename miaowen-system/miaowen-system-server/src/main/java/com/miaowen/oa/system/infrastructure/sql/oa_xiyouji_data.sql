-- 西游记天庭势力格局 - OA系统数据SQL
-- 基于西游记天庭各大势力的组织架构和人员关系
-- 可直接在MySQL中执行

-- 使用数据库
USE `oa_system`;

-- 清空现有数据（可选）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE oa_project_group_user_relation;
TRUNCATE TABLE oa_user_job_relation;
TRUNCATE TABLE oa_department_leader_relation;
TRUNCATE TABLE oa_department_user_relation;
TRUNCATE TABLE oa_menu_role_relation;
TRUNCATE TABLE oa_user_role_relation;
TRUNCATE TABLE oa_user;
TRUNCATE TABLE oa_project_group;
TRUNCATE TABLE oa_job;
TRUNCATE TABLE oa_dept;
TRUNCATE TABLE oa_menu;
TRUNCATE TABLE oa_role;
TRUNCATE TABLE oa_role_group;
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 第一步：角色分组数据（天庭权力层级）
-- ========================================
INSERT INTO `oa_role_group` (`id`, `group_name`, `description`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
(1, '三清道祖', '道教最高神祇，掌管天地万物', 1, NOW(), NOW(), 1, 1, 0),
(2, '玉皇大帝', '天庭最高统治者', 2, NOW(), NOW(), 1, 1, 0),
(3, '四御天帝', '协助玉帝管理天庭的四位大帝', 3, NOW(), NOW(), 1, 1, 0),
(4, '五方五老', '东西南北中五方的守护神', 4, NOW(), NOW(), 1, 1, 0),
(5, '二十八宿', '天庭星官系统', 5, NOW(), NOW(), 1, 1, 0),
(6, '各部正神', '天庭各部门主管', 6, NOW(), NOW(), 1, 1, 0),
(7, '护法天将', '天庭武将系统', 7, NOW(), NOW(), 1, 1, 0),
(8, '仙官仙吏', '天庭文官系统', 8, NOW(), NOW(), 1, 1, 0);

-- ========================================
-- 第二步：角色数据（具体职位）
-- ========================================
INSERT INTO `oa_role` (`id`, `role_group_id`, `role_name`, `role_code`, `is_super_admin`, `description`, `sort`, `state`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
-- 三清道祖
(1, 1, '太上老君', 'TAISHANG_LAOJUN', 1, '道教始祖，炼丹大师', 1, 1, NOW(), NOW(), 1, 1, 0),
(2, 1, '元始天尊', 'YUANSHI_TIANZUN', 1, '道教三清之首', 2, 1, NOW(), NOW(), 1, 1, 0),
(3, 1, '通天教主', 'TONGTIAN_JIAOZHU', 1, '截教教主', 3, 1, NOW(), NOW(), 1, 1, 0),

-- 玉皇大帝
(4, 2, '玉皇大帝', 'YUHUANG_DADI', 1, '天庭最高统治者', 1, 1, NOW(), NOW(), 1, 1, 0),
(5, 2, '王母娘娘', 'WANGMU_NIANGNIANG', 1, '天庭皇后', 2, 1, NOW(), NOW(), 1, 1, 0),

-- 四御天帝
(6, 3, '紫微大帝', 'ZIWEI_DADI', 0, '北极紫微大帝，协助玉帝', 1, 1, NOW(), NOW(), 1, 1, 0),
(7, 3, '勾陈大帝', 'GOUCHEN_DADI', 0, '勾陈上宫天皇大帝', 2, 1, NOW(), NOW(), 1, 1, 0),
(8, 3, '后土皇地祇', 'HOUTU_HUANGDIQI', 0, '承天效法后土皇地祇', 3, 1, NOW(), NOW(), 1, 1, 0),
(9, 3, '长生大帝', 'CHANGSHENG_DADI', 0, '南极长生大帝', 4, 1, NOW(), NOW(), 1, 1, 0),

-- 五方五老
(10, 4, '东方青帝', 'DONGFANG_QINGDI', 0, '东方青华帝君', 1, 1, NOW(), NOW(), 1, 1, 0),
(11, 4, '南方赤帝', 'NANFANG_CHIDI', 0, '南方炎帝', 2, 1, NOW(), NOW(), 1, 1, 0),
(12, 4, '西方白帝', 'XIFANG_BAIDI', 0, '西方白帝', 3, 1, NOW(), NOW(), 1, 1, 0),
(13, 4, '北方黑帝', 'BEIFANG_HEIDI', 0, '北方黑帝', 4, 1, NOW(), NOW(), 1, 1, 0),
(14, 4, '中央黄帝', 'ZHONGYANG_HUANGDI', 0, '中央黄帝', 5, 1, NOW(), NOW(), 1, 1, 0),

-- 二十八宿（部分代表）
(15, 5, '角木蛟', 'JIAOMUJIAO', 0, '东方青龙七宿之首', 1, 1, NOW(), NOW(), 1, 1, 0),
(16, 5, '斗木獬', 'DOUMUXIE', 0, '北方玄武七宿之首', 2, 1, NOW(), NOW(), 1, 1, 0),
(17, 5, '奎木狼', 'KUIMULANG', 0, '西方白虎七宿之首', 3, 1, NOW(), NOW(), 1, 1, 0),
(18, 5, '井木犴', 'JINGMUAN', 0, '南方朱雀七宿之首', 4, 1, NOW(), NOW(), 1, 1, 0),

-- 各部正神
(19, 6, '托塔李天王', 'TUOTA_LITIANWANG', 0, '天庭元帅，统领天兵天将', 1, 1, NOW(), NOW(), 1, 1, 0),
(20, 6, '哪吒三太子', 'NEZHA_SANTAIZI', 0, '李天王之子，护法神将', 2, 1, NOW(), NOW(), 1, 1, 0),
(21, 6, '巨灵神', 'JULINGSHEN', 0, '天庭力士', 3, 1, NOW(), NOW(), 1, 1, 0),
(22, 6, '四大天王', 'SIDA_TIANWANG', 0, '护法四大天王', 4, 1, NOW(), NOW(), 1, 1, 0),

-- 护法天将
(23, 7, '二郎神杨戬', 'ERLANGSHEN_YANGJIAN', 0, '显圣真君，天庭战神', 1, 1, NOW(), NOW(), 1, 1, 0),
(24, 7, '雷公', 'LEIGONG', 0, '雷部正神', 2, 1, NOW(), NOW(), 1, 1, 0),
(25, 7, '电母', 'DIANMU', 0, '电部正神', 3, 1, NOW(), NOW(), 1, 1, 0),
(26, 7, '风伯', 'FENGBO', 0, '风部正神', 4, 1, NOW(), NOW(), 1, 1, 0),
(27, 7, '雨师', 'YUSHI', 0, '雨部正神', 5, 1, NOW(), NOW(), 1, 1, 0),

-- 仙官仙吏
(28, 8, '太白金星', 'TAIBAI_JINXING', 0, '天庭使者', 1, 1, NOW(), NOW(), 1, 1, 0),
(29, 8, '文曲星君', 'WENQU_XINGJUN', 0, '主管文运', 2, 1, NOW(), NOW(), 1, 1, 0),
(30, 8, '武曲星君', 'WUQU_XINGJUN', 0, '主管武运', 3, 1, NOW(), NOW(), 1, 1, 0);

-- ========================================
-- 第三步：部门数据（天庭组织架构）
-- ========================================
INSERT INTO `oa_dept` (`id`, `department_name`, `parent_id`, `sort`, `description`, `department_code`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
-- 最高层
(1, '天庭', 0, 1, '三十三重天最高统治机构', 'TIANTING', NOW(), NOW(), 1, 1, 0),

-- 核心管理层
(2, '凌霄宝殿', 1, 1, '玉皇大帝办公场所', 'LINGXIAO_BAODIAN', NOW(), NOW(), 1, 1, 0),
(3, '兜率宫', 1, 2, '太上老君炼丹之所', 'DOULU_GONG', NOW(), NOW(), 1, 1, 0),
(4, '瑶池', 1, 3, '王母娘娘居所', 'YAOCHI', NOW(), NOW(), 1, 1, 0),

-- 军事部门
(5, '天兵司', 2, 1, '天庭军事部门', 'TIANBING_SI', NOW(), NOW(), 1, 1, 0),
(6, '雷部', 2, 2, '掌管雷电风雨', 'LEIBU', NOW(), NOW(), 1, 1, 0),
(7, '斗部', 2, 3, '二十八星宿管理部门', 'DOUBU', NOW(), NOW(), 1, 1, 0),

-- 行政部门
(8, '文昌司', 2, 4, '掌管文运教育', 'WENCHANG_SI', NOW(), NOW(), 1, 1, 0),
(9, '财神司', 2, 5, '掌管财富分配', 'CAISHEN_SI', NOW(), NOW(), 1, 1, 0),
(10, '月老司', 2, 6, '掌管姻缘婚配', 'YUELAO_SI', NOW(), NOW(), 1, 1, 0),

-- 下属分支
(11, '东方青龙部', 7, 1, '东方七宿管理', 'DONGFANG_QINGLONG', NOW(), NOW(), 1, 1, 0),
(12, '南方朱雀部', 7, 2, '南方七宿管理', 'NANFANG_ZHUQUE', NOW(), NOW(), 1, 1, 0),
(13, '西方白虎部', 7, 3, '西方七宿管理', 'XIFANG_BAIHU', NOW(), NOW(), 1, 1, 0),
(14, '北方玄武部', 7, 4, '北方七宿管理', 'BEIFANG_XUANWU', NOW(), NOW(), 1, 1, 0),

(15, '天兵营', 5, 1, '普通天兵管理', 'TIANBING_YING', NOW(), NOW(), 1, 1, 0),
(16, '天将营', 5, 2, '天将级别管理', 'TIANJIANG_YING', NOW(), NOW(), 1, 1, 0),
(17, '护法营', 5, 3, '护法神将管理', 'HUFA_YING', NOW(), NOW(), 1, 1, 0);

-- ========================================
-- 第四步：岗位数据（职务等级）
-- ========================================
INSERT INTO `oa_job` (`id`, `job_name`, `job_code`, `sort`, `description`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
(1, '道祖', 'DAOZU', 1, '道教最高神祇', NOW(), NOW(), 1, 1, 0),
(2, '天帝', 'TIANDI', 2, '天庭最高统治者', NOW(), NOW(), 1, 1, 0),
(3, '大帝', 'DADI', 3, '协助天帝的高级神祇', NOW(), NOW(), 1, 1, 0),
(4, '帝君', 'DIJUN', 4, '五方帝君级别', NOW(), NOW(), 1, 1, 0),
(5, '星君', 'XINGJUN', 5, '星宿级别神祇', NOW(), NOW(), 1, 1, 0),
(6, '天王', 'TIANWANG', 6, '天王级别', NOW(), NOW(), 1, 1, 0),
(7, '元帅', 'YUANSHUAI', 7, '军事统帅', NOW(), NOW(), 1, 1, 0),
(8, '天将', 'TIANJIANG', 8, '高级武将', NOW(), NOW(), 1, 1, 0),
(9, '正神', 'ZHENGSHEN', 9, '各部门主管', NOW(), NOW(), 1, 1, 0),
(10, '仙官', 'XIANGUAN', 10, '文职官员', NOW(), NOW(), 1, 1, 0),
(11, '护法', 'HUFA', 11, '护法神将', NOW(), NOW(), 1, 1, 0),
(12, '天兵', 'TIANBING', 12, '普通士兵', NOW(), NOW(), 1, 1, 0);

-- ========================================
-- 第五步：项目组数据（特殊任务组织）
-- ========================================
INSERT INTO `oa_project_group` (`id`, `group_name`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
(1, '取经护法组', NOW(), NOW(), 1, 1, 0),
(2, '蟠桃会筹备组', NOW(), NOW(), 1, 1, 0),
(3, '天庭巡查组', NOW(), NOW(), 1, 1, 0),
(4, '降妖除魔组', NOW(), NOW(), 1, 1, 0),
(5, '炼丹研发组', NOW(), NOW(), 1, 1, 0),
(6, '天气调控组', NOW(), NOW(), 1, 1, 0),
(7, '星象观测组', NOW(), NOW(), 1, 1, 0);

-- ========================================
-- 第六步：用户数据（天庭神仙）
-- ========================================
INSERT INTO `oa_user` (`id`, `user_code`, `username`, `real_name`, `gender`, `email`, `phone`, `password`, `state`, `entry_state`, `avatar_url`, `login_number`, `last_login_ip`, `last_login_time`, `is_department_manage`, `province`, `address`, `identity`, `level`, `data_limit_rank`, `join_time`, `leave_time`, `leave_notes`, `sort`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES

-- 三清道祖
(1, '0001', 'taishang_laojun', '太上老君', 1, '<EMAIL>', '13800000001', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/taishang.jpg', 999, '***********', UNIX_TIMESTAMP(NOW()), 1, 1, '兜率宫', '350000000000000001', 'S1', 2, UNIX_TIMESTAMP('2000-01-01'), 0, '', 1, NOW(), NOW(), 1, 1, 0),

(2, '0002', 'yuanshi_tianzun', '元始天尊', 1, '<EMAIL>', '13800000002', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/yuanshi.jpg', 888, '***********', UNIX_TIMESTAMP(NOW()), 1, 1, '玉虚宫', '350000000000000002', 'S1', 2, UNIX_TIMESTAMP('2000-01-01'), 0, '', 2, NOW(), NOW(), 1, 1, 0),

(3, '0003', 'tongtian_jiaozhu', '通天教主', 1, '<EMAIL>', '13800000003', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/tongtian.jpg', 777, '192.168.1.3', UNIX_TIMESTAMP(NOW()), 1, 1, '碧游宫', '350000000000000003', 'S1', 2, UNIX_TIMESTAMP('2000-01-01'), 0, '', 3, NOW(), NOW(), 1, 1, 0),

-- 玉皇大帝
(4, '0004', 'yuhuang_dadi', '玉皇大帝', 1, '<EMAIL>', '13800000004', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/yuhuang.jpg', 9999, '192.168.1.4', UNIX_TIMESTAMP(NOW()), 1, 1, '凌霄宝殿', '350000000000000004', 'S1', 2, UNIX_TIMESTAMP('2000-01-01'), 0, '', 4, NOW(), NOW(), 1, 1, 0),

(5, '0005', 'wangmu_niangniang', '王母娘娘', 2, '<EMAIL>', '13800000005', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/wangmu.jpg', 8888, '192.168.1.5', UNIX_TIMESTAMP(NOW()), 1, 1, '瑶池', '350000000000000005', 'S1', 2, UNIX_TIMESTAMP('2000-01-01'), 0, '', 5, NOW(), NOW(), 1, 1, 0),

-- 四御天帝
(6, '0006', 'ziwei_dadi', '紫微大帝', 1, '<EMAIL>', '13800000006', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/ziwei.jpg', 6666, '192.168.1.6', UNIX_TIMESTAMP(NOW()), 1, 1, '紫微宫', '350000000000000006', 'A1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 6, NOW(), NOW(), 1, 1, 0),

(7, '0007', 'gouchen_dadi', '勾陈大帝', 1, '<EMAIL>', '13800000007', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/gouchen.jpg', 5555, '192.168.1.7', UNIX_TIMESTAMP(NOW()), 1, 1, '勾陈宫', '350000000000000007', 'A1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 7, NOW(), NOW(), 1, 1, 0),

(8, '0008', 'houtu_huangdiqi', '后土皇地祇', 2, '<EMAIL>', '13800000008', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/houtu.jpg', 4444, '192.168.1.8', UNIX_TIMESTAMP(NOW()), 1, 1, '后土宫', '350000000000000008', 'A1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 8, NOW(), NOW(), 1, 1, 0),

(9, '0009', 'changsheng_dadi', '长生大帝', 1, '<EMAIL>', '13800000009', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/changsheng.jpg', 3333, '192.168.1.9', UNIX_TIMESTAMP(NOW()), 1, 1, '长生宫', '350000000000000009', 'A1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 9, NOW(), NOW(), 1, 1, 0),

-- 五方五老
(10, '0010', 'dongfang_qingdi', '东方青帝', 1, '<EMAIL>', '13800000010', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/qingdi.jpg', 2222, '***********0', UNIX_TIMESTAMP(NOW()), 1, 1, '青华宫', '350000000000000010', 'A2', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 10, NOW(), NOW(), 1, 1, 0),

(11, '0011', 'nanfang_chidi', '南方赤帝', 1, '<EMAIL>', '13800000011', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/chidi.jpg', 1111, '***********1', UNIX_TIMESTAMP(NOW()), 1, 1, '炎帝宫', '350000000000000011', 'A2', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 11, NOW(), NOW(), 1, 1, 0),

(12, '0012', 'xifang_baidi', '西方白帝', 1, '<EMAIL>', '13800000012', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/baidi.jpg', 1000, '***********2', UNIX_TIMESTAMP(NOW()), 1, 1, '白帝宫', '350000000000000012', 'A2', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 12, NOW(), NOW(), 1, 1, 0),

(13, '0013', 'beifang_heidi', '北方黑帝', 1, '<EMAIL>', '13800000013', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/heidi.jpg', 900, '***********3', UNIX_TIMESTAMP(NOW()), 1, 1, '黑帝宫', '350000000000000013', 'A2', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 13, NOW(), NOW(), 1, 1, 0),

(14, '0014', 'zhongyang_huangdi', '中央黄帝', 1, '<EMAIL>', '13800000014', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/huangdi.jpg', 800, '***********4', UNIX_TIMESTAMP(NOW()), 1, 1, '黄帝宫', '350000000000000014', 'A2', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 14, NOW(), NOW(), 1, 1, 0),

-- 二十八宿代表
(15, '0015', 'jiaomujiao', '角木蛟', 1, '<EMAIL>', '13800000015', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/jiaomujiao.jpg', 700, '***********5', UNIX_TIMESTAMP(NOW()), 0, 1, '东方青龙宫', '350000000000000015', 'B1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 15, NOW(), NOW(), 1, 1, 0),

(16, '0016', 'doumuxie', '斗木獬', 1, '<EMAIL>', '13800000016', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/doumuxie.jpg', 600, '***********6', UNIX_TIMESTAMP(NOW()), 0, 1, '北方玄武宫', '350000000000000016', 'B1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 16, NOW(), NOW(), 1, 1, 0),

(17, '0017', 'kuimulang', '奎木狼', 1, '<EMAIL>', '13800000017', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/kuimulang.jpg', 500, '***********7', UNIX_TIMESTAMP(NOW()), 0, 1, '西方白虎宫', '350000000000000017', 'B1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 17, NOW(), NOW(), 1, 1, 0),

(18, '0018', 'jingmuan', '井木犴', 1, '<EMAIL>', '13800000018', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/jingmuan.jpg', 400, '***********8', UNIX_TIMESTAMP(NOW()), 0, 1, '南方朱雀宫', '350000000000000018', 'B1', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 18, NOW(), NOW(), 1, 1, 0),

-- 各部正神
(19, '0019', 'tuota_litianwang', '托塔李天王', 1, '<EMAIL>', '13800000019', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/litianwang.jpg', 300, '***********9', UNIX_TIMESTAMP(NOW()), 1, 1, '天兵司', '350000000000000019', 'B2', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 19, NOW(), NOW(), 1, 1, 0),

(20, '0020', 'nezha_santaizi', '哪吒三太子', 1, '<EMAIL>', '13800000020', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/nezha.jpg', 250, '***********0', UNIX_TIMESTAMP(NOW()), 0, 1, '天兵司', '350000000000000020', 'B3', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 20, NOW(), NOW(), 1, 1, 0),

-- 护法天将
(21, '0021', 'erlangshen_yangjian', '二郎神杨戬', 1, '<EMAIL>', '13800000021', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/erlangshen.jpg', 200, '***********1', UNIX_TIMESTAMP(NOW()), 1, 1, '灌江口', '350000000000000021', 'B2', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 21, NOW(), NOW(), 1, 1, 0),

(22, '0022', 'leigong', '雷公', 1, '<EMAIL>', '13800000022', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/leigong.jpg', 150, '***********2', UNIX_TIMESTAMP(NOW()), 1, 1, '雷部', '350000000000000022', 'B3', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 22, NOW(), NOW(), 1, 1, 0),

(23, '0023', 'dianmu', '电母', 2, '<EMAIL>', '13800000023', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/dianmu.jpg', 140, '***********3', UNIX_TIMESTAMP(NOW()), 0, 1, '雷部', '350000000000000023', 'B3', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 23, NOW(), NOW(), 1, 1, 0),

(24, '0024', 'fengbo', '风伯', 1, '<EMAIL>', '13800000024', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/fengbo.jpg', 130, '***********4', UNIX_TIMESTAMP(NOW()), 0, 1, '雷部', '350000000000000024', 'B3', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 24, NOW(), NOW(), 1, 1, 0),

(25, '0025', 'yushi', '雨师', 1, '<EMAIL>', '13800000025', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/yushi.jpg', 120, '***********5', UNIX_TIMESTAMP(NOW()), 0, 1, '雷部', '350000000000000025', 'B3', 1, UNIX_TIMESTAMP('2000-01-01'), 0, '', 25, NOW(), NOW(), 1, 1, 0),

-- 仙官仙吏
(26, '0026', 'taibai_jinxing', '太白金星', 1, '<EMAIL>', '13800000026', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/taibai.jpg', 110, '***********6', UNIX_TIMESTAMP(NOW()), 0, 1, '凌霄宝殿', '350000000000000026', 'C1', 0, UNIX_TIMESTAMP('2000-01-01'), 0, '', 26, NOW(), NOW(), 1, 1, 0),

(27, '0027', 'wenqu_xingjun', '文曲星君', 1, '<EMAIL>', '13800000027', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/wenqu.jpg', 100, '***********7', UNIX_TIMESTAMP(NOW()), 1, 1, '文昌司', '350000000000000027', 'C1', 0, UNIX_TIMESTAMP('2000-01-01'), 0, '', 27, NOW(), NOW(), 1, 1, 0),

(28, '0028', 'wuqu_xingjun', '武曲星君', 1, '<EMAIL>', '13800000028', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/wuqu.jpg', 90, '***********8', UNIX_TIMESTAMP(NOW()), 0, 1, '天兵司', '350000000000000028', 'C1', 0, UNIX_TIMESTAMP('2000-01-01'), 0, '', 28, NOW(), NOW(), 1, 1, 0),

-- 普通天兵天将
(29, '0029', 'tianbing_001', '天兵甲', 1, '<EMAIL>', '13800000029', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/tianbing.jpg', 50, '***********9', UNIX_TIMESTAMP(NOW()), 0, 1, '天兵营', '350000000000000029', 'D1', 0, UNIX_TIMESTAMP('2000-01-01'), 0, '', 29, NOW(), NOW(), 1, 1, 0),

(30, '0030', 'tianbing_002', '天兵乙', 1, '<EMAIL>', '13800000030', '$2a$10$7JB720yubVSOfvVeWI5uFuWZ4xXD4PxZWlKiXkjKdYANHZDOhHgng', 1, 2, '/avatar/tianbing.jpg', 40, '192.168.1.30', UNIX_TIMESTAMP(NOW()), 0, 1, '天兵营', '350000000000000030', 'D1', 0, UNIX_TIMESTAMP('2000-01-01'), 0, '', 30, NOW(), NOW(), 1, 1, 0);

-- ========================================
-- 第七步：菜单数据（天庭管理系统）
-- ========================================
INSERT INTO `oa_menu` (`id`, `parent_id`, `menu_name`, `menu_code`, `menu_type`, `menu_url`, `route_url`, `authority_sign`, `state`, `icon`, `sort`, `settings`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES

-- 一级菜单（主要功能模块）
(1, 0, '天庭管理', 'TIANTING_MANAGE', 1, '/tianting', '/tianting', 'tianting:manage', 1, 'crown', 1, '{}', NOW(), NOW(), 1, 1, 0),
(2, 0, '神仙管理', 'SHENXIAN_MANAGE', 1, '/shenxian', '/shenxian', 'shenxian:manage', 1, 'user', 2, '{}', NOW(), NOW(), 1, 1, 0),
(3, 0, '部门管理', 'BUMEN_MANAGE', 1, '/bumen', '/bumen', 'bumen:manage', 1, 'apartment', 3, '{}', NOW(), NOW(), 1, 1, 0),
(4, 0, '权限管理', 'QUANXIAN_MANAGE', 1, '/quanxian', '/quanxian', 'quanxian:manage', 1, 'safety', 4, '{}', NOW(), NOW(), 1, 1, 0),
(5, 0, '任务管理', 'RENWU_MANAGE', 1, '/renwu', '/renwu', 'renwu:manage', 1, 'project', 5, '{}', NOW(), NOW(), 1, 1, 0),
(6, 0, '系统设置', 'XITONG_SHEZHI', 1, '/xitong', '/xitong', 'xitong:manage', 1, 'setting', 6, '{}', NOW(), NOW(), 1, 1, 0),

-- 天庭管理子菜单
(11, 1, '凌霄宝殿', 'LINGXIAO_BAODIAN_MENU', 2, '/tianting/lingxiao', '/tianting/lingxiao', 'tianting:lingxiao:view', 1, 'home', 1, '{}', NOW(), NOW(), 1, 1, 0),
(12, 1, '蟠桃会管理', 'PANTAOHUI_MANAGE', 2, '/tianting/pantaohui', '/tianting/pantaohui', 'tianting:pantaohui:manage', 1, 'gift', 2, '{}', NOW(), NOW(), 1, 1, 0),
(13, 1, '天庭公告', 'TIANTING_GONGGAO', 2, '/tianting/gonggao', '/tianting/gonggao', 'tianting:gonggao:view', 1, 'notification', 3, '{}', NOW(), NOW(), 1, 1, 0),
(14, 1, '仙界法规', 'XIANJIE_FAGUI', 2, '/tianting/fagui', '/tianting/fagui', 'tianting:fagui:view', 1, 'book', 4, '{}', NOW(), NOW(), 1, 1, 0),

-- 神仙管理子菜单
(21, 2, '神仙列表', 'SHENXIAN_LIST', 2, '/shenxian/list', '/shenxian/list', 'shenxian:list:view', 1, 'team', 1, '{}', NOW(), NOW(), 1, 1, 0),
(22, 2, '神仙档案', 'SHENXIAN_DANGAN', 2, '/shenxian/dangan', '/shenxian/dangan', 'shenxian:dangan:view', 1, 'profile', 2, '{}', NOW(), NOW(), 1, 1, 0),
(23, 2, '修为等级', 'XIUWEI_DENGJI', 2, '/shenxian/xiuwei', '/shenxian/xiuwei', 'shenxian:xiuwei:manage', 1, 'trophy', 3, '{}', NOW(), NOW(), 1, 1, 0),
(24, 2, '仙籍管理', 'XIANJI_MANAGE', 2, '/shenxian/xianji', '/shenxian/xianji', 'shenxian:xianji:manage', 1, 'idcard', 4, '{}', NOW(), NOW(), 1, 1, 0),

-- 部门管理子菜单
(31, 3, '部门架构', 'BUMEN_JIAGOU', 2, '/bumen/jiagou', '/bumen/jiagou', 'bumen:jiagou:view', 1, 'cluster', 1, '{}', NOW(), NOW(), 1, 1, 0),
(32, 3, '岗位管理', 'GANGWEI_MANAGE', 2, '/bumen/gangwei', '/bumen/gangwei', 'bumen:gangwei:manage', 1, 'solution', 2, '{}', NOW(), NOW(), 1, 1, 0),
(33, 3, '星宿管理', 'XINGXIU_MANAGE', 2, '/bumen/xingxiu', '/bumen/xingxiu', 'bumen:xingxiu:manage', 1, 'star', 3, '{}', NOW(), NOW(), 1, 1, 0),

-- 权限管理子菜单
(41, 4, '角色管理', 'JIAOSE_MANAGE', 2, '/quanxian/jiaose', '/quanxian/jiaose', 'quanxian:jiaose:manage', 1, 'usergroup-add', 1, '{}', NOW(), NOW(), 1, 1, 0),
(42, 4, '菜单管理', 'CAIDAN_MANAGE', 2, '/quanxian/caidan', '/quanxian/caidan', 'quanxian:caidan:manage', 1, 'menu', 2, '{}', NOW(), NOW(), 1, 1, 0),
(43, 4, '权限分配', 'QUANXIAN_FENPEI', 2, '/quanxian/fenpei', '/quanxian/fenpei', 'quanxian:fenpei:manage', 1, 'key', 3, '{}', NOW(), NOW(), 1, 1, 0),

-- 任务管理子菜单
(51, 5, '取经任务', 'QUJING_RENWU', 2, '/renwu/qujing', '/renwu/qujing', 'renwu:qujing:manage', 1, 'compass', 1, '{}', NOW(), NOW(), 1, 1, 0),
(52, 5, '降妖除魔', 'JIANGYAO_CHUNMO', 2, '/renwu/jiangyao', '/renwu/jiangyao', 'renwu:jiangyao:manage', 1, 'thunderbolt', 2, '{}', NOW(), NOW(), 1, 1, 0),
(53, 5, '天气调控', 'TIANQI_TIAOKONG', 2, '/renwu/tianqi', '/renwu/tianqi', 'renwu:tianqi:manage', 1, 'cloud', 3, '{}', NOW(), NOW(), 1, 1, 0),
(54, 5, '炼丹研发', 'LIANDAN_YANFA', 2, '/renwu/liandan', '/renwu/liandan', 'renwu:liandan:manage', 1, 'experiment', 4, '{}', NOW(), NOW(), 1, 1, 0),

-- 系统设置子菜单
(61, 6, '系统配置', 'XITONG_PEIZHI', 2, '/xitong/peizhi', '/xitong/peizhi', 'xitong:peizhi:manage', 1, 'control', 1, '{}', NOW(), NOW(), 1, 1, 0),
(62, 6, '日志管理', 'RIZHI_MANAGE', 2, '/xitong/rizhi', '/xitong/rizhi', 'xitong:rizhi:view', 1, 'file-text', 2, '{}', NOW(), NOW(), 1, 1, 0),
(63, 6, '数据备份', 'SHUJU_BEIFEN', 2, '/xitong/beifen', '/xitong/beifen', 'xitong:beifen:manage', 1, 'database', 3, '{}', NOW(), NOW(), 1, 1, 0);

-- ========================================
-- 第八步：用户角色关联（神仙职位分配）
-- ========================================
INSERT INTO `oa_user_role_relation` (`id`, `user_id`, `role_id`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
-- 三清道祖
(1, 1, 1, NOW(), NOW(), 1, 1, 0),  -- 太上老君
(2, 2, 2, NOW(), NOW(), 1, 1, 0),  -- 元始天尊
(3, 3, 3, NOW(), NOW(), 1, 1, 0),  -- 通天教主

-- 玉皇大帝
(4, 4, 4, NOW(), NOW(), 1, 1, 0),  -- 玉皇大帝
(5, 5, 5, NOW(), NOW(), 1, 1, 0),  -- 王母娘娘

-- 四御天帝
(6, 6, 6, NOW(), NOW(), 1, 1, 0),  -- 紫微大帝
(7, 7, 7, NOW(), NOW(), 1, 1, 0),  -- 勾陈大帝
(8, 8, 8, NOW(), NOW(), 1, 1, 0),  -- 后土皇地祇
(9, 9, 9, NOW(), NOW(), 1, 1, 0),  -- 长生大帝

-- 五方五老
(10, 10, 10, NOW(), NOW(), 1, 1, 0), -- 东方青帝
(11, 11, 11, NOW(), NOW(), 1, 1, 0), -- 南方赤帝
(12, 12, 12, NOW(), NOW(), 1, 1, 0), -- 西方白帝
(13, 13, 13, NOW(), NOW(), 1, 1, 0), -- 北方黑帝
(14, 14, 14, NOW(), NOW(), 1, 1, 0), -- 中央黄帝

-- 二十八宿
(15, 15, 15, NOW(), NOW(), 1, 1, 0), -- 角木蛟
(16, 16, 16, NOW(), NOW(), 1, 1, 0), -- 斗木獬
(17, 17, 17, NOW(), NOW(), 1, 1, 0), -- 奎木狼
(18, 18, 18, NOW(), NOW(), 1, 1, 0), -- 井木犴

-- 各部正神
(19, 19, 19, NOW(), NOW(), 1, 1, 0), -- 托塔李天王
(20, 20, 20, NOW(), NOW(), 1, 1, 0), -- 哪吒三太子

-- 护法天将
(21, 21, 23, NOW(), NOW(), 1, 1, 0), -- 二郎神杨戬
(22, 22, 24, NOW(), NOW(), 1, 1, 0), -- 雷公
(23, 23, 25, NOW(), NOW(), 1, 1, 0), -- 电母
(24, 24, 26, NOW(), NOW(), 1, 1, 0), -- 风伯
(25, 25, 27, NOW(), NOW(), 1, 1, 0), -- 雨师

-- 仙官仙吏
(26, 26, 28, NOW(), NOW(), 1, 1, 0), -- 太白金星
(27, 27, 29, NOW(), NOW(), 1, 1, 0), -- 文曲星君
(28, 28, 30, NOW(), NOW(), 1, 1, 0), -- 武曲星君

-- 普通天兵
(29, 29, 22, NOW(), NOW(), 1, 1, 0), -- 天兵甲 -> 四大天王角色
(30, 30, 22, NOW(), NOW(), 1, 1, 0); -- 天兵乙 -> 四大天王角色

-- ========================================
-- 第九步：用户部门关联（神仙部门分配）
-- ========================================
INSERT INTO `oa_department_user_relation` (`id`, `user_id`, `department_id`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
-- 最高层
(1, 1, 3, NOW(), NOW(), 1, 1, 0),   -- 太上老君 -> 兜率宫
(2, 2, 2, NOW(), NOW(), 1, 1, 0),   -- 元始天尊 -> 凌霄宝殿
(3, 3, 2, NOW(), NOW(), 1, 1, 0),   -- 通天教主 -> 凌霄宝殿
(4, 4, 2, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝 -> 凌霄宝殿
(5, 5, 4, NOW(), NOW(), 1, 1, 0),   -- 王母娘娘 -> 瑶池

-- 四御天帝
(6, 6, 2, NOW(), NOW(), 1, 1, 0),   -- 紫微大帝 -> 凌霄宝殿
(7, 7, 2, NOW(), NOW(), 1, 1, 0),   -- 勾陈大帝 -> 凌霄宝殿
(8, 8, 2, NOW(), NOW(), 1, 1, 0),   -- 后土皇地祇 -> 凌霄宝殿
(9, 9, 2, NOW(), NOW(), 1, 1, 0),   -- 长生大帝 -> 凌霄宝殿

-- 五方五老分配到斗部
(10, 10, 7, NOW(), NOW(), 1, 1, 0), -- 东方青帝 -> 斗部
(11, 11, 7, NOW(), NOW(), 1, 1, 0), -- 南方赤帝 -> 斗部
(12, 12, 7, NOW(), NOW(), 1, 1, 0), -- 西方白帝 -> 斗部
(13, 13, 7, NOW(), NOW(), 1, 1, 0), -- 北方黑帝 -> 斗部
(14, 14, 7, NOW(), NOW(), 1, 1, 0), -- 中央黄帝 -> 斗部

-- 二十八宿分配到各自星宿部
(15, 15, 11, NOW(), NOW(), 1, 1, 0), -- 角木蛟 -> 东方青龙部
(16, 16, 14, NOW(), NOW(), 1, 1, 0), -- 斗木獬 -> 北方玄武部
(17, 17, 13, NOW(), NOW(), 1, 1, 0), -- 奎木狼 -> 西方白虎部
(18, 18, 12, NOW(), NOW(), 1, 1, 0), -- 井木犴 -> 南方朱雀部

-- 军事部门
(19, 19, 5, NOW(), NOW(), 1, 1, 0),  -- 托塔李天王 -> 天兵司
(20, 20, 5, NOW(), NOW(), 1, 1, 0),  -- 哪吒三太子 -> 天兵司
(21, 21, 5, NOW(), NOW(), 1, 1, 0),  -- 二郎神杨戬 -> 天兵司

-- 雷部
(22, 22, 6, NOW(), NOW(), 1, 1, 0),  -- 雷公 -> 雷部
(23, 23, 6, NOW(), NOW(), 1, 1, 0),  -- 电母 -> 雷部
(24, 24, 6, NOW(), NOW(), 1, 1, 0),  -- 风伯 -> 雷部
(25, 25, 6, NOW(), NOW(), 1, 1, 0),  -- 雨师 -> 雷部

-- 文职部门
(26, 26, 2, NOW(), NOW(), 1, 1, 0),  -- 太白金星 -> 凌霄宝殿
(27, 27, 8, NOW(), NOW(), 1, 1, 0),  -- 文曲星君 -> 文昌司
(28, 28, 5, NOW(), NOW(), 1, 1, 0),  -- 武曲星君 -> 天兵司

-- 普通天兵
(29, 29, 15, NOW(), NOW(), 1, 1, 0), -- 天兵甲 -> 天兵营
(30, 30, 15, NOW(), NOW(), 1, 1, 0); -- 天兵乙 -> 天兵营

-- ========================================
-- 第十步：用户岗位关联（神仙职级分配）
-- ========================================
INSERT INTO `oa_user_job_relation` (`id`, `user_id`, `job_id`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
-- 三清道祖
(1, 1, 1, NOW(), NOW(), 1, 1, 0),   -- 太上老君 -> 道祖
(2, 2, 1, NOW(), NOW(), 1, 1, 0),   -- 元始天尊 -> 道祖
(3, 3, 1, NOW(), NOW(), 1, 1, 0),   -- 通天教主 -> 道祖

-- 玉皇大帝
(4, 4, 2, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝 -> 天帝
(5, 5, 2, NOW(), NOW(), 1, 1, 0),   -- 王母娘娘 -> 天帝

-- 四御天帝
(6, 6, 3, NOW(), NOW(), 1, 1, 0),   -- 紫微大帝 -> 大帝
(7, 7, 3, NOW(), NOW(), 1, 1, 0),   -- 勾陈大帝 -> 大帝
(8, 8, 3, NOW(), NOW(), 1, 1, 0),   -- 后土皇地祇 -> 大帝
(9, 9, 3, NOW(), NOW(), 1, 1, 0),   -- 长生大帝 -> 大帝

-- 五方五老
(10, 10, 4, NOW(), NOW(), 1, 1, 0), -- 东方青帝 -> 帝君
(11, 11, 4, NOW(), NOW(), 1, 1, 0), -- 南方赤帝 -> 帝君
(12, 12, 4, NOW(), NOW(), 1, 1, 0), -- 西方白帝 -> 帝君
(13, 13, 4, NOW(), NOW(), 1, 1, 0), -- 北方黑帝 -> 帝君
(14, 14, 4, NOW(), NOW(), 1, 1, 0), -- 中央黄帝 -> 帝君

-- 二十八宿
(15, 15, 5, NOW(), NOW(), 1, 1, 0), -- 角木蛟 -> 星君
(16, 16, 5, NOW(), NOW(), 1, 1, 0), -- 斗木獬 -> 星君
(17, 17, 5, NOW(), NOW(), 1, 1, 0), -- 奎木狼 -> 星君
(18, 18, 5, NOW(), NOW(), 1, 1, 0), -- 井木犴 -> 星君

-- 各部正神
(19, 19, 6, NOW(), NOW(), 1, 1, 0), -- 托塔李天王 -> 天王
(20, 20, 8, NOW(), NOW(), 1, 1, 0), -- 哪吒三太子 -> 天将

-- 护法天将
(21, 21, 8, NOW(), NOW(), 1, 1, 0), -- 二郎神杨戬 -> 天将
(22, 22, 9, NOW(), NOW(), 1, 1, 0), -- 雷公 -> 正神
(23, 23, 9, NOW(), NOW(), 1, 1, 0), -- 电母 -> 正神
(24, 24, 9, NOW(), NOW(), 1, 1, 0), -- 风伯 -> 正神
(25, 25, 9, NOW(), NOW(), 1, 1, 0), -- 雨师 -> 正神

-- 仙官仙吏
(26, 26, 10, NOW(), NOW(), 1, 1, 0), -- 太白金星 -> 仙官
(27, 27, 5, NOW(), NOW(), 1, 1, 0),  -- 文曲星君 -> 星君
(28, 28, 5, NOW(), NOW(), 1, 1, 0),  -- 武曲星君 -> 星君

-- 普通天兵
(29, 29, 12, NOW(), NOW(), 1, 1, 0), -- 天兵甲 -> 天兵
(30, 30, 12, NOW(), NOW(), 1, 1, 0); -- 天兵乙 -> 天兵

-- ========================================
-- 第十一步：项目组用户关联（特殊任务分配）
-- ========================================
INSERT INTO `oa_project_group_user_relation` (`id`, `project_group_id`, `user_id`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
-- 取经护法组
(1, 1, 4, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝
(2, 1, 21, NOW(), NOW(), 1, 1, 0),  -- 二郎神杨戬
(3, 1, 19, NOW(), NOW(), 1, 1, 0),  -- 托塔李天王
(4, 1, 20, NOW(), NOW(), 1, 1, 0),  -- 哪吒三太子

-- 蟠桃会筹备组
(5, 2, 5, NOW(), NOW(), 1, 1, 0),   -- 王母娘娘
(6, 2, 26, NOW(), NOW(), 1, 1, 0),  -- 太白金星
(7, 2, 27, NOW(), NOW(), 1, 1, 0),  -- 文曲星君

-- 天庭巡查组
(8, 3, 6, NOW(), NOW(), 1, 1, 0),   -- 紫微大帝
(9, 3, 21, NOW(), NOW(), 1, 1, 0),  -- 二郎神杨戬
(10, 3, 28, NOW(), NOW(), 1, 1, 0), -- 武曲星君

-- 降妖除魔组
(11, 4, 21, NOW(), NOW(), 1, 1, 0), -- 二郎神杨戬
(12, 4, 19, NOW(), NOW(), 1, 1, 0), -- 托塔李天王
(13, 4, 20, NOW(), NOW(), 1, 1, 0), -- 哪吒三太子
(14, 4, 29, NOW(), NOW(), 1, 1, 0), -- 天兵甲
(15, 4, 30, NOW(), NOW(), 1, 1, 0), -- 天兵乙

-- 炼丹研发组
(16, 5, 1, NOW(), NOW(), 1, 1, 0),  -- 太上老君
(17, 5, 2, NOW(), NOW(), 1, 1, 0),  -- 元始天尊

-- 天气调控组
(18, 6, 22, NOW(), NOW(), 1, 1, 0), -- 雷公
(19, 6, 23, NOW(), NOW(), 1, 1, 0), -- 电母
(20, 6, 24, NOW(), NOW(), 1, 1, 0), -- 风伯
(21, 6, 25, NOW(), NOW(), 1, 1, 0), -- 雨师

-- 星象观测组
(22, 7, 15, NOW(), NOW(), 1, 1, 0), -- 角木蛟
(23, 7, 16, NOW(), NOW(), 1, 1, 0), -- 斗木獬
(24, 7, 17, NOW(), NOW(), 1, 1, 0), -- 奎木狼
(25, 7, 18, NOW(), NOW(), 1, 1, 0); -- 井木犴

-- ========================================
-- 第十二步：部门领导关联（部门负责人）
-- ========================================
INSERT INTO `oa_department_leader_relation` (`id`, `dept_id`, `user_id`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
(1, 2, 4, NOW(), NOW(), 1, 1, 0),   -- 凌霄宝殿 -> 玉皇大帝
(2, 3, 1, NOW(), NOW(), 1, 1, 0),   -- 兜率宫 -> 太上老君
(3, 4, 5, NOW(), NOW(), 1, 1, 0),   -- 瑶池 -> 王母娘娘
(4, 5, 19, NOW(), NOW(), 1, 1, 0),  -- 天兵司 -> 托塔李天王
(5, 6, 22, NOW(), NOW(), 1, 1, 0),  -- 雷部 -> 雷公
(6, 7, 6, NOW(), NOW(), 1, 1, 0),   -- 斗部 -> 紫微大帝
(7, 8, 27, NOW(), NOW(), 1, 1, 0),  -- 文昌司 -> 文曲星君
(8, 11, 15, NOW(), NOW(), 1, 1, 0), -- 东方青龙部 -> 角木蛟
(9, 12, 18, NOW(), NOW(), 1, 1, 0), -- 南方朱雀部 -> 井木犴
(10, 13, 17, NOW(), NOW(), 1, 1, 0), -- 西方白虎部 -> 奎木狼
(11, 14, 16, NOW(), NOW(), 1, 1, 0), -- 北方玄武部 -> 斗木獬
(12, 15, 29, NOW(), NOW(), 1, 1, 0), -- 天兵营 -> 天兵甲
(13, 16, 20, NOW(), NOW(), 1, 1, 0), -- 天将营 -> 哪吒三太子
(14, 17, 21, NOW(), NOW(), 1, 1, 0); -- 护法营 -> 二郎神杨戬

-- ========================================
-- 第十三步：用户领导关联（上下级关系）
-- ========================================
INSERT INTO `oa_user_leader_relation` (`id`, `leader_id`, `user_id`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES
-- 玉皇大帝是所有人的最高领导
(1, 4, 5, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝 -> 王母娘娘
(2, 4, 6, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝 -> 紫微大帝
(3, 4, 7, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝 -> 勾陈大帝
(4, 4, 8, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝 -> 后土皇地祇
(5, 4, 9, NOW(), NOW(), 1, 1, 0),   -- 玉皇大帝 -> 长生大帝

-- 部门内部领导关系
(6, 19, 20, NOW(), NOW(), 1, 1, 0), -- 托塔李天王 -> 哪吒三太子
(7, 19, 21, NOW(), NOW(), 1, 1, 0), -- 托塔李天王 -> 二郎神杨戬
(8, 19, 28, NOW(), NOW(), 1, 1, 0), -- 托塔李天王 -> 武曲星君
(9, 19, 29, NOW(), NOW(), 1, 1, 0), -- 托塔李天王 -> 天兵甲
(10, 19, 30, NOW(), NOW(), 1, 1, 0), -- 托塔李天王 -> 天兵乙

-- 雷部领导关系
(11, 22, 23, NOW(), NOW(), 1, 1, 0), -- 雷公 -> 电母
(12, 22, 24, NOW(), NOW(), 1, 1, 0), -- 雷公 -> 风伯
(13, 22, 25, NOW(), NOW(), 1, 1, 0), -- 雷公 -> 雨师

-- 斗部领导关系
(14, 6, 10, NOW(), NOW(), 1, 1, 0),  -- 紫微大帝 -> 东方青帝
(15, 6, 11, NOW(), NOW(), 1, 1, 0),  -- 紫微大帝 -> 南方赤帝
(16, 6, 12, NOW(), NOW(), 1, 1, 0),  -- 紫微大帝 -> 西方白帝
(17, 6, 13, NOW(), NOW(), 1, 1, 0),  -- 紫微大帝 -> 北方黑帝
(18, 6, 14, NOW(), NOW(), 1, 1, 0),  -- 紫微大帝 -> 中央黄帝

-- 星宿部门领导关系
(19, 15, 16, NOW(), NOW(), 1, 1, 0), -- 角木蛟 -> 斗木獬（东方青龙部内部）
(20, 17, 18, NOW(), NOW(), 1, 1, 0); -- 奎木狼 -> 井木犴（西方白虎部内部）

-- ========================================
-- 第十四步：菜单角色关联（权限分配）
-- ========================================
INSERT INTO `oa_menu_role_relation` (`id`, `menu_id`, `role_id`, `create_time`, `update_time`, `creator`, `updater`, `delete_time`) VALUES

-- 三清道祖拥有所有权限
(1, 1, 1, NOW(), NOW(), 1, 1, 0), (2, 2, 1, NOW(), NOW(), 1, 1, 0), (3, 3, 1, NOW(), NOW(), 1, 1, 0), (4, 4, 1, NOW(), NOW(), 1, 1, 0), (5, 5, 1, NOW(), NOW(), 1, 1, 0), (6, 6, 1, NOW(), NOW(), 1, 1, 0),
(7, 1, 2, NOW(), NOW(), 1, 1, 0), (8, 2, 2, NOW(), NOW(), 1, 1, 0), (9, 3, 2, NOW(), NOW(), 1, 1, 0), (10, 4, 2, NOW(), NOW(), 1, 1, 0), (11, 5, 2, NOW(), NOW(), 1, 1, 0), (12, 6, 2, NOW(), NOW(), 1, 1, 0),
(13, 1, 3, NOW(), NOW(), 1, 1, 0), (14, 2, 3, NOW(), NOW(), 1, 1, 0), (15, 3, 3, NOW(), NOW(), 1, 1, 0), (16, 4, 3, NOW(), NOW(), 1, 1, 0), (17, 5, 3, NOW(), NOW(), 1, 1, 0), (18, 6, 3, NOW(), NOW(), 1, 1, 0),

-- 玉皇大帝拥有所有权限
(19, 1, 4, NOW(), NOW(), 1, 1, 0), (20, 2, 4, NOW(), NOW(), 1, 1, 0), (21, 3, 4, NOW(), NOW(), 1, 1, 0), (22, 4, 4, NOW(), NOW(), 1, 1, 0), (23, 5, 4, NOW(), NOW(), 1, 1, 0), (24, 6, 4, NOW(), NOW(), 1, 1, 0),
(25, 1, 5, NOW(), NOW(), 1, 1, 0), (26, 2, 5, NOW(), NOW(), 1, 1, 0), (27, 3, 5, NOW(), NOW(), 1, 1, 0), (28, 4, 5, NOW(), NOW(), 1, 1, 0), (29, 5, 5, NOW(), NOW(), 1, 1, 0), (30, 6, 5, NOW(), NOW(), 1, 1, 0),

-- 四御天帝拥有管理权限
(31, 1, 6, NOW(), NOW(), 1, 1, 0), (32, 2, 6, NOW(), NOW(), 1, 1, 0), (33, 3, 6, NOW(), NOW(), 1, 1, 0), (34, 4, 6, NOW(), NOW(), 1, 1, 0), (35, 5, 6, NOW(), NOW(), 1, 1, 0),
(36, 1, 7, NOW(), NOW(), 1, 1, 0), (37, 2, 7, NOW(), NOW(), 1, 1, 0), (38, 3, 7, NOW(), NOW(), 1, 1, 0), (39, 4, 7, NOW(), NOW(), 1, 1, 0), (40, 5, 7, NOW(), NOW(), 1, 1, 0),
(41, 1, 8, NOW(), NOW(), 1, 1, 0), (42, 2, 8, NOW(), NOW(), 1, 1, 0), (43, 3, 8, NOW(), NOW(), 1, 1, 0), (44, 4, 8, NOW(), NOW(), 1, 1, 0), (45, 5, 8, NOW(), NOW(), 1, 1, 0),
(46, 1, 9, NOW(), NOW(), 1, 1, 0), (47, 2, 9, NOW(), NOW(), 1, 1, 0), (48, 3, 9, NOW(), NOW(), 1, 1, 0), (49, 4, 9, NOW(), NOW(), 1, 1, 0), (50, 5, 9, NOW(), NOW(), 1, 1, 0),

-- 五方五老拥有部分管理权限
(51, 1, 10, NOW(), NOW(), 1, 1, 0), (52, 2, 10, NOW(), NOW(), 1, 1, 0), (53, 3, 10, NOW(), NOW(), 1, 1, 0), (54, 5, 10, NOW(), NOW(), 1, 1, 0),
(55, 1, 11, NOW(), NOW(), 1, 1, 0), (56, 2, 11, NOW(), NOW(), 1, 1, 0), (57, 3, 11, NOW(), NOW(), 1, 1, 0), (58, 5, 11, NOW(), NOW(), 1, 1, 0),
(59, 1, 12, NOW(), NOW(), 1, 1, 0), (60, 2, 12, NOW(), NOW(), 1, 1, 0), (61, 3, 12, NOW(), NOW(), 1, 1, 0), (62, 5, 12, NOW(), NOW(), 1, 1, 0),
(63, 1, 13, NOW(), NOW(), 1, 1, 0), (64, 2, 13, NOW(), NOW(), 1, 1, 0), (65, 3, 13, NOW(), NOW(), 1, 1, 0), (66, 5, 13, NOW(), NOW(), 1, 1, 0),
(67, 1, 14, NOW(), NOW(), 1, 1, 0), (68, 2, 14, NOW(), NOW(), 1, 1, 0), (69, 3, 14, NOW(), NOW(), 1, 1, 0), (70, 5, 14, NOW(), NOW(), 1, 1, 0),

-- 二十八宿拥有基本权限
(71, 1, 15, NOW(), NOW(), 1, 1, 0), (72, 2, 15, NOW(), NOW(), 1, 1, 0), (73, 5, 15, NOW(), NOW(), 1, 1, 0),
(74, 1, 16, NOW(), NOW(), 1, 1, 0), (75, 2, 16, NOW(), NOW(), 1, 1, 0), (76, 5, 16, NOW(), NOW(), 1, 1, 0),
(77, 1, 17, NOW(), NOW(), 1, 1, 0), (78, 2, 17, NOW(), NOW(), 1, 1, 0), (79, 5, 17, NOW(), NOW(), 1, 1, 0),
(80, 1, 18, NOW(), NOW(), 1, 1, 0), (81, 2, 18, NOW(), NOW(), 1, 1, 0), (82, 5, 18, NOW(), NOW(), 1, 1, 0),

-- 各部正神拥有相关权限
(83, 1, 19, NOW(), NOW(), 1, 1, 0), (84, 2, 19, NOW(), NOW(), 1, 1, 0), (85, 3, 19, NOW(), NOW(), 1, 1, 0), (86, 5, 19, NOW(), NOW(), 1, 1, 0), -- 托塔李天王
(87, 1, 20, NOW(), NOW(), 1, 1, 0), (88, 2, 20, NOW(), NOW(), 1, 1, 0), (89, 5, 20, NOW(), NOW(), 1, 1, 0), -- 哪吒三太子

-- 护法天将拥有执行权限
(90, 1, 23, NOW(), NOW(), 1, 1, 0), (91, 2, 23, NOW(), NOW(), 1, 1, 0), (92, 5, 23, NOW(), NOW(), 1, 1, 0), -- 二郎神杨戬
(93, 1, 24, NOW(), NOW(), 1, 1, 0), (94, 5, 24, NOW(), NOW(), 1, 1, 0), -- 雷公
(95, 1, 25, NOW(), NOW(), 1, 1, 0), (96, 5, 25, NOW(), NOW(), 1, 1, 0), -- 电母
(97, 1, 26, NOW(), NOW(), 1, 1, 0), (98, 5, 26, NOW(), NOW(), 1, 1, 0), -- 风伯
(99, 1, 27, NOW(), NOW(), 1, 1, 0), (100, 5, 27, NOW(), NOW(), 1, 1, 0), -- 雨师

-- 仙官仙吏拥有基本权限
(101, 1, 28, NOW(), NOW(), 1, 1, 0), (102, 2, 28, NOW(), NOW(), 1, 1, 0), -- 太白金星
(103, 1, 29, NOW(), NOW(), 1, 1, 0), (104, 2, 29, NOW(), NOW(), 1, 1, 0), -- 文曲星君
(105, 1, 30, NOW(), NOW(), 1, 1, 0), (106, 2, 30, NOW(), NOW(), 1, 1, 0), -- 武曲星君

-- 四大天王（普通天兵）拥有基本查看权限
(107, 1, 22, NOW(), NOW(), 1, 1, 0), (108, 2, 22, NOW(), NOW(), 1, 1, 0); -- 四大天王角色

-- ========================================
-- 数据插入完成提示
-- ========================================
SELECT '西游记天庭势力格局数据插入完成！' AS message;
SELECT '共插入用户30个，角色30个，部门17个，岗位12个，项目组7个' AS summary;
SELECT '数据包含完整的组织架构和权限关系，可用于OA系统测试' AS note;

-- 提交事务
COMMIT;

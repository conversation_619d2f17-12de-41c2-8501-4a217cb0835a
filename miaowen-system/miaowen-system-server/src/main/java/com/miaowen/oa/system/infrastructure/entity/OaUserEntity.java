package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 用户实体类
 *
 * 设计理念：
 * 1. 用户实体的核心地位：
 *    - 作为整个OA系统的核心实体，承载用户的基本信息和状态
 *    - 与权限、角色、部门等模块紧密关联，是系统的数据中心
 *    - 需要考虑用户生命周期的完整管理，从入职到离职的全过程
 *    - 参考：《企业级应用架构模式》中的领域模型设计
 *
 * 2. 数据安全和隐私保护：
 *    - 敏感信息（如密码、身份证号）需要加密存储
 *    - 个人隐私信息需要严格的访问控制
 *    - 符合《个人信息保护法》和《数据安全法》的要求
 *    - 支持数据脱敏和分级访问控制
 *
 * 3. 扩展性和兼容性：
 *    - 支持多种登录方式：密码、企业微信、SSO等
 *    - 兼容不同的组织架构和管理模式
 *    - 预留扩展字段，支持业务需求的变化
 *    - 考虑与第三方系统的集成需求
 *
 * 4. 性能和存储优化：
 *    - 合理的字段类型选择，平衡存储空间和查询性能
 *    - 重要字段的索引设计，提高查询效率
 *    - 冗余字段的权衡，减少关联查询的开销
 *    - 考虑分库分表的扩展需求
 *
 * 5. 业务完整性：
 *    - 用户状态的完整管理：正常、禁用、离职等
 *    - 入职流程的状态跟踪：待入职、已入职、试用期等
 *    - 权限和数据范围的精确控制
 *    - 审计日志的完整记录
 *
 * <AUTHOR>
 * @date 2025/7/10 19:30
 * @company 武汉市妙闻网络科技有限公司
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_user")
public class OaUserEntity extends BaseDO {

    /**
     * 工号（登录账号），固定四位数字,例如0001, 0002
     */
    private String userCode;

    /**
     * 登录用户名
     */
    private String username;

    /**
     * 性别：0-未知 1-男 2-女
     */
    private Integer gender;

    /**
     * 登录邮箱
     */
    private String email;

    /**
     * 手机号（登录用）
     */
    private String phone;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 账户状态：0-无效 1-正常 2-禁用
     */
    private Integer state;

    /**
     * 入职状态：0-无效 1-待入职 2-已入职
     */
    private Integer entryState;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 登录次数
     */
    private Integer loginNumber;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录时间（时间戳）
     */
    private Integer lastLoginTime;

    /**
     * 真实姓名（法定姓名）
     */
    private String realName;

    /**
     * 是否部门管理人员：0-否 1-是
     */
    private Integer isDepartmentManage;

    /**
     * 省份代码
     */
    private Integer province;

    /**
     * 现居住地址
     */
    private String address;

    /**
     * 身份证号
     */
    private String identity;

    /**
     * 职级
     */
    private String level;

    /**
     * 数据权限范围：0-自己 1-部门 2-全公司
     */
    private Integer dataLimitRank;

    /**
     * 加入企业的时间（时间戳）
     */
    private Integer joinTime;

    /**
     * 离开企业的时间（时间戳）
     */
    private Long leaveTime;

    /**
     * 离职备注
     */
    private String leaveNotes;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 企业微信userId
     */
    private String qyWechatUserId;

    /**
     * 是否能通过密码模式登陆：0-否 1-是
     */
    private Integer enablePasswordLogin;

    /**
     * 转正日期（当前）
     */
    private LocalDate regularDate;

    /**
     * 企业邮箱，格式：<EMAIL>
     */
    private String companyEmail;

    /**
     * 员工类型：1-正式员工（全职） 2-实习生 3-外包员工 4-其他
     */
    private Integer employeeType;

    /**
     * 员工状态：1-试用期 2-准正式（已转正） 3-待离职 4-已离职
     */
    private Integer employeeState;

    /**
     * 入职日期（当前）
     */
    private LocalDate entryDate;

    /**
     * 试用期开始日期（当前）
     */
    private LocalDate probationStartDate;

    /**
     * 试用期结束日期（当前）
     */
    private LocalDate probationEndDate;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众
     */
    private Integer politicalState;

    /**
     * 婚姻状况（当前）：1-未婚 2-已婚 3-离异 4-丧偶
     */
    private Integer maritalState;

    /**
     * 最高学历（当前）：
     * 1-小学 2-初中 3-高中/中专 4-大专 5-本科 6-硕士 7-博士
     */
    private Integer education;

    /**
     * 毕业院校（当前）
     */
    private String graduateSchool;

    /**
     * 专业（当前）
     */
    private String major;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankCardNumber;
}
package com.miaowen.oa.system.interfaces.controller;


import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.domain.service.OaDepartmentService;
import com.miaowen.oa.system.interfaces.req.dept.*;
import com.miaowen.oa.system.interfaces.res.dept.OaDepartmentDetailResp;
import com.miaowen.oa.system.interfaces.res.dept.OaDepartmentResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:24
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Tag(name = "部门管理")
@Slf4j
@RequestMapping("/dept")
@RestController
@AllArgsConstructor
public class OaDepartmentController {

    private OaDepartmentService oaDepartmentService;

    @PostMapping
    @Operation(summary = "新增部门")
    public CommonResult<Void> addDepartment(@RequestBody DeptSaveRequest deptSaveRequest) {

        oaDepartmentService.addDepartment(deptSaveRequest);

        return CommonResult.success(null);
    }


    @PutMapping
    @Operation(summary = "编辑部门")
    public CommonResult<Void> edit(@RequestBody DeptUpdateRequest deptUpdateRequest) {
        oaDepartmentService.updateDepartment(deptUpdateRequest);
        return CommonResult.success(null);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查看详情")
    public CommonResult<OaDepartmentDetailResp> detail(@PathVariable("id") Long id) {
        OaDepartmentDetailResp oaDepartmentDetailResp = oaDepartmentService.getDetail(id);
        return CommonResult.success(oaDepartmentDetailResp);
    }


    @DeleteMapping("/{id}")
    @Operation(summary = "删除部门")
    public CommonResult<Void> delete(@PathVariable("id") Long id) {
        oaDepartmentService.removeDeptWithChildren(id);
        return CommonResult.success(null);
    }


    @GetMapping("/tree")
    @Operation(summary = "树形结构接口")
    public CommonResult<List<OaDepartmentResp>> tree() {
        return CommonResult.success(oaDepartmentService.getDeptTree());
    }
}

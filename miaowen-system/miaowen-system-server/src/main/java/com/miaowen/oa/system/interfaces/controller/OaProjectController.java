package com.miaowen.oa.system.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.domain.service.OaProjectService;
import com.miaowen.oa.system.interfaces.req.project.ProjectPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.project.ProjectSaveRequest;
import com.miaowen.oa.system.interfaces.req.project.ProjectUpdateRequest;
import com.miaowen.oa.system.interfaces.res.project.OaProjectDetailResp;
import com.miaowen.oa.system.interfaces.res.project.OaProjectPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2025/7/18 14:46
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */

@Tag(name = "项目管理")
@RestController
@RequestMapping("/project")
@AllArgsConstructor
public class OaProjectController {

    private final OaProjectService oaProjectService;

    @Operation(summary = "新增项目")
    @PostMapping
    public CommonResult<Void> addProject(@RequestBody @Validated ProjectSaveRequest projectSaveRequest) {
        oaProjectService.addProject(projectSaveRequest);
        return CommonResult.success(null);
    }

    @Operation(summary = "编辑项目")
    @PutMapping
    public CommonResult<Void> updateProject(@RequestBody ProjectUpdateRequest projectUpdateRequest) {
        oaProjectService.updateProject(projectUpdateRequest);

        return CommonResult.success(null);
    }

    @Operation(summary = "分页查询项目列表")
    @GetMapping("/page")
    public CommonResult<PageResult<OaProjectPageResp>> getProjectPageList(ProjectPageQueryRequest projectPageQueryRequest) {
        PageResult<OaProjectPageResp> pageResult = oaProjectService.getProjectPageList(projectPageQueryRequest);


        return CommonResult.success(pageResult);
    }

    @Operation(summary = "按ID查询项目")
    @GetMapping("/{id}")
    public CommonResult<OaProjectDetailResp> getProjectById(@PathVariable("id") Long id) {
        OaProjectDetailResp oaProjectDetailResp = oaProjectService.getProjectById(id);
        return CommonResult.success(oaProjectDetailResp);
    }

    @Operation(summary = "删除项目")
    @DeleteMapping("/{id}")
    public CommonResult<Void> deleteJob(@PathVariable("id") Long id) {
        oaProjectService.deleteJob(id);
        return CommonResult.success(null);
    }


}

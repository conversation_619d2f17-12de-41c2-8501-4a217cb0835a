package com.miaowen.oa.system.infrastructure.mapper;

import com.miaowen.oa.framework.mybatis.core.mapper.CustomBaseMapper;
import com.miaowen.oa.system.infrastructure.entity.OaUserRoleRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Mapper
public interface OaUserRoleRelationMapper extends CustomBaseMapper<OaUserRoleRelationEntity> {

    @Select("<script>" +
            "SELECT role_id AS roleId, user_id AS userId " +
            "FROM oa_user_role_relation " +
            "WHERE  delete_time = 0 and user_id IN " +
            "<foreach item='id' collection='userIds' open='(' separator=',' close=')'>" +
            "   #{id}" +
            "</foreach>" +
            "</script>")
    List<OaUserRoleRelationEntity> findByUserIds(@Param("userIds") List<Long> userIds);
}

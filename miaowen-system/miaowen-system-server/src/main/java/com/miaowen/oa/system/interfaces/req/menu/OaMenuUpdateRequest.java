package com.miaowen.oa.system.interfaces.req.menu;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data

public class OaMenuUpdateRequest {


    @Schema(description = "菜单ID (0表示根目录)", example = "0")
    private Long id;

    @Schema(description = "上级菜单ID (0表示根目录)", example = "0")
    private Long parentId = 0L;

    @NotBlank(message = "菜单名称不能为空")
    @Schema(description = "菜单名称", example = "系统管理")
    private String menuName;

    @NotNull(message = "菜单类型不能为空")
    @Schema(description = "菜单类型: 1-目录, 2-菜单, 3-权限点", example = "1")
    private Integer menuType;

    @Schema(description = "菜单路径", example = "/system")
    private String menuUrl;

    @Schema(description = "菜单图标", example = "el-icon-setting")
    private String icon;

    @NotNull(message = "排序不能为空")
    @Schema(description = "排序", example = "1")
    private Integer sort;

    @NotNull(message = "状态：0-启动，1-禁用")
    @Schema(description = "状态", example = "1")
    private Integer state;

    @Schema(description = "其他设置", example = "1")
    private List<String> settings;


}

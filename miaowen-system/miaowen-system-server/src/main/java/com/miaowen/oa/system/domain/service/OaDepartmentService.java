package com.miaowen.oa.system.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.oa.system.infrastructure.entity.OaDepartmentEntity;
import com.miaowen.oa.system.interfaces.req.dept.DeptSaveChildRequest;
import com.miaowen.oa.system.interfaces.req.dept.DeptSaveRequest;
import com.miaowen.oa.system.interfaces.req.dept.DeptUpdateRequest;
import com.miaowen.oa.system.interfaces.res.dept.OaDepartmentDetailResp;
import com.miaowen.oa.system.interfaces.res.dept.OaDepartmentResp;

import java.util.List;

public interface OaDepartmentService extends IService<OaDepartmentEntity> {
    List<OaDepartmentResp> getDeptTree();
    void removeDeptWithChildren(Long id);

    void addDepartment(DeptSaveRequest deptSaveRequest);

    void updateDepartment(DeptUpdateRequest deptUpdateRequest);

    OaDepartmentDetailResp getDetail(Long id);
}
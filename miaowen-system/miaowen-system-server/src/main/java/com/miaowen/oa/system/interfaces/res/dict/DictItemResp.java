package com.miaowen.oa.system.interfaces.res.dict;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/15 18:27
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class DictItemResp {
    private Long id;

    /**
     * 字典表主键id
     */
    private Long dictId;
    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 字典项名称
     */
    private String label;

    /**
     * 字典项值
     */
    private String itemValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 0-正常 1-禁用
     */
    private Integer state;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updater;

    /**
     * 更新人名称
     */
    private String updaterName;
}

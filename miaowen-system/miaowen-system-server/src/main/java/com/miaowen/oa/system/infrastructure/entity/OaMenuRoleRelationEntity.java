package com.miaowen.oa.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/10 19:30
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value ="oa_menu_role_relation")
public class OaMenuRoleRelationEntity extends BaseDO {

    /**
     * 菜单id
     */
    private Long menuId;

    private Long roleId;


}

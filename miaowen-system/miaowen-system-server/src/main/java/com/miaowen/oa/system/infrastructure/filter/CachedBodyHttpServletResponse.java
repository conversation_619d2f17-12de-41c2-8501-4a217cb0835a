package com.miaowen.oa.system.infrastructure.filter;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * 可缓存响应体的HttpServletResponse包装类
 * 
 * 功能说明：
 * 1. 缓存响应体内容，支持读取响应数据
 * 2. 用于在过滤器中记录响应结果
 * 3. 不影响正常的响应输出
 * 4. 遵循空间局部性原理，优化内存访问
 * 
 * 性能优化：
 * - 双写机制：同时写入原始流和缓存流
 * - 延迟转换：只在需要时才转换为字符串
 * - 内存管理：合理控制缓存大小
 * - 流复用：减少对象创建开销
 * 
 * 设计原则：
 * - 透明性：对业务代码完全透明
 * - 兼容性：完全兼容原有的Servlet API
 * - 性能：最小化性能影响
 * - 安全性：防止内存泄漏
 * 
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {

    /**
     * 缓存响应体的字节数组输出流
     * 使用ByteArrayOutputStream提供高效的内存写入
     */
    private final ByteArrayOutputStream cachedBody = new ByteArrayOutputStream();

    /**
     * 包装后的ServletOutputStream
     */
    private final ServletOutputStream outputStream;

    /**
     * 包装后的PrintWriter
     */
    private PrintWriter writer;

    /**
     * 最大缓存大小（5MB）
     * 防止大响应导致内存溢出
     */
    private static final int MAX_CACHE_SIZE = 5 * 1024 * 1024;

    /**
     * 构造函数
     * 
     * @param response 原始响应对象
     * @throws IOException IO异常
     */
    public CachedBodyHttpServletResponse(HttpServletResponse response) throws IOException {
        super(response);
        this.outputStream = new CachedBodyServletOutputStream(response.getOutputStream(), cachedBody);
    }

    /**
     * 重写getOutputStream方法
     * 
     * @return ServletOutputStream
     * @throws IOException IO异常
     */
    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        return outputStream;
    }

    /**
     * 重写getWriter方法
     * 
     * 延迟创建PrintWriter，节省内存
     * 
     * @return PrintWriter
     * @throws IOException IO异常
     */
    @Override
    public PrintWriter getWriter() throws IOException {
        if (writer == null) {
            String encoding = getCharacterEncoding();
            if (encoding == null) {
                encoding = StandardCharsets.UTF_8.name();
            }
            writer = new PrintWriter(new OutputStreamWriter(outputStream, encoding));
        }
        return writer;
    }

    /**
     * 获取缓存的响应体内容（字符串形式）
     * 
     * 性能优化：延迟转换为字符串，只在需要时才转换
     * 
     * @return 响应体字符串
     */
    public String getCachedBody() {
        // 确保所有数据都已写入缓存
        flushBuffer();
        
        String encoding = getCharacterEncoding();
        if (encoding == null) {
            encoding = StandardCharsets.UTF_8.name();
        }
        
        try {
            return cachedBody.toString(encoding);
        } catch (UnsupportedEncodingException e) {
            return cachedBody.toString(StandardCharsets.UTF_8);
        }
    }

    /**
     * 获取缓存的响应体内容（字节数组形式）
     * 
     * @return 响应体字节数组
     */
    public byte[] getCachedBodyBytes() {
        // 确保所有数据都已写入缓存
        flushBuffer();
        return cachedBody.toByteArray();
    }

    /**
     * 获取响应体大小
     * 
     * @return 响应体大小（字节）
     */
    public int getCachedBodySize() {
        return cachedBody.size();
    }

    /**
     * 检查是否有响应体内容
     * 
     * @return 是否有内容
     */
    public boolean hasCachedBody() {
        return cachedBody.size() > 0;
    }

    /**
     * 刷新缓冲区
     * 
     * 确保所有数据都已写入缓存
     */
    @Override
    public void flushBuffer()  {
        try {
            if (writer != null) {
                writer.flush();
            }
            if (outputStream != null) {
                outputStream.flush();
            }
            super.flushBuffer();
        }catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 重置响应
     * 
     * 同时清空缓存
     */
    @Override
    public void reset() {
        super.reset();
        cachedBody.reset();
        writer = null;
    }

    /**
     * 重置缓冲区
     * 
     * 同时清空缓存
     */
    @Override
    public void resetBuffer() {
        super.resetBuffer();
        cachedBody.reset();
    }

    /**
     * 缓存的ServletOutputStream实现
     * 
     * 功能说明：
     * 1. 双写机制：同时写入原始流和缓存流
     * 2. 支持Servlet 3.0的异步写入API
     * 3. 性能优化的流实现
     * 4. 自动检查缓存大小限制
     */
    private static class CachedBodyServletOutputStream extends ServletOutputStream {

        /**
         * 原始的ServletOutputStream
         */
        private final ServletOutputStream originalOutputStream;

        /**
         * 缓存响应体的输出流
         */
        private final ByteArrayOutputStream cachedBody;

        /**
         * 构造函数
         * 
         * @param originalOutputStream 原始输出流
         * @param cachedBody 缓存输出流
         */
        public CachedBodyServletOutputStream(ServletOutputStream originalOutputStream, ByteArrayOutputStream cachedBody) {
            this.originalOutputStream = originalOutputStream;
            this.cachedBody = cachedBody;
        }

        /**
         * 检查是否准备好写入
         * 
         * @return 是否准备好
         */
        @Override
        public boolean isReady() {
            return originalOutputStream.isReady();
        }

        /**
         * 设置写入监听器（Servlet 3.0异步API）
         * 
         * @param writeListener 写入监听器
         */
        @Override
        public void setWriteListener(WriteListener writeListener) {
            originalOutputStream.setWriteListener(writeListener);
        }

        /**
         * 写入单个字节
         * 
         * 双写机制：同时写入原始流和缓存流
         * 
         * @param b 字节值
         * @throws IOException IO异常
         */
        @Override
        public void write(int b) throws IOException {
            originalOutputStream.write(b);
            
            // 检查缓存大小限制
            if (cachedBody.size() < MAX_CACHE_SIZE) {
                cachedBody.write(b);
            }
        }

        /**
         * 写入字节数组
         * 
         * @param b 字节数组
         * @throws IOException IO异常
         */
        @Override
        public void write(byte[] b) throws IOException {
            originalOutputStream.write(b);
            
            // 检查缓存大小限制
            if (cachedBody.size() + b.length <= MAX_CACHE_SIZE) {
                cachedBody.write(b);
            }
        }

        /**
         * 写入字节数组的指定部分
         * 
         * @param b 字节数组
         * @param off 起始偏移量
         * @param len 写入长度
         * @throws IOException IO异常
         */
        @Override
        public void write(byte[] b, int off, int len) throws IOException {
            originalOutputStream.write(b, off, len);
            
            // 检查缓存大小限制
            if (cachedBody.size() + len <= MAX_CACHE_SIZE) {
                cachedBody.write(b, off, len);
            }
        }

        /**
         * 刷新流
         * 
         * @throws IOException IO异常
         */
        @Override
        public void flush() throws IOException {
            originalOutputStream.flush();
            cachedBody.flush();
        }

        /**
         * 关闭流
         * 
         * @throws IOException IO异常
         */
        @Override
        public void close() throws IOException {
            originalOutputStream.close();
            cachedBody.close();
        }
    }
}

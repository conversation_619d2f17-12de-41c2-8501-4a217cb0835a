package com.miaowen.oa.system.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.system.infrastructure.entity.OaApplicationEntity;
import com.miaowen.oa.system.interfaces.req.application.ApplicationPageQueryRequest;
import com.miaowen.oa.system.interfaces.req.application.ApplicationSaveRequest;
import com.miaowen.oa.system.interfaces.req.application.ApplicationUpdateRequest;
import com.miaowen.oa.system.interfaces.res.application.OaApplicationDetailResp;
import com.miaowen.oa.system.interfaces.res.application.OaApplicationPageResp;
import com.miaowen.oa.system.interfaces.res.application.OaApplicationResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/17 14:28
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */

public interface OaApplicationService extends IService<OaApplicationEntity> {
    OaApplicationDetailResp getApplicationDetail(Long id);

    PageResult<OaApplicationPageResp> getApplicationPage(ApplicationPageQueryRequest page);

    void addApplication(ApplicationSaveRequest applicationSaveRequest);

    void updateApplication(ApplicationUpdateRequest applicationUpdateRequest);

    void deleteApplication(Long id);

    List<OaApplicationResp> getApplicationList();

}

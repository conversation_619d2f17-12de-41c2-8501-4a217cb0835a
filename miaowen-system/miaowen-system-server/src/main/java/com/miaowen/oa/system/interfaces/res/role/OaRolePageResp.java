package com.miaowen.oa.system.interfaces.res.role;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.oa.system.interfaces.res.dept.UserDepartmentResp;
import com.miaowen.oa.system.interfaces.res.job.OaJobResp;
import com.miaowen.oa.system.interfaces.res.project.OaProjectGroupResp;
import com.miaowen.oa.system.interfaces.res.user.OaLeaderResp;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */
@Data
public class OaRolePageResp {

    private Long id;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 真实名称
     */
    private String realName;


    /**
     * 员工编号(工号):固定四位数字,例如0001, 0002
     */
    private String userCode;

    /**
     * 手机号
     */
    private String phone;


    /**
     * 1男 2女
     */
    private Integer gender;


    /**
     * 部门响应体
     */
    private List<UserDepartmentResp> departments;

    /**
     * 项目组响应体
     */
    private List<OaProjectGroupResp> projectGroups;

    /**
     * 岗位响应体
     */
    private List<OaJobResp> jobs;


    /**
     * 添加时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     *  操作人
     */
    private String operator;

}

package com.miaowen.oa.system.infrastructure.filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.miaowen.oa.framework.common.util.servlet.ServletUtils;
import com.miaowen.oa.framework.security.core.LoginUser;
import com.miaowen.oa.framework.security.core.util.SecurityFrameworkUtils;
import com.miaowen.oa.system.domain.service.OperationLogService;
import com.miaowen.oa.system.infrastructure.entity.OaOperationLogEntity;
import com.miaowen.oa.system.infrastructure.utils.OperationLogUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 操作日志过滤器
 *
 * 功能说明：
 * 1. 基于Servlet过滤器实现日志录入
 * 2. 自动记录用户的查看、新增、修改、删除等操作
 * 3. 记录操作前后的数据变更
 * 4. 支持日志数据的筛选和统计
 * 5. 遵循时间局部性和空间局部性原理优化性能
 *
 * 实现原理：
 * - 在请求处理的最外层进行拦截
 * - 记录请求开始和结束时间
 * - 异步保存操作日志，避免阻塞主线程
 * - 使用ThreadLocal存储请求上下文，减少参数传递
 *
 * 性能优化：
 * - 时间局部性：最近访问的数据更可能被再次访问
 * - 空间局部性：相邻的数据更可能被一起访问
 * - 异步处理：日志记录不影响业务响应时间
 * - 内存优化：及时清理ThreadLocal，防止内存泄漏
 *
 * 注意：不使用@Component注解，通过FilterRegistrationBean注册
 *
 * <AUTHOR>
 * @Date 2025/7/18
 * @Company 武汉市妙闻网络科技有限公司
 */
@Slf4j
public class OperationLogFilter implements Filter {

    /**
     * Spring应用上下文
     * 用于获取Spring管理的Bean
     */
    private ApplicationContext applicationContext;

    /**
     * 操作日志服务
     * 用于异步记录操作日志
     */
    private OperationLogService operationLogService;

    /**
     * JSON处理器
     * 用于序列化和反序列化JSON数据
     */
    private ObjectMapper objectMapper;

    // ThreadLocal存储请求上下文，利用空间局部性原理
    private static final ThreadLocal<OperationContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置ApplicationContext
     *
     * @param applicationContext Spring应用上下文
     */
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("操作日志过滤器初始化开始");

        // 初始化依赖的Bean
        if (applicationContext != null) {
            try {
                this.operationLogService = applicationContext.getBean(OperationLogService.class);
                this.objectMapper = applicationContext.getBean(ObjectMapper.class);
                log.info("操作日志过滤器依赖注入成功");
            } catch (Exception e) {
                log.warn("操作日志过滤器依赖注入失败，将在运行时获取: {}", e.getMessage());
            }
        }

        log.info("操作日志过滤器初始化完成");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        // 只处理HTTP请求
        if (!(request instanceof HttpServletRequest) || !(response instanceof HttpServletResponse)) {
            chain.doFilter(request, response);
            return;
        }

        // 运行时获取依赖（如果初始化时未成功）
        if (operationLogService == null && applicationContext != null) {
            try {
                this.operationLogService = applicationContext.getBean(OperationLogService.class);
                this.objectMapper = applicationContext.getBean(ObjectMapper.class);
            } catch (Exception e) {
                log.debug("运行时获取依赖失败，跳过日志记录: {}", e.getMessage());
                chain.doFilter(request, response);
                return;
            }
        }

        // 如果依赖仍然为空，跳过日志记录
        if (operationLogService == null) {
            log.debug("操作日志服务未初始化，跳过日志记录");
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 检查是否需要记录日志（时间局部性优化：快速过滤不需要记录的请求）
        if (!shouldRecordLog(httpRequest)) {
            chain.doFilter(request, response);
            return;
        }

        // 创建操作上下文（空间局部性：将相关数据聚合在一起）
        OperationContext context = createOperationContext(httpRequest);
        CONTEXT_HOLDER.set(context);

        try {
            // 包装请求以支持多次读取请求体
            CachedBodyHttpServletRequest cachedRequest = new CachedBodyHttpServletRequest(httpRequest);
            
            // 包装响应以捕获响应信息
            CachedBodyHttpServletResponse cachedResponse = new CachedBodyHttpServletResponse(httpResponse);

            // 记录操作前数据（用于数据变更对比）
            recordBeforeData(context, cachedRequest);

            // 继续过滤器链
            chain.doFilter(cachedRequest, cachedResponse);

            // 记录操作后数据和响应信息
            recordAfterData(context, cachedRequest, cachedResponse);

            // 异步记录操作日志（性能优化：不阻塞主线程）
            recordOperationLogAsync(context);

        } catch (Exception e) {
            // 记录异常信息
            context.setException(e);
            recordOperationLogAsync(context);
            throw e;
        } finally {
            // 清理ThreadLocal，防止内存泄漏
            CONTEXT_HOLDER.remove();
        }
    }

    @Override
    public void destroy() {
        log.info("操作日志过滤器销毁");
    }

    /**
     * 判断是否需要记录日志
     * 
     * 时间局部性优化：
     * - 优先判断最常见的过滤条件
     * - 使用短路求值减少不必要的计算
     * 
     * @param request HTTP请求
     * @return 是否需要记录
     */
    private boolean shouldRecordLog(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String method = request.getMethod();

        // 快速过滤：静态资源（最常见的过滤条件）
        if (isStaticResource(uri)) {
            return false;
        }

        // 快速过滤：系统端点
        if (isSystemEndpoint(uri)) {
            return false;
        }

        // 快速过滤：OPTIONS请求
        if ("OPTIONS".equals(method)) {
            return false;
        }

        // 只记录API请求
        return uri.startsWith("/api/");
    }

    /**
     * 是否为静态资源
     * 
     * 空间局部性优化：将相关的文件扩展名放在一起判断
     */
    private boolean isStaticResource(String uri) {
        // 按使用频率排序，提高命中率
        return uri.contains("/static/") || 
               uri.endsWith(".js") ||      // 最常见
               uri.endsWith(".css") ||     // 次常见
               uri.endsWith(".png") ||
               uri.endsWith(".jpg") ||
               uri.endsWith(".ico") ||
               uri.endsWith(".woff") ||
               uri.endsWith(".ttf") ||
               uri.contains("/css/") || 
               uri.contains("/js/") || 
               uri.contains("/images/");
    }

    /**
     * 是否为系统端点
     */
    private boolean isSystemEndpoint(String uri) {
        return uri.startsWith("/actuator/") ||
               uri.startsWith("/health") ||
               uri.startsWith("/info") ||
               uri.startsWith("/metrics") ||
               uri.startsWith("/swagger") ||
               uri.startsWith("/v3/api-docs");
    }

    /**
     * 创建操作上下文
     * 
     * 空间局部性优化：将相关的操作数据聚合在一个对象中
     * 
     * @param request HTTP请求
     * @return 操作上下文
     */
    private OperationContext createOperationContext(HttpServletRequest request) {
        OperationContext context = new OperationContext();
        
        // 基础信息（按访问频率排序）
        context.setStartTime(System.currentTimeMillis());
        context.setTraceId(OperationLogUtil.generateTraceId());
        context.setRequestMethod(request.getMethod());
        context.setRequestUrl(request.getRequestURI());
        context.setClientIp(ServletUtils.getClientIP(request));
        context.setUserAgent(request.getHeader("User-Agent"));
        
        // 用户信息
        fillUserInfo(context);
        
        // 操作信息（从URL推断）
        fillOperationInfo(context, request);
        
        return context;
    }

    /**
     * 填充用户信息
     * 
     * @param context 操作上下文
     */
    private void fillUserInfo(OperationContext context) {
        try {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            if (loginUser != null) {
                context.setUserId(loginUser.getId());
                context.setUsername(SecurityFrameworkUtils.getLoginUserRealName());
            }
        } catch (Exception e) {
            log.debug("获取用户信息失败", e);
        }
    }

    /**
     * 填充操作信息
     * 
     * 时间局部性优化：根据URL模式快速推断操作类型
     * 
     * @param context 操作上下文
     * @param request HTTP请求
     */
    private void fillOperationInfo(OperationContext context, HttpServletRequest request) {
        String uri = request.getRequestURI();
        String method = request.getMethod();
        
        // 操作类型（按HTTP方法推断）
        context.setOperationType(getOperationTypeByMethod(method, uri));
        
        // 操作模块（从URL提取）
        context.setOperationModule(extractModuleName(uri));
        
        // 操作功能
        context.setOperationFunction(generateOperationFunction(method, uri));
        
        // 操作描述
        context.setOperationDescription(generateOperationDescription(method, uri));
        
        // 业务类型
        context.setBusinessType(extractBusinessType(uri));
        
        // 风险等级
        context.setRiskLevel(calculateRiskLevel(method, uri));
        
        // 敏感操作标记
        context.setIsSensitive(isSensitiveOperation(uri));
        
        // 操作来源
        context.setOperationSource(1); // Web端
    }

    /**
     * 记录操作前数据
     * 
     * @param context 操作上下文
     * @param request 请求对象
     */
    private void recordBeforeData(OperationContext context, CachedBodyHttpServletRequest request) {
        try {
            // 记录请求参数
            String requestParams = getRequestParams(request);
            context.setRequestParams(requestParams);
            
            // 对于修改和删除操作，尝试获取操作前数据
            if (context.getOperationType() == 3 || context.getOperationType() == 4) {
                // 这里可以根据业务需要实现获取操作前数据的逻辑
                // 例如：从请求参数中提取ID，然后查询数据库获取当前数据
                String beforeData = extractBeforeData(context, request);
                context.setBeforeData(beforeData);
            }
            
        } catch (Exception e) {
            log.warn("记录操作前数据失败", e);
        }
    }

    /**
     * 记录操作后数据
     * 
     * @param context 操作上下文
     * @param request 请求对象
     * @param response 响应对象
     */
    private void recordAfterData(OperationContext context, CachedBodyHttpServletRequest request, 
                               CachedBodyHttpServletResponse response) {
        try {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - context.getStartTime();
            context.setExecutionTime(executionTime);
            
            // 记录响应状态
            int status = response.getStatus();
            context.setOperationState(status >= 200 && status < 300 ? 1 : 0);
            
            // 记录响应结果
            String responseResult = response.getCachedBody();
            context.setResponseResult(responseResult);
            
            // 对于新增和修改操作，记录操作后数据
            if (context.getOperationType() == 2 || context.getOperationType() == 3) {
                String afterData = extractAfterData(context, request, response);
                context.setAfterData(afterData);
                
                // 对比数据变更
                if (StringUtils.hasText(context.getBeforeData()) && StringUtils.hasText(afterData)) {
                    String changedFields = compareDataChanges(context.getBeforeData(), afterData);
                    context.setChangedFields(changedFields);
                }
            }
            
        } catch (Exception e) {
            log.warn("记录操作后数据失败", e);
        }
    }

    /**
     * 异步记录操作日志
     * 
     * 性能优化：使用异步处理，不阻塞主线程
     * 
     * @param context 操作上下文
     */
    private void recordOperationLogAsync(OperationContext context) {
        CompletableFuture.runAsync(() -> {
            try {
                OaOperationLogEntity logEntity = buildLogEntity(context);
                operationLogService.recordOperationLogAsync(logEntity);
                
                log.debug("操作日志记录完成，URL：{}，耗时：{}ms", 
                         context.getRequestUrl(), context.getExecutionTime());
                         
            } catch (Exception e) {
                log.error("异步记录操作日志失败", e);
            }
        });
    }

    /**
     * 构建日志实体
     * 
     * 空间局部性优化：按字段访问频率排序
     * 
     * @param context 操作上下文
     * @return 日志实体
     */
    private OaOperationLogEntity buildLogEntity(OperationContext context) {
        OaOperationLogEntity entity = new OaOperationLogEntity();
        
        // 基础信息（最常访问的字段）
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setTraceId(context.getTraceId());
        
        // 用户信息
        entity.setUserId(context.getUserId());
        entity.setUsername(context.getUsername());
        entity.setCreator(context.getUserId());
        
        // 操作信息
        entity.setOperationType(context.getOperationType());
        entity.setOperationModule(context.getOperationModule());
        entity.setOperationFunction(context.getOperationFunction());
        entity.setOperationDescription(context.getOperationDescription());
        entity.setOperationState(context.getOperationState());
        
        // 业务信息
        entity.setBusinessType(context.getBusinessType());
        entity.setBusinessId(context.getBusinessId());
        
        // 请求信息
        entity.setRequestMethod(context.getRequestMethod());
        entity.setRequestUrl(context.getRequestUrl());
        entity.setRequestParams(context.getRequestParams());
        entity.setClientIp(context.getClientIp());
        entity.setUserAgent(context.getUserAgent());
        
        // 执行信息
        entity.setExecutionTime(context.getExecutionTime());
        entity.setResponseResult(context.getResponseResult());
        entity.setErrorMessage(context.getException() != null ? context.getException().getMessage() : null);
        
        // 数据变更信息
        entity.setBeforeData(context.getBeforeData());
        entity.setAfterData(context.getAfterData());
        entity.setChangedFields(context.getChangedFields());
        
        // 风险信息
        entity.setRiskLevel(context.getRiskLevel());
        entity.setIsSensitive(context.getIsSensitive());
        entity.setOperationSource(context.getOperationSource());
        
        // 版本信息
        entity.setDataVersion(1);
        
        return entity;
    }

    // ========== 辅助方法 ==========

    /**
     * 根据HTTP方法获取操作类型
     *
     * 时间局部性优化：按使用频率排序判断条件
     *
     * @param method HTTP方法
     * @param uri 请求URI
     * @return 操作类型
     */
    private Integer getOperationTypeByMethod(String method, String uri) {
        switch (method.toUpperCase()) {
            case "GET":
                // 按使用频率判断GET请求的具体类型
                if (uri.contains("/export") || uri.contains("/download")) {
                    return 5; // 导出
                }
                return 1; // 查看
            case "POST":
                if (uri.contains("/import") || uri.contains("/upload")) {
                    return 6; // 导入
                }
                if (uri.contains("/login")) {
                    return 7; // 登录
                }
                return 2; // 新增
            case "PUT":
                // edit
                return 3;
            case "PATCH":
                return 3; // 修改
            case "DELETE":
                return 4; // 删除
            default:
                return 9; // 其他
        }
    }

    /**
     * 提取模块名称
     *
     * 空间局部性优化：从URL路径中提取模块信息
     *
     * @param uri 请求URI
     * @return 模块名称
     */
    private String extractModuleName(String uri) {
        String[] segments = uri.split("/");
        if (segments.length >= 4) {
            return segments[3]; // /api/v1/user -> user
        }
        return "unknown";
    }

    /**
     * 生成操作功能描述
     *
     * @param method HTTP方法
     * @param uri 请求URI
     * @return 操作功能
     */
    private String generateOperationFunction(String method, String uri) {
        String operation = getOperationNameByMethod(method);
        String resource = extractResourceName(uri);
        return operation + resource;
    }

    /**
     * 生成操作描述
     *
     * @param method HTTP方法
     * @param uri 请求URI
     * @return 操作描述
     */
    private String generateOperationDescription(String method, String uri) {
        return String.format("%s %s", getOperationNameByMethod(method), uri);
    }

    /**
     * 根据方法获取操作名称
     *
     * @param method HTTP方法
     * @return 操作名称
     */
    private String getOperationNameByMethod(String method) {
        switch (method.toUpperCase()) {
            case "GET": return "查询";
            case "POST": return "新增";
            case "PUT": return "修改";
            case "PATCH": return "更新";
            case "DELETE": return "删除";
            default: return "操作";
        }
    }

    /**
     * 提取资源名称
     *
     * @param uri 请求URI
     * @return 资源名称
     */
    private String extractResourceName(String uri) {
        String[] segments = uri.split("/");
        for (int i = segments.length - 1; i >= 0; i--) {
            if (StringUtils.hasText(segments[i]) && !segments[i].matches("\\d+")) {
                return segments[i];
            }
        }
        return "资源";
    }

    /**
     * 提取业务类型
     *
     * 时间局部性优化：按使用频率排序判断条件
     *
     * @param uri 请求URI
     * @return 业务类型
     */
    private String extractBusinessType(String uri) {
        // 按使用频率排序
        if (uri.contains("/user")) return "user";
        if (uri.contains("/role")) return "role";
        if (uri.contains("/menu")) return "menu";
        if (uri.contains("/dept")) return "dept";
        if (uri.contains("/dict")) return "dict";
        if (uri.contains("/config")) return "config";
        if (uri.contains("/log")) return "log";
        if (uri.contains("/file")) return "file";
        if (uri.contains("/notice")) return "notice";
        return "unknown";
    }

    /**
     * 计算风险等级
     *
     * @param method HTTP方法
     * @param uri 请求URI
     * @return 风险等级
     */
    private Integer calculateRiskLevel(String method, String uri) {
        // 删除操作为高风险
        if ("DELETE".equals(method)) {
            return 3;
        }

        // 敏感URL为高风险
        if (uri.contains("/delete") || uri.contains("/remove") ||
            uri.contains("/reset") || uri.contains("/clear") ||
            uri.contains("/password") || uri.contains("/secret")) {
            return 3;
        }

        // 修改操作为中风险
        if ("PUT".equals(method) || "PATCH".equals(method)) {
            return 2;
        }

        // 其他为低风险
        return 1;
    }

    /**
     * 判断是否为敏感操作
     *
     * @param uri 请求URI
     * @return 是否敏感
     */
    private Integer isSensitiveOperation(String uri) {
        boolean sensitive = uri.contains("/password") ||
                           uri.contains("/secret") ||
                           uri.contains("/key") ||
                           uri.contains("/token") ||
                           uri.contains("/auth") ||
                           uri.contains("/login") ||
                           uri.contains("/logout");
        return sensitive ? 1 : 0;
    }

    /**
     * 获取请求参数
     *
     * @param request 请求对象
     * @return 请求参数JSON字符串
     */
    private String getRequestParams(CachedBodyHttpServletRequest request) {
        try {
            String method = request.getMethod();
            if ("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method)) {
                // 获取请求体
                String body = request.getCachedBody();
                if (StringUtils.hasText(body)) {
                    return body;
                }
            }

            // 获取查询参数
            String queryString = request.getQueryString();
            if (StringUtils.hasText(queryString)) {
                return queryString;
            }

        } catch (Exception e) {
            log.warn("获取请求参数失败", e);
        }
        return "";
    }

    /**
     * 提取操作前数据
     *
     * @param context 操作上下文
     * @param request 请求对象
     * @return 操作前数据
     */
    private String extractBeforeData(OperationContext context, CachedBodyHttpServletRequest request) {
        try {
            String uri = request.getRequestURI();
            String method = request.getMethod();

            // 只有修改和删除操作需要记录操作前数据
            if (!"PUT".equals(method) && !"PATCH".equals(method) && !"DELETE".equals(method)) {
                return null;
            }

            // 解析业务信息
            BusinessInfo businessInfo = parseBusinessInfoFromUrl(uri);
            if (businessInfo == null) {
                return null;
            }

            // 设置业务信息到上下文
            context.setBusinessType(businessInfo.getType());
            context.setBusinessId(businessInfo.getId());

            // 根据业务类型获取数据
            return getDataByBusinessInfo(businessInfo.getType(), businessInfo.getId());

        } catch (Exception e) {
            log.warn("提取操作前数据失败", e);
        }
        return null;
    }

    /**
     * 从URL解析业务信息
     */
    private BusinessInfo parseBusinessInfoFromUrl(String uri) {
        try {
            // 匹配模式：/api/v1/user/1001
            String[] segments = uri.split("/");
            if (segments.length >= 4) {
                String businessType = segments[3]; // user, role, dept等

                // 查找ID
                for (int i = 4; i < segments.length; i++) {
                    if (segments[i].matches("\\d+")) {
                        Long id = Long.valueOf(segments[i]);
                        return new BusinessInfo(businessType, id);
                    }
                }

                // 如果没有找到ID，返回业务类型
                return new BusinessInfo(businessType, null);
            }
        } catch (Exception e) {
            log.debug("解析业务信息失败：{}", uri);
        }
        return null;
    }

    /**
     * 根据业务信息获取数据
     */
    private String getDataByBusinessInfo(String businessType, Long businessId) {
        if (businessId == null) {
            return null;
        }

        try {
            // 根据业务类型构建查询SQL
            String tableName = getTableNameByBusinessType(businessType);
            if (tableName == null) {
                return null;
            }

            // 这里需要注入JdbcTemplate或者相应的Service
            // 为了演示，先返回模拟数据
            return queryDataFromDatabase(tableName, businessId);

        } catch (Exception e) {
            log.warn("根据业务信息获取数据失败，类型：{}，ID：{}", businessType, businessId, e);
        }
        return null;
    }

    /**
     * 从数据库查询数据
     */
    private String queryDataFromDatabase(String tableName, Long id) {
        try {
            // 这里应该使用JdbcTemplate或MyBatis查询数据
            // 为了演示，返回模拟的JSON数据
            if (objectMapper != null) {
                Map<String, Object> data = new HashMap<>();
                data.put("id", id);
                data.put("tableName", tableName);
                data.put("queryTime", LocalDateTime.now().toString());
                data.put("beforeOperation", true);
                return objectMapper.writeValueAsString(data);
            }
        } catch (Exception e) {
            log.warn("查询数据库失败，表：{}，ID：{}", tableName, id, e);
        }
        return null;
    }

    /**
     * 根据业务类型获取表名
     */
    private String getTableNameByBusinessType(String businessType) {
        Map<String, String> typeToTable = new HashMap<>();
        typeToTable.put("user", "oa_user");
        typeToTable.put("users", "oa_user");
        typeToTable.put("role", "oa_role");
        typeToTable.put("roles", "oa_role");
        typeToTable.put("menu", "oa_menu");
        typeToTable.put("menus", "oa_menu");
        typeToTable.put("dept", "oa_dept");
        typeToTable.put("depts", "oa_dept");
        typeToTable.put("dict", "oa_dict");
        typeToTable.put("dicts", "oa_dict");

        return typeToTable.get(businessType);
    }

    /**
     * 业务信息类
     */
    private static class BusinessInfo {
        private String type;
        private Long id;

        public BusinessInfo(String type, Long id) {
            this.type = type;
            this.id = id;
        }

        public String getType() { return type; }
        public Long getId() { return id; }
    }

    /**
     * 提取操作后数据
     *
     * @param context 操作上下文
     * @param request 请求对象
     * @param response 响应对象
     * @return 操作后数据
     */
    private String extractAfterData(OperationContext context, CachedBodyHttpServletRequest request,
                                  CachedBodyHttpServletResponse response) {
        try {
            String method = request.getMethod();

            // 只有新增和修改操作需要记录操作后数据
            if (!"POST".equals(method) && !"PUT".equals(method) && !"PATCH".equals(method)) {
                return null;
            }

            // 首先尝试从响应中提取数据
            String responseBody = response.getCachedBody();
            if (StringUtils.hasText(responseBody)) {
                String extractedData = extractDataFromResponse(responseBody, context);
                if (StringUtils.hasText(extractedData)) {
                    return extractedData;
                }
            }

            // 如果响应中没有完整数据，尝试重新查询
            if (context.getBusinessId() != null && StringUtils.hasText(context.getBusinessType())) {
                return getDataByBusinessInfo(context.getBusinessType(), context.getBusinessId());
            }

            // 对于新增操作，尝试从响应中解析新生成的ID
            if ("POST".equals(method)) {
                Long newId = extractIdFromResponse(responseBody);
                if (newId != null && StringUtils.hasText(context.getBusinessType())) {
                    context.setBusinessId(newId);
                    return getDataByBusinessInfo(context.getBusinessType(), newId);
                }
            }

        } catch (Exception e) {
            log.warn("提取操作后数据失败", e);
        }
        return null;
    }

    /**
     * 从响应中提取数据
     */
    private String extractDataFromResponse(String responseBody, OperationContext context) {
        try {
            if (objectMapper == null) {
                return responseBody;
            }

            JsonNode responseNode = objectMapper.readTree(responseBody);

            // 尝试从data字段提取
            if (responseNode.has("data")) {
                JsonNode dataNode = responseNode.get("data");
                if (dataNode.isObject() || dataNode.isArray()) {
                    return dataNode.toString();
                }
            }

            // 如果响应本身就是数据对象
            if (responseNode.isObject() && responseNode.has("id")) {
                return responseNode.toString();
            }

        } catch (Exception e) {
            log.debug("从响应中提取数据失败", e);
        }
        return null;
    }

    /**
     * 从响应中提取ID
     */
    private Long extractIdFromResponse(String responseBody) {
        try {
            if (!StringUtils.hasText(responseBody) || objectMapper == null) {
                return null;
            }

            JsonNode responseNode = objectMapper.readTree(responseBody);

            // 尝试从data.id获取
            if (responseNode.has("data")) {
                JsonNode dataNode = responseNode.get("data");
                if (dataNode.has("id")) {
                    return dataNode.get("id").asLong();
                }
            }

            // 尝试直接从id获取
            if (responseNode.has("id")) {
                return responseNode.get("id").asLong();
            }

        } catch (Exception e) {
            log.debug("从响应中提取ID失败", e);
        }
        return null;
    }

    /**
     * 对比数据变更
     *
     * @param beforeData 操作前数据
     * @param afterData 操作后数据
     * @return 变更字段信息
     */
    private String compareDataChanges(String beforeData, String afterData) {
        try {
            if (!StringUtils.hasText(beforeData) || !StringUtils.hasText(afterData)) {
                return null;
            }

            if (objectMapper == null) {
                // 简单的字符串对比
                return String.format("{\"hasChange\":%s,\"beforeLength\":%d,\"afterLength\":%d}",
                    !beforeData.equals(afterData), beforeData.length(), afterData.length());
            }

            // 解析JSON数据
            JsonNode beforeNode = objectMapper.readTree(beforeData);
            JsonNode afterNode = objectMapper.readTree(afterData);

            // 对比JSON对象
            Map<String, Object> changes = new HashMap<>();
            Set<String> allFields = new HashSet<>();

            // 收集所有字段名
            if (beforeNode.isObject()) {
                beforeNode.fieldNames().forEachRemaining(allFields::add);
            }
            if (afterNode.isObject()) {
                afterNode.fieldNames().forEachRemaining(allFields::add);
            }

            // 对比每个字段
            Map<String, Map<String, Object>> fieldChanges = new HashMap<>();
            for (String fieldName : allFields) {
                // 跳过系统字段
                if (isSystemField(fieldName)) {
                    continue;
                }

                JsonNode beforeValue = beforeNode.get(fieldName);
                JsonNode afterValue = afterNode.get(fieldName);

                if (!Objects.equals(beforeValue, afterValue)) {
                    Map<String, Object> fieldChange = new HashMap<>();
                    fieldChange.put("before", beforeValue != null ? beforeValue.asText() : null);
                    fieldChange.put("after", afterValue != null ? afterValue.asText() : null);

                    // 敏感字段脱敏
                    if (isSensitiveField(fieldName)) {
                        fieldChange.put("before", maskSensitiveValue(beforeValue));
                        fieldChange.put("after", maskSensitiveValue(afterValue));
                    }

                    fieldChanges.put(fieldName, fieldChange);
                }
            }

            if (!fieldChanges.isEmpty()) {
                changes.put("changedFields", fieldChanges);
                changes.put("changeCount", fieldChanges.size());
                changes.put("hasChange", true);
            } else {
                changes.put("hasChange", false);
            }

            return objectMapper.writeValueAsString(changes);

        } catch (Exception e) {
            log.warn("对比数据变更失败", e);
            // 降级处理
            return String.format("{\"hasChange\":true,\"beforeLength\":%d,\"afterLength\":%d,\"error\":\"%s\"}",
                beforeData.length(), afterData.length(), e.getMessage());
        }
    }

    /**
     * 判断是否为系统字段
     */
    private boolean isSystemField(String fieldName) {
        Set<String> systemFields = new HashSet<>(Arrays.asList(
            "updateTime", "update_time", "updater", "dataVersion", "data_version",
            "lastModified", "last_modified", "version", "createTime", "create_time"
        ));
        return systemFields.contains(fieldName);
    }

    /**
     * 判断是否为敏感字段
     */
    private boolean isSensitiveField(String fieldName) {
        Set<String> sensitiveFields = new HashSet<>(Arrays.asList(
            "password", "passwd", "pwd", "secret", "token", "key", "auth",
            "credential", "authorization", "signature", "sign", "salt"
        ));
        return sensitiveFields.contains(fieldName.toLowerCase());
    }

    /**
     * 脱敏敏感值
     */
    private String maskSensitiveValue(JsonNode value) {
        if (value == null || value.isNull()) {
            return null;
        }

        String str = value.asText();
        if (str.length() <= 2) {
            return "***";
        } else if (str.length() <= 6) {
            return str.charAt(0) + "***" + str.charAt(str.length() - 1);
        } else {
            return str.substring(0, 2) + "***" + str.substring(str.length() - 2);
        }
    }
}

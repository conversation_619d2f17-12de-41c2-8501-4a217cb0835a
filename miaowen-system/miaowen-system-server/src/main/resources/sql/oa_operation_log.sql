-- 操作日志表创建脚本
-- 数据库：g3-mw-oa-system
-- 表名：oa_operation_log
-- 功能：记录用户操作日志，支持数据变更追踪和审计

DROP TABLE IF EXISTS `oa_operation_log`;

CREATE TABLE `oa_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作用户ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作用户名',
  `operation_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '操作类型：1-查看 2-新增 3-修改 4-删除 5-导出 6-导入 7-登录 8-登出 9-其他',
  `operation_module` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作模块',
  `operation_function` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作功能',
  `operation_state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '操作状态：0-失败 1-成功',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `business_id` bigint(20) DEFAULT NULL COMMENT '业务数据ID',
  `business_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '业务数据类型',
  `operation_description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '操作描述',
  `execution_time` bigint(20) DEFAULT '0' COMMENT '操作耗时(毫秒)',
  `before_data` longtext COLLATE utf8mb4_unicode_ci COMMENT '操作前数据(JSON格式)',
  `after_data` longtext COLLATE utf8mb4_unicode_ci COMMENT '操作后数据(JSON格式)',
  `changed_fields` text COLLATE utf8mb4_unicode_ci COMMENT '变更字段信息(JSON格式)',
  `data_version` int(11) DEFAULT '1' COMMENT '数据版本',
  `request_method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求方法',
  `request_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求URL',
  `request_params` text COLLATE utf8mb4_unicode_ci COMMENT '请求参数(JSON格式)',
  `response_result` text COLLATE utf8mb4_unicode_ci COMMENT '响应结果',
  `client_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '客户端IP地址',
  `user_agent` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户代理',
  `trace_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '链路追踪ID',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `operation_source` tinyint(4) DEFAULT '1' COMMENT '操作来源：1-Web端 2-移动端 3-API调用 4-定时任务 5-系统内部',
  `risk_level` tinyint(4) DEFAULT '1' COMMENT '风险等级：1-低风险 2-中风险 3-高风险',
  `is_sensitive` tinyint(4) DEFAULT '0' COMMENT '是否敏感操作：0-否 1-是',
  `audit_flag` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '审计标记',
  `extra_data` text COLLATE utf8mb4_unicode_ci COMMENT '扩展字段(JSON格式)',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` int(11) DEFAULT '0' COMMENT '创建者',
  `updater` int(11) DEFAULT '0' COMMENT '更新者',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间(0表示未删除)',
  PRIMARY KEY (`id`),
  KEY `idx_user_type_time` (`user_id`,`operation_type`,`create_time`),
  KEY `idx_module_time` (`operation_module`,`create_time`),
  KEY `idx_business_time` (`business_type`,`business_id`,`create_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_risk_time` (`risk_level`,`create_time`),
  KEY `idx_status_time` (`operation_state`,`create_time`),
  KEY `idx_trace_id` (`trace_id`),
  KEY `idx_ip_time` (`client_ip`,`create_time`),
  KEY `idx_delete_create_time` (`delete_time`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 插入示例数据（可选）
INSERT INTO `oa_operation_log` (
  `user_id`, `username`, `operation_type`, `operation_module`, `operation_function`,
  `operation_state`, `business_type`, `business_id`, `operation_description`,
  `execution_time`, `request_method`, `request_url`, `client_ip`,
  `operation_source`, `risk_level`, `is_sensitive`, `trace_id`
) VALUES 
(1001, 'admin', 2, 'user', '创建用户', 1, 'user', 1001, '创建用户：张三', 150, 'POST', '/api/v1/user', '*************', 1, 1, 0, 'OL1642752000000123456'),
(1001, 'admin', 3, 'user', '修改用户', 1, 'user', 1001, '修改用户信息：张三', 200, 'PUT', '/api/v1/user/1001', '*************', 1, 2, 0, 'OL1642752000000123457'),
(1002, 'operator', 1, 'user', '查看用户列表', 1, 'user', NULL, '查看用户列表', 80, 'GET', '/api/v1/user/page', '*************', 1, 1, 0, 'OL1642752000000123458'),
(1001, 'admin', 4, 'user', '删除用户', 1, 'user', 1002, '删除用户：李四', 120, 'DELETE', '/api/v1/user/1002', '*************', 1, 3, 0, 'OL1642752000000123459');

-- 索引使用说明
-- idx_user_type_time: 用于按用户和操作类型查询，时间范围筛选
-- idx_module_time: 用于按模块查询，时间范围筛选
-- idx_business_time: 用于按业务类型和业务ID查询，数据变更追踪
-- idx_create_time: 用于时间范围查询和排序
-- idx_risk_time: 用于风险等级查询和监控
-- idx_status_time: 用于操作状态查询和统计
-- idx_trace_id: 用于链路追踪查询
-- idx_ip_time: 用于IP地址查询和安全分析
-- idx_delete_create_time: 用于软删除查询和数据清理

-- 性能优化建议
-- 1. 定期清理过期数据，保持表大小合理
-- 2. 根据实际查询模式调整索引
-- 3. 考虑分区表策略（按时间分区）
-- 4. 监控慢查询并优化
-- 5. 定期分析表统计信息

-- 数据清理脚本（示例）
-- 清理90天前的数据
-- UPDATE oa_operation_log SET delete_time = UNIX_TIMESTAMP(NOW()) 
-- WHERE delete_time = 0 AND create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 物理删除已标记删除的数据（7天前标记删除的）
-- DELETE FROM oa_operation_log 
-- WHERE delete_time > 0 AND delete_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY));

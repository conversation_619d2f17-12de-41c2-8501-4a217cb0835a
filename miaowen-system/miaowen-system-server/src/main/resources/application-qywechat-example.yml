# 企业微信集成配置示例
miaowen:
  # 企业微信配置
  qy-wechat:
    enabled: true
    corp-id: your-corp-id
    corp-secret: your-corp-secret
    agent-id: your-agent-id
    redirect-uri: http://your-domain.com/callback
    oa-web-host: http://your-oa-web.com

    
    # HTTP客户端配置
    http-client:
      connect-timeout: 10      # 连接超时10秒
      read-timeout: 30         # 读取超时30秒
      max-retries: 3           # 最大重试3次
      enable-request-logging: false  # 是否启用请求日志
    
    # Token缓存配置
    token-cache:
      key-prefix: qy_wechat_token_
      expire-offset: 60

# 日志配置
logging:
  level:
    com.miaowen.oa.system.application.repository.OaUserService: DEBUG
    com.miaowen.oa.framework.qywechat: DEBUG

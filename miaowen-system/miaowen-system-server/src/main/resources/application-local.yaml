--- #################### 注册中心 + 配置中心相关配置 ####################

spring:
  cloud:
    nacos:
      server-addr: 127.0.0.1:8848 # Nacos 服务器地址
      username: nacos
      password: nacos
      discovery: # 【配置中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
      config: # 【注册中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP

--- #################### 数据库相关配置 ####################
spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration # 禁用 Spring Boot Admin 的 Client 的自动配置
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 1 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: *********************************************************************************************************
          username: chenweihao
          password: tMWDChnQ
        #          username: sa # SQL Server 连接的示例
        #          password: JSm:g(*%lU4ZAkz06cd52KqT3)i1?H7W # SQL Server 连接的示例
        #          username: SYSDBA # DM 连接的示例
        #          password: SYSDBA # DM 连接的示例
#        slave: # 模拟从库，可根据自己需要修改
#          lazy: true # 开启懒加载，保证启动速度
#          url: ***************************************************************************************************************************************************************************
#          username: root
#          password: root


# MyBatis-Plus 配置
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    # 开启 MyBatis SQL 日志（直接输出到控制台）
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6379 # 端口
      database: 0 # 数据库索引
#      password: 123456 # 密码，建议生产环境开启

--- #################### MQ 消息队列相关配置 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  name-server: 127.0.0.1:9876 # RocketMQ Namesrv

spring:
  # RabbitMQ 配置项，对应 RabbitProperties 配置类
  rabbitmq:
    # 临时禁用RabbitMQ连接，避免启动错误
    # 如需启用，请确保RabbitMQ服务正常运行并配置正确的用户名密码
    host: 127.0.0.1        # RabbitMQ 服务器地址
    port: 5672              # 默认端口
    username: guest         # 使用默认用户名
    password: guest         # 使用默认密码
    virtual-host: /         # 使用默认虚拟主机
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000


--- #################### 定时任务相关配置 ####################

xxl:
  job:
    enabled: false # 是否开启调度中心，默认为 true 开启
    admin:
      addresses: http://127.0.0.1:9090/xxl-job-admin # 调度中心部署跟地址

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]

# 日志文件配置
logging:
  level:
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR


--- #################### 秒闻相关配置 ####################
# 企业微信集成配置示例
miaowen:
  # 企业微信配置
  qy-wechat:
    corp-id: wwfc7e4cbb39cb5e55
    corp-secret: Ae2593JIBp1BoZB5AJp2KVPzkOk5BNaat5ECoYp99_w
    agent-id: 1000007
    qy-wechat-host: https://qyapi.weixin.qq.com/cgi-bin
    token:
    encoding-aes-key:
    oa-webHost: https://csoa.1cece.top
    # Token缓存配置
    token-cache:
      key-prefix: qy_wechat_token_
      expire-offset: 60

# 秒闻配置项，设置当前项目所有自定义的配置
system:
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  captcha:
    enable: false # 本地环境，暂时关闭图片验证码，方便登录等接口的测试
  security:
    mock-enable: true
  access-log: # 访问日志的配置项
    enable: true
  wxa-code:
    env-version: develop # 小程序版本: 正式版为 "release"；体验版为 "trial"；开发版为 "develop"
  wxa-subscribe-message:
    miniprogram-state: developer # 跳转小程序类型：开发版为 “developer”；体验版为 “trial”为；正式版为 “formal”

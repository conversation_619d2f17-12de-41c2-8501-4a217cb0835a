<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.miaowen</groupId>
        <artifactId>miaowen-system</artifactId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>miaowen-system-server</artifactId>
    <version>${revision}</version>
    <name>miaowen-system-server</name>
    <description>miaowen-system-server</description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-env</artifactId>
        </dependency>

        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>


        <!-- Web 相关 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-mq</artifactId>
        </dependency>


        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
<!--        <dependency>-->
<!--            <groupId>com.miaowen</groupId>-->
<!--            <artifactId>miaowen-spring-boot-starter-monitor</artifactId>-->
<!--        </dependency>-->

        <!-- 企业微信集成 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-qy-wechat</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Mockito for unit testing -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>

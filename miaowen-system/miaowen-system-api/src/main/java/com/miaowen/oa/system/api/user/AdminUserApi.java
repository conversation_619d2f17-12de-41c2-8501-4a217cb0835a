package com.miaowen.oa.system.api.user;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.api.user.dto.UserInfoDTO;
import com.miaowen.oa.system.api.user.dto.UserSaveDTO;
import com.miaowen.oa.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 管理员用户")
public interface AdminUserApi {

    String PREFIX = ApiConstants.PREFIX + "/user";


    @GetMapping(PREFIX + "/info/{userId}")
    @Operation(summary = "获取用户信息")
    @Parameter(name = "userId", description = "用户id", example = "1", required = true)
    CommonResult<UserInfoDTO> getUserId(@PathVariable("userId") Long userId);

    @GetMapping(PREFIX + "/info/by-username")
    @Operation(summary = "根据用户用户名或者手机号获取用户信息")
    @Parameter(name = "username", description = "用户昵称或者手机号", example = "1", required = true)
    CommonResult<UserInfoDTO> getUserByUsernameOrPhone(@RequestParam("username") String username);

    @GetMapping(PREFIX + "/info/by-qywechat")
    @Operation(summary = "根据企业微信用户ID获取用户信息")
    @Parameter(name = "qyWechatUserId", description = "企业微信用户ID", example = "wechat123", required = true)
    CommonResult<UserInfoDTO> getUserByQyWechatUserId(@RequestParam("qyWechatUserId") String qyWechatUserId);

    @PostMapping(PREFIX + "/info/save")
    @Operation(summary = "根据企业微信用户ID获取用户信息")
    CommonResult<Void> save(@RequestBody UserSaveDTO dto);
}

package com.miaowen.oa.system.api.user.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */

@Data
@Accessors(chain = true)
public class UserSaveDTO {

    @Schema(description = "用户ID", example = "1001", required = true)
    @Min(value = 1, message = "用户ID必须大于0")
    private Long userId;

    @Schema(description = "性别", example = "女", required = true)
    private Integer gender;

    @Schema(description = "个人邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号码", example = "<EMAIL>")
    private String phone;

    @Schema(description = "出生日期", example = "1990-01-01")
    private String birthday;

    /**
     * 政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众
     */
    @Schema(description = "政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众", example = "5")
    private Integer politicalState;


    /**
     * 民族
     */
    @Schema(description = "民族", example = "5")
    private String ethnicity;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号", example = "5")
    private String identity;


    /**
     * 籍贯
     */
    @Schema(description = "籍贯", example = "5")
    private String nativePlace;
    /**
     * 婚姻状况设计说明：
     * 1-未婚，2-已婚，3-离异，4-丧偶
     */
    @Schema(description = "婚姻状况", example = "2", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "婚姻状况代码不能小于1")
    @Max(value = 4, message = "婚姻状况代码不能大于4")
    private Integer maritalState;




    /**
     * 最高学历设计说明：
     * 1-小学，2-初中，3-高中/中专，4-大专，5-本科，6-硕士，7-博士
     */
    @Schema(description = "最高学历", example = "5", allowableValues = {"1", "2", "3", "4", "5", "6", "7"})
    @Min(value = 1, message = "最高学历代码不能小于1")
    @Max(value = 7, message = "最高学历代码不能大于7")
    private Integer highestEducation;


    /**
     * 健康状况设计说明：
     * 1-良好，2-一般，3-患病
     */
    @Schema(description = "健康状况", example = "2", allowableValues = {"1", "2", "3"})
    @Min(value = 1, message = "健康状况代码不能小于1")
    @Max(value = 3, message = "健康状况代码不能大于3")
    private Integer healthState;



    @Schema(description = "工作年限（年）", example = "5")
    @Min(value = 0, message = "工作年限不能小于0年")
    @Max(value = 50, message = "工作年限不能大于50年")
    private Integer workYears;

    @Schema(description = "户口性质", example = "1")
    private Integer householdNature;



    /**
     * 开户行
     */
    @Schema(description = "开户行", example = "5")
    private String bankName;

    /**
     * 银行卡号
     */
    @Schema(description = "银行卡号", example = "5")
    private String bankCardNumber;


    @Schema(description = "现居地址", example = "武汉市")
    private String address;


    @Schema(description = "户籍地址", example = "武汉市")
    private String hometown;
}

package com.miaowen.oa.system.api.user.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:52
 * @Company 武汉市妙闻网络科技有限公司
 * @Description
 */

@Data
@Accessors(chain = true)
public class UserSaveDTO {

    @Schema(description = "用户ID", example = "1001", required = true)
    @Min(value = 1, message = "用户ID必须大于0")
    private Long userId;
    /**
     * 工号（登录账号），固定四位数字,例如0001, 0002
     */
    private String userCode;

    /**
     * 登录用户名
     */
    private String username;

    /**
     * 性别：0-未知 1-男 2-女
     */
    private Integer gender;

    /**
     * 登录邮箱
     */
    private String email;

    /**
     * 手机号（登录用）
     */
    private String phone;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 账户状态：0-无效 1-正常 2-禁用
     */
    private Integer state;

    /**
     * 入职状态：0-无效 1-待入职 2-已入职
     */
    private Integer entryState;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 登录次数
     */
    private Integer loginNumber;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录时间（时间戳）
     */
    private Integer lastLoginTime;

    /**
     * 真实姓名（法定姓名）
     */
    private String realName;

    /**
     * 是否部门管理人员：0-否 1-是
     */
    private Integer isDepartmentManage;

    /**
     * 省份代码
     */
    private Integer province;

    /**
     * 现居住地址
     */
    private String address;

    /**
     * 身份证号
     */
    private String identity;

    /**
     * 职级
     */
    private String level;

    /**
     * 数据权限范围：0-自己 1-部门 2-全公司
     */
    private Integer dataLimitRank;

    /**
     * 加入企业的时间（时间戳）
     */
    private Integer joinTime;

    /**
     * 离开企业的时间（时间戳）
     */
    private Long leaveTime;

    /**
     * 离职备注
     */
    private String leaveNotes;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 企业微信userId
     */
    private String qyWechatUserId;

    /**
     * 是否能通过密码模式登陆：0-否 1-是
     */
    private Integer enablePasswordLogin;

    /**
     * 转正日期（当前）
     */
    private LocalDate regularDate;

    /**
     * 转正说明
     */
    private String regularNotes;

    /**
     * 企业邮箱，格式：<EMAIL>
     */
    private String companyEmail;

    /**
     * 员工类型：1-正式员工（全职） 2-实习生 3-外包员工 4-其他
     */
    private Integer employeeType;

    /**
     * 员工状态：1-试用期，2-准正式，3-正式 4-待离职，5-已离职
     */
    private Integer employeeState;

    /**
     * 入职日期（当前）
     */
    private LocalDate entryDate;

    /**
     * 试用期开始日期（当前）
     */
    private LocalDate probationStartDate;

    /**
     * 试用期结束日期（当前）
     */
    private LocalDate probationEndDate;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众
     */
    private Integer politicalState;

    /**
     * 婚姻状况（当前）：1-未婚 2-已婚 3-离异 4-丧偶
     */
    private Integer maritalState;

    /**
     * 最高学历（当前）：
     * 1-小学 2-初中 3-高中/中专 4-大专 5-本科 6-硕士 7-博士
     */
    private Integer education;

    /**
     * 毕业院校（当前）
     */
    private String graduateSchool;

    /**
     * 专业（当前）
     */
    private String major;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankCardNumber;
}

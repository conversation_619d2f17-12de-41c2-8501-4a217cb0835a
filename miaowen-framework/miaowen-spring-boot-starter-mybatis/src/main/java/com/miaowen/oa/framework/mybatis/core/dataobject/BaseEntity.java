package com.miaowen.oa.framework.mybatis.core.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/11 19:13
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
public class BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间 秒级时间戳
     */
    private Integer deleteTime;

    /**
     * 创建时间  秒级时间戳
     */
    private LocalDateTime createTime;

    /**
     *  更新时间  秒级时间戳
     */
    private LocalDateTime updateTime;
}

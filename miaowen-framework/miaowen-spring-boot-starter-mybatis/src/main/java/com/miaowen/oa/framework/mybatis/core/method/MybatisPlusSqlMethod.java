package com.miaowen.oa.framework.mybatis.core.method;

import lombok.Getter;

/**
 * 自定义MybatisPlus 支持 SQL 方法
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Getter
public enum MybatisPlusSqlMethod {
    /**
     * 逻辑删除
     */
    LOGIC_DELETE_BY_ID("logicDeleteById", "根据ID 逻辑删除一条数据", "<script>\nUPDATE %s %s WHERE %s=#{%s} \n</script>"),
    LOGIC_DELETE("logicDelete", "逻辑删除数据", "<script>\nUPDATE %s %s %s %s\n</script>"),
    LOGIC_DELETE_BATCH_BY_IDS("logicDeleteBatchIds", "根据ID集合，批量逻辑删除数据", "<script>\nUPDATE %s %s WHERE %s IN (%s) %s\n</script>"),
    RECOVER_BY_ID("recoverById", "根据ID集合，逻辑恢复一条数据", "<script>\nUPDATE %s %s WHERE %s=#{%s} %s\n</script>"),



    INSERT_SHARDING("insertSharding", "插入一条数据（选择字段插入）", "<script>\nINSERT INTO %s %s VALUES %s\n</script>"),


    ;


    private final String method;
    private final String desc;
    private final String sql;

    MybatisPlusSqlMethod(String method, String desc, String sql) {
        this.method = method;
        this.desc = desc;
        this.sql = sql;
    }

}

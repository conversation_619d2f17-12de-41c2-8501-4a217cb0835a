package com.miaowen.oa.framework.security.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * 认证默认空实现
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnMissingBean(AuthenticationSecurityConfig.class) // 没有自定义实现时才生效
public class DefaultSecurityAutoConfiguration {
    
    @Bean
    public DefaultAuthenticationSecurityConfig defaultAuthenticationSecurityConfig() {
        return new DefaultAuthenticationSecurityConfig();
    }
}
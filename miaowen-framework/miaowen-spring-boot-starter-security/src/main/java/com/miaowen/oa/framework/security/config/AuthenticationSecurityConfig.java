package com.miaowen.oa.framework.security.config;

import org.springframework.security.config.annotation.SecurityConfigurer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;

/**
 * 抽象认证服务配置
 * <AUTHOR>
 */
public interface AuthenticationSecurityConfig extends SecurityConfigurer<DefaultSecurityFilterChain, HttpSecurity> {

}

package com.miaowen.oa.framework.security.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;

/**
 * DefaultAuthenticationSecurityConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-07-16
 */
@Slf4j
public class DefaultAuthenticationSecurityConfig implements AuthenticationSecurityConfig{
    @Override
    public void init(HttpSecurity builder) {
        log.debug("Initializing default authentication security config (no-op)");
    }

    @Override
    public void configure(HttpSecurity builder) {
        log.debug("Configuring default authentication security (no-op)");
    }
}

package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业微信Token响应DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TokenResponseDTO extends QyWeChatResponse<Void> {
    
    /**
     * 获取到的凭证，最长为512字节
     */
    @JsonProperty("access_token")
    private String accessToken;
    
    /**
     * 凭证的有效时间（秒）
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;
}

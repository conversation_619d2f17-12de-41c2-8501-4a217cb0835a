package com.miaowen.oa.framework.qywechat.core;

import com.miaowen.oa.framework.qywechat.config.QyWeChatProperties;
import com.miaowen.oa.framework.qywechat.dto.*;
import com.miaowen.oa.framework.qywechat.util.QyWechatHttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 企业微信开放平台服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QyWechatOpenClient {

    private final QyWeChatProperties qyWeChatProperties;
    private final StringRedisTemplate stringRedisTemplate;
    private final RestTemplate restTemplate;

    /**
     * 获取企业微信访问令牌
     *
     * @return 访问令牌
     */
    public String getAccessToken() {
        // 1. 先从缓存中获取
        String cacheKey = qyWeChatProperties.getTokenCache().getKeyPrefix() + qyWeChatProperties.getCorpId();
        String cachedToken = stringRedisTemplate.opsForValue().get(cacheKey);
        
        if (StringUtils.hasText(cachedToken)) {
            log.debug("从缓存中获取到企业微信访问令牌");
            return cachedToken;
        }

        // 2. 缓存中没有，调用企业微信API获取
        String url = String.format("%s/gettoken?corpid=%s&corpsecret=%s",
                qyWeChatProperties.getQyWechatHost(),
                qyWeChatProperties.getCorpId(),
                qyWeChatProperties.getCorpSecret());

        try {
            log.debug("调用企业微信API获取访问令牌: url={}", url);
            
            ResponseEntity<TokenResponseDTO> response = restTemplate.getForEntity(url, TokenResponseDTO.class);
            TokenResponseDTO tokenResponse = response.getBody();

            if (Objects.isNull(tokenResponse)) {
                log.error("获取企业微信访问令牌失败：响应为空");
                throw new RuntimeException("获取企业微信访问令牌失败：响应为空");
            }

            if (!tokenResponse.isSuccess()) {
                log.error("获取企业微信访问令牌失败：{}", tokenResponse.getErrorMessage());
                throw new RuntimeException("获取企业微信访问令牌失败：" + tokenResponse.getErrorMessage());
            }

            String accessToken = tokenResponse.getAccessToken();
            Integer expiresIn = tokenResponse.getExpiresIn();

            if (!StringUtils.hasText(accessToken)) {
                log.error("获取企业微信访问令牌失败：令牌为空");
                throw new RuntimeException("获取企业微信访问令牌失败：令牌为空");
            }

            // 3. 将令牌存入缓存，设置过期时间（提前一分钟过期）
            long cacheExpireTime = expiresIn - qyWeChatProperties.getTokenCache().getExpireOffset();
            stringRedisTemplate.opsForValue().set(cacheKey, accessToken, cacheExpireTime, TimeUnit.SECONDS);

            log.info("成功获取企业微信访问令牌，缓存时间：{}秒", cacheExpireTime);
            return accessToken;

        } catch (Exception e) {
            log.error("获取企业微信访问令牌异常", e);
            throw new RuntimeException("获取企业微信访问令牌异常：" + e.getMessage(), e);
        }
    }


    /**
     * 登企业微信登录 code解析
     *
     * @return \think\response\Json
     */
    public String qyWechatH5(String state) {
        String redirect = StringUtils.hasText(state) ? "?redirect=" + state : "";
        return "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + qyWeChatProperties.getCorpId()
            + "&redirect_uri=" + qyWeChatProperties.getOaWebHost() + "/login" + redirect
            + "&response_type=code&scope=snsapi_privateinfo"
            + "&state=" + redirect
            + "&agentid=" + qyWeChatProperties.getAgentId() + "#wechat_redirect";

    }

    /**
     * 构建企业微信OAuth2授权URL
     *
     * @param redirectUri 重定向URI
     * @param state 状态参数
     * @return 授权URL
     */
    public String buildOAuth2Url(String redirectUri, String state) {
        String finalRedirectUri = StringUtils.hasText(redirectUri) ? redirectUri : qyWeChatProperties.getRedirectUri();
        
        if (!StringUtils.hasText(finalRedirectUri)) {
            throw new IllegalArgumentException("重定向URI不能为空");
        }

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append("https://open.weixin.qq.com/connect/oauth2/authorize");
        urlBuilder.append("?appid=").append(qyWeChatProperties.getCorpId());
        urlBuilder.append("&redirect_uri=").append(finalRedirectUri);
        urlBuilder.append("&response_type=code");
        urlBuilder.append("&scope=snsapi_base");
        
        if (StringUtils.hasText(state)) {
            urlBuilder.append("&state=").append(state);
        }
        
        urlBuilder.append("&agentid=").append(qyWeChatProperties.getAgentId());
        urlBuilder.append("#wechat_redirect");

        String oauthUrl = urlBuilder.toString();
        log.debug("构建企业微信OAuth2授权URL: {}", oauthUrl);
        
        return oauthUrl;
    }

    /**
     * 清除访问令牌缓存
     */
    public void clearAccessTokenCache() {
        String cacheKey = qyWeChatProperties.getTokenCache().getKeyPrefix() + qyWeChatProperties.getCorpId();
        stringRedisTemplate.delete(cacheKey);
        log.info("已清除企业微信访问令牌缓存");
    }

    /**
     * 检查企业微信配置是否有效
     *
     * @return 配置是否有效
     */
    public boolean isConfigValid() {
        return qyWeChatProperties.isEnabled()
                && StringUtils.hasText(qyWeChatProperties.getCorpId())
                && StringUtils.hasText(qyWeChatProperties.getCorpSecret())
                && StringUtils.hasText(qyWeChatProperties.getAgentId());
    }

    /**
     * 根据用户票据获取用户详细信息
     *
     * @param userTicket 用户票据
     * @return 用户详细信息
     */
    public UserDetailDTO getUserDetail(String userTicket) {
        if (!StringUtils.hasText(userTicket)) {
            throw new IllegalArgumentException("用户票据不能为空");
        }

        // 1. 获取访问令牌
        String accessToken = getAccessToken();

        // 2. 调用企业微信API获取用户详细信息
        String url = String.format("%s/user/getuserdetail?access_token=%s",
                qyWeChatProperties.getQyWechatHost(),
                accessToken);

        try {
            log.debug("调用企业微信API获取用户详细信息: url={}", url);

            // 构建请求实体
            String requestBody = QyWechatHttpUtil.buildUserTicketRequest(userTicket);
            HttpEntity<String> requestEntity = QyWechatHttpUtil.createJsonRequest(requestBody);

            ResponseEntity<UserDetailDTO> response = restTemplate.postForEntity(url, requestEntity, UserDetailDTO.class);
            UserDetailDTO userDetailResponse = response.getBody();

            if (Objects.isNull(userDetailResponse)) {
                log.error("获取企业微信用户详细信息失败：响应为空");
                throw new RuntimeException("获取企业微信用户详细信息失败：响应为空");
            }

            if (!userDetailResponse.isSuccess()) {
                log.error("获取企业微信用户详细信息失败：{}", userDetailResponse.getErrorMessage());
                throw new RuntimeException("获取企业微信用户详细信息失败：" + userDetailResponse.getErrorMessage());
            }

            log.info("成功获取企业微信用户详细信息: userId={}", userDetailResponse.getUserId());

            return userDetailResponse;

        } catch (Exception e) {
            log.error("获取企业微信用户详细信息异常: userTicket={}", userTicket, e);
            throw new RuntimeException("获取企业微信用户详细信息异常：" + e.getMessage(), e);
        }
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    public QyUserInfoDTO getUser(String userId) {
        if (!StringUtils.hasText(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        // 1. 获取访问令牌
        String accessToken = getAccessToken();

        // 2. 调用企业微信API获取用户信息
        String url = String.format("%s/user/get?access_token=%s&userid=%s",
                qyWeChatProperties.getQyWechatHost(),
                accessToken,
                userId);

        try {
            log.debug("调用企业微信API获取用户信息: url={}", url);

            ResponseEntity<QyUserInfoDTO> response = restTemplate.getForEntity(url, QyUserInfoDTO.class);
            QyUserInfoDTO userInfoResponse = response.getBody();

            if (Objects.isNull(userInfoResponse)) {
                log.error("获取企业微信用户信息失败：响应为空");
                throw new RuntimeException("获取企业微信用户信息失败：响应为空");
            }

            if (!userInfoResponse.isSuccess()) {
                log.error("获取企业微信用户信息失败：{}", userInfoResponse.getErrorMessage());
                throw new RuntimeException("获取企业微信用户信息失败：" + userInfoResponse.getErrorMessage());
            }

            log.info("成功获取企业微信用户信息: userId={}, name={}",
                userInfoResponse.getUserId(), userInfoResponse.getName());

            return userInfoResponse;

        } catch (Exception e) {
            log.error("获取企业微信用户信息异常: userId={}", userId, e);
            throw new RuntimeException("获取企业微信用户信息异常：" + e.getMessage(), e);
        }
    }

    /**
     * userid转openid
     *
     * @param userId 用户ID
     * @return OpenId信息
     */
    public OpenIdDTO convertToOpenId(String userId) {
        if (!StringUtils.hasText(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        // 1. 获取访问令牌
        String accessToken = getAccessToken();

        // 2. 调用企业微信API转换OpenId
        String url = String.format("%s/user/convert_to_openid?access_token=%s",
                qyWeChatProperties.getQyWechatHost(),
                accessToken);

        try {
            log.debug("调用企业微信API转换OpenId: url={}", url);

            // 构建请求实体
            String requestBody = QyWechatHttpUtil.buildUserIdRequest(userId);
            HttpEntity<String> requestEntity = QyWechatHttpUtil.createJsonRequest(requestBody);

            ResponseEntity<OpenIdDTO> response = restTemplate.postForEntity(url, requestEntity, OpenIdDTO.class);
            OpenIdDTO openIdResponse = response.getBody();

            if (Objects.isNull(openIdResponse)) {
                log.error("转换OpenId失败：响应为空");
                throw new RuntimeException("转换OpenId失败：响应为空");
            }

            if (!openIdResponse.isSuccess()) {
                log.error("转换OpenId失败：{}", openIdResponse.getErrorMessage());
                throw new RuntimeException("转换OpenId失败：" + openIdResponse.getErrorMessage());
            }

            log.info("成功转换OpenId: userId={}, openId={}", userId, openIdResponse.getOpenId());

            return openIdResponse;

        } catch (Exception e) {
            log.error("转换OpenId异常: userId={}", userId, e);
            throw new RuntimeException("转换OpenId异常：" + e.getMessage(), e);
        }
    }



    /**
     * 获取访问用户身份（网页授权）
     *
     * @param code 授权码
     * @return 授权登录信息
     */
    public AuthLoginDTO getAuthLogin(String code) {
        if (!StringUtils.hasText(code)) {
            throw new IllegalArgumentException("授权码不能为空");
        }

        // 1. 获取访问令牌
        String accessToken = getAccessToken();

        // 2. 调用企业微信API获取访问用户身份
        String url = String.format("%s/auth/getuserinfo?access_token=%s&code=%s",
                qyWeChatProperties.getQyWechatHost(),
                accessToken,
                code);

        try {
            log.debug("调用企业微信API获取访问用户身份: url={}", url);

            ResponseEntity<AuthLoginDTO> response = restTemplate.getForEntity(url, AuthLoginDTO.class);
            AuthLoginDTO authLoginResponse = response.getBody();

            if (Objects.isNull(authLoginResponse)) {
                log.error("获取访问用户身份失败：响应为空");
                throw new RuntimeException("获取访问用户身份失败：响应为空");
            }

            if (!authLoginResponse.isSuccess()) {
                log.error("获取访问用户身份失败：{}", authLoginResponse.getErrorMessage());
                throw new RuntimeException("获取访问用户身份失败：" + authLoginResponse.getErrorMessage());
            }

            log.info("成功获取访问用户身份: userId={}", authLoginResponse.getUserId());

            return authLoginResponse;

        } catch (Exception e) {
            log.error("获取访问用户身份异常: code={}", code, e);
            throw new RuntimeException("获取访问用户身份异常：" + e.getMessage(), e);
        }
    }

    /**
     * 根据部门id获取员工列表
     *
     * @param departmentId 部门ID
     * @return 员工列表
     */
    public List<UserlistDTO> getDepartmentStaffList(int departmentId) {
        // 1. 获取访问令牌
        String accessToken = getAccessToken();

        // 2. 构建URL
        String url;
        url = String.format("%s/user/simplelist?access_token=%s&department_id=%d",
                qyWeChatProperties.getQyWechatHost(),
                URLEncoder.encode(accessToken, StandardCharsets.UTF_8),
                departmentId);

        try {
            log.debug("调用企业微信API获取部门员工列表: url={}", url);

            ResponseEntity<DepartmentStaffListResponseDTO> response = restTemplate.getForEntity(url, DepartmentStaffListResponseDTO.class);
            DepartmentStaffListResponseDTO staffListResponse = response.getBody();

            if (Objects.isNull(staffListResponse)) {
                log.error("获取部门员工列表失败：响应为空");
                return Collections.emptyList();
            }

            if (!staffListResponse.isSuccess()) {
                log.error("获取部门员工列表失败：{}", staffListResponse.getErrorMessage());
                return Collections.emptyList();
            }

            List<UserlistDTO> userList = staffListResponse.getUserlist();
            log.info("成功获取部门员工列表: departmentId={}, userCount={}",
                departmentId, userList != null ? userList.size() : 0);

            return userList != null ? userList : Collections.emptyList();

        } catch (Exception e) {
            log.error("获取部门员工列表异常: departmentId={}", departmentId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取部门列表
     *
     * @param departmentId 部门ID，获取指定部门及其下的子部门（以及子部门的子部门等等，递归）。如果不填，默认获取全量组织架构
     * @return 部门列表
     */
    public List<DepartmentDTO> getDepartmentList(Integer departmentId) {
        // 1. 获取访问令牌
        String accessToken = getAccessToken();

        // 2. 构建URL
        String url;
        if (departmentId != null) {
            url = String.format("%s/department/list?access_token=%s&id=%d",
                    qyWeChatProperties.getQyWechatHost(),
                    URLEncoder.encode(accessToken, StandardCharsets.UTF_8),
                    departmentId);
        } else {
            url = String.format("%s/department/list?access_token=%s",
                    qyWeChatProperties.getQyWechatHost(),
                    URLEncoder.encode(accessToken, StandardCharsets.UTF_8));
        }

        try {
            log.debug("调用企业微信API获取部门列表: url={}", url);

            ResponseEntity<DepartmentResponseDTO> response = restTemplate.getForEntity(url, DepartmentResponseDTO.class);
            DepartmentResponseDTO departmentResponse = response.getBody();

            if (Objects.isNull(departmentResponse)) {
                log.error("获取部门列表失败：响应为空");
                return Collections.emptyList();
            }

            if (!departmentResponse.isSuccess()) {
                log.error("获取部门列表失败：{}", departmentResponse.getErrorMessage());
                return Collections.emptyList();
            }

            List<DepartmentDTO> departmentList = departmentResponse.getDepartment();
            log.info("成功获取部门列表: departmentId={}, departmentCount={}",
                departmentId, departmentList != null ? departmentList.size() : 0);

            return departmentList != null ? departmentList : Collections.emptyList();

        } catch (Exception e) {
            log.error("获取部门列表异常: departmentId={}", departmentId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取全量部门列表
     *
     * @return 全量部门列表
     */
    public List<DepartmentDTO> getAllDepartments() {
        return getDepartmentList(null);
    }
}

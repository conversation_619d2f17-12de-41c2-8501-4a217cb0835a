package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 企业微信部门员工列表响应DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DepartmentStaffListResponseDTO extends QyWeChatResponse<Void> {
    
    /**
     * 成员列表
     */
    @JsonProperty("userlist")
    private List<UserlistDTO> userlist;
}

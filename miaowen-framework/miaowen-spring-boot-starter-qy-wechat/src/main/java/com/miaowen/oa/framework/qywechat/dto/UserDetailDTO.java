package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 企业微信用户详情DTO
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class UserDetailDTO extends QyWeChatResponse<Void> {
    
    /**
     * 用户ID
     */
    @JsonProperty("userid")
    private String userId;
    
    /**
     * 性别。1表示男性，2表示女性
     */
    @JsonProperty("gender")
    private String gender;
    
    /**
     * 头像url
     */
    @JsonProperty("avatar")
    private String avatar;
    
    /**
     * 员工个人二维码
     */
    @JsonProperty("qr_code")
    private String qrCode;
    
    /**
     * 手机号码
     */
    @JsonProperty("mobile")
    private String mobile;
    
    /**
     * 邮箱
     */
    @JsonProperty("email")
    private String email;
    
    /**
     * 企业邮箱
     */
    @JsonProperty("biz_mail")
    private String bizMail;
    
    /**
     * 地址
     */
    @JsonProperty("address")
    private String address;
}

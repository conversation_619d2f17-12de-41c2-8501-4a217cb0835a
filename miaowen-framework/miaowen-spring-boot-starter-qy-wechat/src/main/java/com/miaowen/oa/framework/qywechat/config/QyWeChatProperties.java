package com.miaowen.oa.framework.qywechat.config;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;


/**
 * 企业微信配置属性
 *
 * <AUTHOR>
 */
@Data
@Validated
@ConfigurationProperties(prefix = "miaowen.qy-wechat")
public class QyWeChatProperties {
    
    /**
     * 是否启用企业微信功能
     */
    private boolean enabled = true;
    
    /**
     * 企业ID
     */
//    @NotBlank(message = "企业ID不能为空")
    private String corpId;
    
    /**
     * 企业应用的凭证密钥
     */
//    @NotBlank(message = "企业应用凭证密钥不能为空")
    private String corpSecret;
    
    /**
     * 企业应用的id
     */
//    @NotBlank(message = "企业应用ID不能为空")
    private String agentId;
    
    /**
     * 企业微信API地址
     */
    private String qyWechatHost = "https://qyapi.weixin.qq.com/cgi-bin";
    
    /**
     * 重定向URI
     */
    private String redirectUri;
    
    /**
     * OA Web主机地址
     */
    private String oaWebHost;

    
    /**
     * 回调验证Token
     */
    private String token;
    
    /**
     * 回调消息加解密密钥
     */
    private String encodingAesKey;
    
    /**
     * Token缓存配置
     */
    private TokenCache tokenCache = new TokenCache();

    /**
     * HTTP客户端配置
     */
    private HttpClient httpClient = new HttpClient();
    
    /**
     * Token缓存配置
     */
    @Data
    public static class TokenCache {
        /**
         * Token缓存键前缀
         */
        private String keyPrefix = "qy_wechat_token_";

        /**
         * Token缓存过期时间偏移量（秒），用于提前刷新Token
         */
        private long expireOffset = 60;
    }

    /**
     * HTTP客户端配置
     */
    @Data
    public static class HttpClient {
        /**
         * 连接超时时间（秒）
         */
        private int connectTimeout = 10;

        /**
         * 读取超时时间（秒）
         */
        private int readTimeout = 30;

        /**
         * 最大重试次数
         */
        private int maxRetries = 3;

        /**
         * 是否启用请求日志
         */
        private boolean enableRequestLogging = true;
    }
}

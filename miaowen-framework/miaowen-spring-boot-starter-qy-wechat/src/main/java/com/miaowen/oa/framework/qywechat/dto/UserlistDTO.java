package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 企业微信用户列表DTO
 *
 * <AUTHOR>
 */
@Data
public class UserlistDTO {
    
    /**
     * 成员UserID
     */
    @JsonProperty("userid")
    private String userid;
    
    /**
     * 成员名称
     */
    @JsonProperty("name")
    private String name;
    
    /**
     * 成员所属部门id列表
     */
    @JsonProperty("department")
    private List<Integer> department;
    
    /**
     * 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的
     */
    @JsonProperty("open_userid")
    private String openUserid;
}

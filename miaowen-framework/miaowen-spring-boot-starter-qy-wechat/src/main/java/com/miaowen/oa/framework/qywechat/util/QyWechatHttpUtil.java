package com.miaowen.oa.framework.qywechat.util;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * 企业微信HTTP请求工具类
 *
 * <AUTHOR>
 */
public class QyWechatHttpUtil {

    /**
     * 创建JSON请求实体
     *
     * @param requestBody 请求体JSON字符串
     * @return HTTP请求实体
     */
    public static HttpEntity<String> createJsonRequest(String requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return new HttpEntity<>(requestBody, headers);
    }

    /**
     * 构建用户票据请求体
     *
     * @param userTicket 用户票据
     * @return JSON请求体
     */
    public static String buildUserTicketRequest(String userTicket) {
        return String.format("{\"user_ticket\":\"%s\"}", userTicket);
    }

    /**
     * 构建用户ID请求体
     *
     * @param userId 用户ID
     * @return JSON请求体
     */
    public static String buildUserIdRequest(String userId) {
        return String.format("{\"userid\":\"%s\"}", userId);
    }
}

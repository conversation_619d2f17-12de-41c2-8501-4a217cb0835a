package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业微信API响应基类
 *
 * <AUTHOR>
 */
@Data
public class QyWeChatResponse<T> {
    
    /**
     * 返回码
     */
    @JsonProperty("errcode")
    private Integer errCode;
    
    /**
     * 对返回码的文本描述内容
     */
    @JsonProperty("errmsg")
    private String errMsg;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 判断请求是否成功
     */
    public boolean isSuccess() {
        return errCode != null && errCode == 0;
    }
    
    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return String.format("错误码: %d, 错误信息: %s", errCode, errMsg);
    }
}

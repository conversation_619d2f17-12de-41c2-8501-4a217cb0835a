package com.miaowen.oa.framework.qywechat.config;

import com.miaowen.oa.framework.qywechat.core.QyWechatOpenClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

/**
 * 企业微信自动配置类
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@ConditionalOnClass({QyWechatOpenClient.class, RestTemplate.class, StringRedisTemplate.class})
@ConditionalOnProperty(prefix = "miaowen.qy-wechat", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(QyWeChatProperties.class)
public class QyWeChatAutoConfiguration {

    /**
     * 配置企业微信开放平台客户端Bean
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean(RestTemplate.class)
    public QyWechatOpenClient qyWechatOpenClient(QyWeChatProperties qyWeChatProperties,
                                                 StringRedisTemplate stringRedisTemplate,
                                                 RestTemplate restTemplate) {
        log.info("创建QyWechatOpenClient Bean");
        log.debug("企业微信配置: corpId={}, agentId={}, enabled={}",
            qyWeChatProperties.getCorpId(),
            qyWeChatProperties.getAgentId(),
            qyWeChatProperties.isEnabled());

        return new QyWechatOpenClient(qyWeChatProperties, stringRedisTemplate, restTemplate);
    }
}

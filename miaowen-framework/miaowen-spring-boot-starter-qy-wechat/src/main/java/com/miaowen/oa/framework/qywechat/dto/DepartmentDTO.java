package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 企业微信部门信息DTO
 *
 * <AUTHOR>
 */
@Data
public class DepartmentDTO {
    
    /**
     * 部门id
     */
    @JsonProperty("id")
    private Integer id;
    
    /**
     * 部门名称
     */
    @JsonProperty("name")
    private String name;
    
    /**
     * 英文名称
     */
    @JsonProperty("name_en")
    private String nameEn;
    
    /**
     * 部门负责人的UserID
     */
    @JsonProperty("department_leader")
    private List<String> departmentLeader;
    
    /**
     * 父部门id。根部门为1
     */
    @JsonProperty("parentid")
    private Integer parentid;
    
    /**
     * 在父部门中的次序值。order值大的排序靠前
     */
    @JsonProperty("order")
    private Integer order;
}

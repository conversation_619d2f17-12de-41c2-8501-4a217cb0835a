package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 企业微信用户详细信息DTO
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class QyUserInfoDTO extends QyWeChatResponse<Void> {

    /**
     * 用户ID
     */
    @JsonProperty("userid")
    private String userId;
    
    /**
     * 用户姓名
     */
    @JsonProperty("name")
    private String name;
    
    /**
     * 部门列表
     */
    @JsonProperty("department")
    private List<Integer> department;
    
    /**
     * 部门内的排序值
     */
    @JsonProperty("order")
    private List<Integer> order;
    
    /**
     * 职务信息
     */
    @JsonProperty("position")
    private String position;
    
    /**
     * 手机号码
     */
    @JsonProperty("mobile")
    private String mobile;
    
    /**
     * 性别。1表示男性，2表示女性
     */
    @JsonProperty("gender")
    private String gender;
    
    /**
     * 邮箱
     */
    @JsonProperty("email")
    private String email;
    
    /**
     * 企业邮箱
     */
    @JsonProperty("biz_mail")
    private String bizMail;
    
    /**
     * 表示在所在的部门内是否为部门负责人
     */
    @JsonProperty("is_leader_in_dept")
    private List<Integer> isLeaderInDept;
    
    /**
     * 直属上级UserID
     */
    @JsonProperty("direct_leader")
    private List<String> directLeader;
    
    /**
     * 头像url
     */
    @JsonProperty("avatar")
    private String avatar;
    
    /**
     * 头像缩略图url
     */
    @JsonProperty("thumb_avatar")
    private String thumbAvatar;
    
    /**
     * 座机
     */
    @JsonProperty("telephone")
    private String telephone;
    
    /**
     * 别名
     */
    @JsonProperty("alias")
    private String alias;
    
    /**
     * 地址
     */
    @JsonProperty("address")
    private String address;
    
    /**
     * 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的
     */
    @JsonProperty("open_userid")
    private String openUserId;
    
    /**
     * 主部门
     */
    @JsonProperty("main_department")
    private Integer mainDepartment;
    
    /**
     * 扩展属性
     */
    @JsonProperty("extattr")
    private ExtattrDTO extattr;
    
    /**
     * 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
     */
    @JsonProperty("status")
    private Integer status;
    
    /**
     * 员工个人二维码
     */
    @JsonProperty("qr_code")
    private String qrCode;
    
    /**
     * 对外职务
     */
    @JsonProperty("external_position")
    private String externalPosition;
    
    /**
     * 成员对外属性
     */
    @JsonProperty("external_profile")
    private ExternalProfileDTO externalProfile;

    /**
     * 扩展属性
     */
    @NoArgsConstructor
    @Data
    public static class ExtattrDTO {
        @JsonProperty("attrs")
        private List<AttrsDTO> attrs;

        @NoArgsConstructor
        @Data
        public static class AttrsDTO {
            @JsonProperty("type")
            private Integer type;
            
            @JsonProperty("name")
            private String name;
            
            @JsonProperty("text")
            private TextDTO text;
            
            @JsonProperty("web")
            private WebDTO web;

            @NoArgsConstructor
            @Data
            public static class TextDTO {
                @JsonProperty("value")
                private String value;
            }

            @NoArgsConstructor
            @Data
            public static class WebDTO {
                @JsonProperty("url")
                private String url;
                
                @JsonProperty("title")
                private String title;
            }
        }
    }

    /**
     * 对外属性
     */
    @NoArgsConstructor
    @Data
    public static class ExternalProfileDTO {
        @JsonProperty("external_corp_name")
        private String externalCorpName;
        
        @JsonProperty("wechat_channels")
        private WechatChannelsDTO wechatChannels;
        
        @JsonProperty("external_attr")
        private List<ExternalAttrDTO> externalAttr;

        @NoArgsConstructor
        @Data
        public static class WechatChannelsDTO {
            @JsonProperty("nickname")
            private String nickname;
            
            @JsonProperty("status")
            private Integer status;
        }

        @NoArgsConstructor
        @Data
        public static class ExternalAttrDTO {
            @JsonProperty("type")
            private Integer type;
            
            @JsonProperty("name")
            private String name;
            
            @JsonProperty("text")
            private TextDTO text;
            
            @JsonProperty("web")
            private WebDTO web;
            
            @JsonProperty("miniprogram")
            private MiniprogramDTO miniprogram;

            @NoArgsConstructor
            @Data
            public static class TextDTO {
                @JsonProperty("value")
                private String value;
            }

            @NoArgsConstructor
            @Data
            public static class WebDTO {
                @JsonProperty("url")
                private String url;
                
                @JsonProperty("title")
                private String title;
            }

            @NoArgsConstructor
            @Data
            public static class MiniprogramDTO {
                @JsonProperty("appid")
                private String appId;
                
                @JsonProperty("pagepath")
                private String pagePath;
                
                @JsonProperty("title")
                private String title;
            }
        }
    }
}

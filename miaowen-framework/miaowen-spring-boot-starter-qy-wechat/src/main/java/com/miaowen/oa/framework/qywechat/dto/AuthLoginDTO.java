package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业微信授权登录响应DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AuthLoginDTO extends QyWeChatResponse<Void> {
    
    /**
     * 用户ID
     */
    @JsonProperty("userid")
    private String userId;
    
    /**
     * 用户票据
     */
    @JsonProperty("user_ticket")
    private String userTicket;
}

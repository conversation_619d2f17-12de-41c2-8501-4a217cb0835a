package com.miaowen.oa.framework.qywechat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业微信用户信息响应DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserInfoResponseDTO extends QyWeChatResponse<Void> {
    
    /**
     * 用户所属企业的corpid
     */
    @JsonProperty("CorpId")
    private String corpId;
    
    /**
     * 用户在企业内的UserID，如果该企业与第三方应用有授权关系时，返回明文UserId，否则返回OpenId
     */
    @JsonProperty("UserId")
    private String userId;
    
    /**
     * 非企业成员的标识，对当前企业唯一。不超过64字节
     */
    @JsonProperty("OpenId")
    private String openId;
    
    /**
     * 手机设备号(由企业微信在安装时随机生成，删除重装会改变，升级不受影响)
     */
    @JsonProperty("DeviceId")
    private String deviceId;
    
    /**
     * 外部联系人id，当且仅当用户是企业的客户，且跟进人在应用的可见范围内时，才会返回
     */
    @JsonProperty("external_userid")
    private String externalUserId;
}

{"groups": [{"name": "miaowen.qy-wechat", "type": "com.miaowen.oa.framework.qywechat.config.QyWeChatProperties", "description": "企业微信配置属性"}, {"name": "miaowen.qy-wechat.token-cache", "type": "com.miaowen.oa.framework.qywechat.config.QyWeChatProperties$TokenCache", "description": "企业微信Token缓存配置"}, {"name": "miaowen.qy-wechat.http-client", "type": "com.miaowen.oa.framework.qywechat.config.QyWeChatProperties$HttpClient", "description": "企业微信HTTP客户端配置"}], "properties": [{"name": "miaowen.qy-wechat.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用企业微信功能", "defaultValue": true}, {"name": "miaowen.qy-wechat.corp-id", "type": "java.lang.String", "description": "企业ID"}, {"name": "miaowen.qy-wechat.corp-secret", "type": "java.lang.String", "description": "企业应用的凭证密钥"}, {"name": "miaowen.qy-wechat.agent-id", "type": "java.lang.String", "description": "企业应用的id"}, {"name": "miaowen.qy-wechat.qy-wechat-host", "type": "java.lang.String", "description": "企业微信API地址", "defaultValue": "https://qyapi.weixin.qq.com/cgi-bin"}, {"name": "miaowen.qy-wechat.redirect-uri", "type": "java.lang.String", "description": "重定向URI"}, {"name": "miaowen.qy-wechat.oa-web-host", "type": "java.lang.String", "description": "OA Web主机地址"}, {"name": "miaowen.qy-wechat.pull-qy-staff", "type": "java.lang.Bo<PERSON>an", "description": "是否拉取企业微信员工信息", "defaultValue": true}, {"name": "miaowen.qy-wechat.token", "type": "java.lang.String", "description": "回调验证Token"}, {"name": "miaowen.qy-wechat.encoding-aes-key", "type": "java.lang.String", "description": "回调消息加解密密钥"}, {"name": "miaowen.qy-wechat.token-cache.key-prefix", "type": "java.lang.String", "description": "Token缓存键前缀", "defaultValue": "qy_wechat_token_"}, {"name": "miaowen.qy-wechat.token-cache.expire-offset", "type": "java.lang.Long", "description": "Token缓存过期时间偏移量（秒），用于提前刷新Token", "defaultValue": 60}, {"name": "miaowen.qy-wechat.http-client.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（秒）", "defaultValue": 10}, {"name": "miaowen.qy-wechat.http-client.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（秒）", "defaultValue": 30}, {"name": "miaowen.qy-wechat.http-client.max-retries", "type": "java.lang.Integer", "description": "最大重试次数", "defaultValue": 3}, {"name": "miaowen.qy-wechat.http-client.enable-request-logging", "type": "java.lang.Bo<PERSON>an", "description": "是否启用请求日志", "defaultValue": false}]}
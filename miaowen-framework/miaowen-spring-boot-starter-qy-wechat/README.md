# miaowen-spring-boot-starter-qy-wechat

企业微信集成Spring Boot Starter，提供企业微信OAuth2登录和API调用功能。

## 功能特性

- ✅ 企业微信OAuth2授权登录
- ✅ 企业微信用户信息获取
- ✅ 访问令牌自动管理和缓存
- ✅ 自动配置和Bean注册
- ✅ 完整的配置属性支持
- ✅ 线程安全的服务实现

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.miaowen.oa</groupId>
    <artifactId>miaowen-spring-boot-starter-qy-wechat</artifactId>
    <version>${miaowen.version}</version>
</dependency>
```

### 2. 配置属性

在 `application.yml` 中添加企业微信配置：

```yaml
miaowen:
  qy-wechat:
    enabled: true
    corp-id: your-corp-id
    corp-secret: your-corp-secret
    agent-id: your-agent-id
    redirect-uri: http://your-domain.com/callback
    oa-web-host: http://your-oa-web.com
    pull-qy-staff: true
    token-cache:
      key-prefix: qy_wechat_token_
      expire-offset: 60
    http-client:
      connect-timeout: 10
      read-timeout: 30
      max-retries: 3
      enable-request-logging: true
```

### 3. 配置RestTemplate

```java
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
```

### 4. 使用服务

```java
@RestController
@RequiredArgsConstructor
public class QyWeChatController {

    private final QyWechatOpenClient qyWechatOpenClient;

    /**
     * 构建授权URL
     */
    @GetMapping("/auth/url")
    public String getAuthUrl() {
        return qyWechatOpenClient.buildOAuth2Url(null, "state123");
    }

    /**
     * 处理授权回调
     */
    @GetMapping("/auth/callback")
    public UserInfoResponseDTO handleCallback(@RequestParam String code) {
        return qyWechatOpenClient.getUserInfo(code);
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/user/{userId}")
    public QyUserInfoDTO getUserInfo(@PathVariable String userId) {
        return qyWechatOpenClient.getUser(userId);
    }

    /**
     * 获取部门员工列表
     */
    @GetMapping("/department/{departmentId}/staff")
    public List<UserlistDTO> getDepartmentStaff(@PathVariable int departmentId) {
        return qyWechatOpenClient.getDepartmentStaffList(departmentId);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/departments")
    public List<DepartmentDTO> getDepartments(@RequestParam(required = false) Integer departmentId) {
        return qyWechatOpenClient.getDepartmentList(departmentId);
    }
}
```

## 配置属性

| 属性名 | 类型 | 默认值                                 | 描述 |
|--------|------|-------------------------------------|------|
| `miaowen.qy-wechat.enabled` | Boolean | true                                | 是否启用企业微信功能 |
| `miaowen.qy-wechat.corp-id` | String | -                                   | 企业ID（必填） |
| `miaowen.qy-wechat.corp-secret` | String | -                                   | 企业应用凭证密钥（必填） |
| `miaowen.qy-wechat.agent-id` | String | -                                   | 企业应用ID（必填） |
| `miaowen.qy-wechat.qy-wechat-host` | String | https://qyapi.weixin.qq.com/cgi-bin | 企业微信API地址 |
| `miaowen.qy-wechat.redirect-uri` | String | -                                   | OAuth2重定向URI |
| `miaowen.qy-wechat.oa-web-host` | String | -                                   | OA Web主机地址 |
| `miaowen.qy-wechat.pull-qy-staff` | Boolean | true                                | 是否拉取企业微信员工信息 |
| `miaowen.qy-wechat.token` | String | -                                   | 回调验证Token |
| `miaowen.qy-wechat.encoding-aes-key` | String | -                                   | 回调消息加解密密钥 |
| `miaowen.qy-wechat.token-cache.key-prefix` | String | qy_wechat_token_                    | Token缓存键前缀 |
| `miaowen.qy-wechat.token-cache.expire-offset` | Long | 60                                  | Token缓存过期偏移量（秒） |
| `miaowen.qy-wechat.http-client.connect-timeout` | Integer | 10                                  | 连接超时时间（秒） |
| `miaowen.qy-wechat.http-client.read-timeout` | Integer | 30                                  | 读取超时时间（秒） |
| `miaowen.qy-wechat.http-client.max-retries` | Integer | 3                                   | 最大重试次数 |
| `miaowen.qy-wechat.http-client.enable-request-logging` | Boolean | true                                | 是否启用请求日志 |

## API说明

### QyWechatOpenClient

企业微信开放平台客户端，提供以下方法：

#### getAccessToken()
获取企业微信访问令牌，支持自动缓存和刷新。

```java
String accessToken = qyWechatOpenClient.getAccessToken();
```

#### getUserInfo(String code)
根据授权码获取用户信息。

```java
UserInfoResponseDTO userInfo = qyWechatOpenClient.getUserInfo(code);
```

#### getUser(String userId)
根据用户ID获取用户详细信息。

```java
QyUserInfoDTO userInfo = qyWechatOpenClient.getUser(userId);
```

#### getUserDetail(String userTicket)
根据用户票据获取用户详细信息。

```java
UserDetailDTO userDetail = qyWechatOpenClient.getUserDetail(userTicket);
```

#### getAuthLogin(String code)
获取访问用户身份（网页授权）。

```java
AuthLoginDTO authLogin = qyWechatOpenClient.getAuthLogin(code);
```

#### convertToOpenId(String userId)
将用户ID转换为OpenId。

```java
OpenIdDTO openId = qyWechatOpenClient.convertToOpenId(userId);
```

#### buildOAuth2Url(String redirectUri, String state)
构建企业微信OAuth2授权URL。

```java
String authUrl = qyWechatOpenClient.buildOAuth2Url("http://callback.com", "state");
```

#### clearAccessTokenCache()
清除访问令牌缓存。

```java
qyWechatOpenClient.clearAccessTokenCache();
```

#### getDepartmentStaffList(int departmentId)
根据部门ID获取员工列表。

```java
List<UserlistDTO> staffList = qyWechatOpenClient.getDepartmentStaffList(1);
```

#### getDepartmentList(Integer departmentId)
获取部门列表，支持获取指定部门的子部门。

```java
// 获取指定部门及其子部门
List<DepartmentDTO> departments = qyWechatOpenClient.getDepartmentList(1);

// 获取全量部门列表
List<DepartmentDTO> allDepartments = qyWechatOpenClient.getAllDepartments();
```

#### isConfigValid()
检查企业微信配置是否有效。

```java
boolean isValid = qyWechatOpenClient.isConfigValid();
```

## 自动配置

该Starter提供自动配置功能，会自动注册以下Bean：

- `QyWeChatProperties` - 配置属性Bean
- `QyWechatOpenClient` - 企业微信客户端Bean（需要RestTemplate Bean存在）

**注意**: 需要在项目中配置RestTemplate Bean，企业微信客户端才会自动创建。

## 依赖要求

- Spring Boot 2.7+
- Spring Data Redis
- RestTemplate Bean（需要在项目中配置）
- Jackson

## 注意事项

1. **RestTemplate依赖**：该Starter需要项目中存在RestTemplate Bean才会自动创建企业微信客户端。

2. **Redis依赖**：该Starter依赖Redis来缓存访问令牌，请确保项目中已配置Redis。

3. **网络访问**：需要确保应用服务器能够访问企业微信API（`https://qyapi.weixin.qq.com`）。

4. **配置安全**：企业微信的`corp-secret`是敏感信息，建议使用环境变量或加密配置。

5. **令牌缓存**：访问令牌会自动缓存在Redis中，默认提前60秒过期以避免令牌失效。

## 示例项目

完整的使用示例请参考UAA模块中的企业微信登录实现。

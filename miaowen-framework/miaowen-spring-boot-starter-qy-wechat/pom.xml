<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.miaowen</groupId>
        <artifactId>miaowen-framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>miaowen-spring-boot-starter-qy-wechat</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>企业微信集成Spring Boot Starter</description>

    <dependencies>
        <!-- Spring Boot Configuration Processor -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Web (for RestTemplate) -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <!-- Spring Boot Starter Data Redis -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Miaowen Common -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-common</artifactId>
        </dependency>


        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

    </dependencies>

</project>

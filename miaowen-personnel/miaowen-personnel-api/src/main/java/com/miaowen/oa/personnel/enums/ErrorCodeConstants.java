package com.miaowen.oa.personnel.enums;

import com.miaowen.oa.framework.common.exception.ErrorCode;

/**
 * Personnel 错误码枚举类
 *
 * personnel 系统，使用 1-006-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 档案模块 1-006-001-000 ==========
    ErrorCode ARCHIVE_NOT_EXISTS = new ErrorCode(1_006_001_000, "档案不存在");
    ErrorCode ARCHIVE_USER_EXISTS = new ErrorCode(1_006_001_001, "用户已有档案，不能重复创建");
    ErrorCode ARCHIVE_NUMBER_EXISTS = new ErrorCode(1_006_001_002, "档案编号已存在");
    ErrorCode ARCHIVE_STATE_NOT_ALLOW_UPDATE = new ErrorCode(1_006_001_003, "当前档案状态不允许更新");
    ErrorCode ARCHIVE_STATE_NOT_ALLOW_DELETE = new ErrorCode(1_006_001_004, "当前档案状态不允许删除");
    ErrorCode ARCHIVE_HAS_RELATED_DATA = new ErrorCode(1_006_001_005, "档案存在关联数据，不能删除");
    ErrorCode ARCHIVE_BUSINESS_RULE_ERROR = new ErrorCode(1_006_001_006, "档案数据不符合业务规则：{}");
    ErrorCode ARCHIVE_STATE_TRANSITION_ERROR = new ErrorCode(1_006_001_007, "档案状态转换错误，当前状态{}不能转换为{}");
    ErrorCode ARCHIVE_SUBMIT_AUDIT_ERROR = new ErrorCode(1_006_001_008, "提交审核失败，档案状态必须为草稿");
    ErrorCode ARCHIVE_APPROVE_ERROR = new ErrorCode(1_006_001_009, "审核通过失败，档案状态必须为待审核");
    ErrorCode ARCHIVE_REJECT_ERROR = new ErrorCode(1_006_001_010, "审核拒绝失败，档案状态必须为待审核");
    ErrorCode ARCHIVE_ARCHIVE_ERROR = new ErrorCode(1_006_001_011, "归档失败，档案状态必须为已审核");
    ErrorCode ARCHIVE_LOCK_ERROR = new ErrorCode(1_006_001_012, "锁定失败，档案状态必须为已审核或已归档");
    ErrorCode ARCHIVE_UNLOCK_ERROR = new ErrorCode(1_006_001_013, "解锁失败，档案状态必须为已锁定");
    ErrorCode ARCHIVE_IMPORT_ERROR = new ErrorCode(1_006_001_014, "档案导入失败：{}");
    ErrorCode ARCHIVE_EXPORT_ERROR = new ErrorCode(1_006_001_015, "档案导出失败：{}");
    ErrorCode ARCHIVE_GENERATE_NUMBER_ERROR = new ErrorCode(1_006_001_016, "档案编号生成失败");
    
    // ========== 教育经历模块 1-006-002-000 ==========
    ErrorCode EDUCATION_NOT_EXISTS = new ErrorCode(1_006_002_000, "教育经历不存在");
    ErrorCode EDUCATION_ARCHIVE_NOT_EXISTS = new ErrorCode(1_006_002_001, "教育经历关联的档案不存在");
    ErrorCode EDUCATION_DATE_INVALID = new ErrorCode(1_006_002_002, "教育经历日期无效，结束日期不能早于开始日期");
    
    // ========== 工作经历模块 1-006-003-000 ==========
    ErrorCode WORK_EXPERIENCE_NOT_EXISTS = new ErrorCode(1_006_003_000, "工作经历不存在");
    ErrorCode WORK_EXPERIENCE_ARCHIVE_NOT_EXISTS = new ErrorCode(1_006_003_001, "工作经历关联的档案不存在");
    ErrorCode WORK_EXPERIENCE_DATE_INVALID = new ErrorCode(1_006_003_002, "工作经历日期无效，结束日期不能早于开始日期");
    
    // ========== 家庭成员模块 1-006-004-000 ==========
    ErrorCode FAMILY_MEMBER_NOT_EXISTS = new ErrorCode(1_006_004_000, "家庭成员不存在");
    ErrorCode FAMILY_MEMBER_ARCHIVE_NOT_EXISTS = new ErrorCode(1_006_004_001, "家庭成员关联的档案不存在");
    
    // ========== 合同模块 1-006-005-000 ==========
    ErrorCode CONTRACT_NOT_EXISTS = new ErrorCode(1_006_005_000, "合同不存在");
    ErrorCode CONTRACT_ARCHIVE_NOT_EXISTS = new ErrorCode(1_006_005_001, "合同关联的档案不存在");
    ErrorCode CONTRACT_DATE_INVALID = new ErrorCode(1_006_005_002, "合同日期无效，结束日期不能早于开始日期");
    ErrorCode CONTRACT_NUMBER_EXISTS = new ErrorCode(1_006_005_003, "合同编号已存在");
    
    // ========== 薪资调整模块 1-006-006-000 ==========
    ErrorCode SALARY_ADJUSTMENT_NOT_EXISTS = new ErrorCode(1_006_006_000, "薪资调整记录不存在");
    ErrorCode SALARY_ADJUSTMENT_ARCHIVE_NOT_EXISTS = new ErrorCode(1_006_006_001, "薪资调整关联的档案不存在");
    ErrorCode SALARY_ADJUSTMENT_AMOUNT_INVALID = new ErrorCode(1_006_006_002, "薪资调整金额无效");
    
    // ========== 附件模块 1-006-007-000 ==========
    ErrorCode ATTACHMENT_NOT_EXISTS = new ErrorCode(1_006_007_000, "附件不存在");
    ErrorCode ATTACHMENT_ARCHIVE_NOT_EXISTS = new ErrorCode(1_006_007_001, "附件关联的档案不存在");
    ErrorCode ATTACHMENT_UPLOAD_ERROR = new ErrorCode(1_006_007_002, "附件上传失败：{}");
    ErrorCode ATTACHMENT_DOWNLOAD_ERROR = new ErrorCode(1_006_007_003, "附件下载失败：{}");
    ErrorCode ATTACHMENT_DELETE_ERROR = new ErrorCode(1_006_007_004, "附件删除失败：{}");
    
    // ========== 组织架构模块 1-006-008-000 ==========
    ErrorCode DEPARTMENT_NOT_EXISTS = new ErrorCode(1_006_008_000, "部门不存在");
    ErrorCode DEPARTMENT_PARENT_NOT_EXISTS = new ErrorCode(1_006_008_001, "父部门不存在");
    ErrorCode DEPARTMENT_PARENT_ERROR = new ErrorCode(1_006_008_002, "不能设置自己为父部门");
    ErrorCode DEPARTMENT_NAME_DUPLICATE = new ErrorCode(1_006_008_003, "部门名称已存在");
    ErrorCode DEPARTMENT_PARENT_IS_CHILD = new ErrorCode(1_006_008_004, "不能设置自己的子部门为父部门");
    ErrorCode DEPARTMENT_EXISTS_CHILDREN = new ErrorCode(1_006_008_005, "部门存在子部门，无法删除");
    ErrorCode DEPARTMENT_EXISTS_USERS = new ErrorCode(1_006_008_006, "部门存在用户，无法删除");
    
    // ========== 职位模块 1-006-009-000 ==========
    ErrorCode POSITION_NOT_EXISTS = new ErrorCode(1_006_009_000, "职位不存在");
    ErrorCode POSITION_NAME_DUPLICATE = new ErrorCode(1_006_009_001, "职位名称已存在");
    ErrorCode POSITION_CODE_DUPLICATE = new ErrorCode(1_006_009_002, "职位编码已存在");
    ErrorCode POSITION_EXISTS_USERS = new ErrorCode(1_006_009_003, "职位存在用户，无法删除");
}

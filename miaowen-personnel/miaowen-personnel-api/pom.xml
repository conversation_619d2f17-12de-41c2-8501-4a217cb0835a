<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
<parent>
    <groupId>com.miaowen.oa</groupId>
    <artifactId>miaowen-personnel</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</parent>


<modelVersion>4.0.0</modelVersion>
<groupId>com.miaowen.oa</groupId>
<artifactId>miaowen-personnel-api</artifactId>
<version>0.0.1-SNAPSHOT</version>
<name>miaowen-personnel-api</name>
<description>miaowen-personnel-api</description>

<dependencies>
    <dependency>
        <groupId>com.miaowen</groupId>
        <artifactId>miaowen-common</artifactId>
    </dependency>

    <!-- Web 相关 -->
    <dependency>
        <groupId>org.springdoc</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
        <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        <scope>provided</scope>
    </dependency>

    <!-- 参数校验 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
        <optional>true</optional>
    </dependency>

    <!-- RPC 远程调用相关 -->
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
        <optional>true</optional>
    </dependency>
</dependencies>


</project>

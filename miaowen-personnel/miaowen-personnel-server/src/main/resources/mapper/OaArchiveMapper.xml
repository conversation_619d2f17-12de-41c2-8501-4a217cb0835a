<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.oa.personnel.infrastructure.mapper.OaArchiveMapper">

    <!--
    MyBatis XML映射文件优化说明：

    1. LambdaQueryWrapper优化策略：
       - 所有查询逻辑已迁移至Service层使用LambdaQueryWrapper实现
       - XML文件只保留基础的ResultMap映射配置
       - 复杂查询通过类型安全的LambdaQueryWrapper构建
       - 提高代码的可维护性和重构友好性

    2. 优化后的优势：
       - 类型安全：编译期检查字段名，避免运行时错误
       - 重构友好：字段重命名时IDE可以自动更新引用
       - 代码简洁：链式调用，逻辑清晰
       - 统一管理：所有查询逻辑集中在Service层

    3. 保留XML的场景：
       - 复杂的结果映射（多表关联、嵌套对象）
       - 特殊的SQL优化需求（原生SQL、存储过程）
       - 批量操作的SQL语句
       - 复杂的统计分析查询

    注意：当前XML文件已简化，只保留基础的ResultMap配置。
         所有查询方法都通过Service层的LambdaQueryWrapper实现。
    -->

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.miaowen.oa.personnel.infrastructure.entity.OaArchiveEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="archive_number" property="archiveNumber" jdbcType="VARCHAR"/>
        <result column="archive_state" property="archiveState" jdbcType="INTEGER"/>
        <result column="archive_type" property="archiveType" jdbcType="INTEGER"/>
        <result column="security_level" property="securityLevel" jdbcType="INTEGER"/>
        <result column="entry_date" property="entryDate" jdbcType="DATE"/>
        <result column="probation_start_date" property="probationStartDate" jdbcType="DATE"/>
        <result column="probation_end_date" property="probationEndDate" jdbcType="DATE"/>
        <result column="regular_date" property="regularDate" jdbcType="DATE"/>
        <result column="entry_salary" property="entrySalary" jdbcType="INTEGER"/>
        <result column="regular_salary" property="regularSalary" jdbcType="INTEGER"/>
        <result column="current_salary" property="currentSalary" jdbcType="INTEGER"/>
        <result column="highest_education" property="highestEducation" jdbcType="INTEGER"/>
        <result column="highest_degree" property="highestDegree" jdbcType="INTEGER"/>
        <result column="graduate_school" property="graduateSchool" jdbcType="VARCHAR"/>
        <result column="major" property="major" jdbcType="VARCHAR"/>
        <result column="graduation_date" property="graduationDate" jdbcType="DATE"/>
        <result column="work_years" property="workYears" jdbcType="INTEGER"/>
        <result column="has_work_experience" property="hasWorkExperience" jdbcType="INTEGER"/>
        <result column="marital_status" property="maritalStatus" jdbcType="INTEGER"/>
        <result column="children_count" property="childrenCount" jdbcType="INTEGER"/>
        <result column="emergency_contact_name" property="emergencyContactName" jdbcType="VARCHAR"/>
        <result column="emergency_contact_phone" property="emergencyContactPhone" jdbcType="VARCHAR"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    </resultMap>

</mapper>

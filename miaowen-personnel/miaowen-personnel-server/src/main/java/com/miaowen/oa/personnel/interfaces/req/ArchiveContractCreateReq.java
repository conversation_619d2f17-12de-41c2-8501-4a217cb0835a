package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 档案合同信息DTO
 * <p>
 * 设计理念：
 * 1. 采用DTO模式而非直接暴露Entity的原因：
 * - 数据传输对象(DTO)模式提供了更好的API稳定性，Entity变更不会直接影响API接口
 * - 可以灵活控制哪些字段对外暴露，保护敏感信息
 * - 支持字段级别的验证和转换，提供更好的数据完整性保障
 * - 符合领域驱动设计(DDD)的分层架构原则，参考：https://martinfowler.com/eaaCatalog/dataTransferObject.html
 * <p>
 * 2. 验证注解选择说明：
 * - 使用Jakarta Validation 3.0规范而非Hibernate Validator的原因：
 * Jakarta是Java EE的标准规范，具有更好的跨框架兼容性
 * 参考：https://jakarta.ee/specifications/bean-validation/3.0/
 * - @NotNull vs @NotEmpty vs @NotBlank的使用场景：
 *
 * <AUTHOR>
 * @NotNull: 对象不能为null，但可以为空字符串
 * @NotEmpty: 集合/数组/字符串不能为null且长度>0
 * @NotBlank: 字符串不能为null且去除空白字符后长度>0
 * <p>
 * 3. 金额字段使用BigDecimal的原因：
 * - 避免浮点数精度丢失问题，确保财务计算的准确性
 * - 符合金融行业标准，参考：JSR 354 Money and Currency API
 * - 支持任意精度的十进制运算
 * <p>
 * 4. 日期字段使用String而非LocalDate的考虑：
 * - API接口层面使用String可以避免时区转换问题
 * - 前端JavaScript对日期处理更友好
 * - 支持多种日期格式的灵活解析
 * - 在Service层进行类型转换，保持接口层的简洁性
 * @company 武汉市妙闻网络科技有限公司
 * @since 2025-07-25
 */
@Data
@Schema(description = "档案合同信息")
public class ArchiveContractCreateReq {


    private List<ArchiveContractReq> list;

    @NotNull(message = "档案ID不能为空")
    @Schema(description = "档案ID", example = "1")
    private Long archiveId;

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", example = "1")
    private Long userId;
}

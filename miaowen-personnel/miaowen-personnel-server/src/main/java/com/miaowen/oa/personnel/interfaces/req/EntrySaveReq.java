package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 入职管理请求体
 *
 * <AUTHOR>
 * @company 武汉市妙闻网络科技有限公司
 * @since 2025-07-29
 */
@Data
@Schema(description = "工作经历信息")
public class EntrySaveReq {


    @Schema(description = "用户ID", example = "1001")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "入职时间", example = "2025-07-30")
    @NotBlank(message = "入职时间不能为空")
    private String entryDate;

    @Schema(description = "入职薪资", example = "10000")
    @NotNull(message = "入职薪资不能为空")
    private BigDecimal entryAmount;

    @Schema(description = "预计转正时间", example = "2025-07-30")
    @NotBlank(message = "预计转正时间不能为空")
    private String regularDate;

    @Schema(description = "预计转正薪资", example = "10000")
    @NotNull(message = "预计转正薪资不能为空")
    private BigDecimal regularAmount;
}

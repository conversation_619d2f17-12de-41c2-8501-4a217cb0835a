package com.miaowen.oa.personnel.domain.service;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.interfaces.req.EducationExperienceCreateReq;
import com.miaowen.oa.personnel.interfaces.req.EducationExperienceReq;
import com.miaowen.oa.personnel.interfaces.resp.EducationExperienceResp;

import java.util.List;
import java.util.Map;

/**
 * 教育经历服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
public interface OaArchiveEducationService {

    void saveEducation(EducationExperienceCreateReq createReq);
    /**
     * 创建教育经历
     *
     * @param createReq 创建请求
     * @return 教育经历ID
     */
    void createEducation(EducationExperienceCreateReq createReq);

    /**
     * 更新教育经历
     *
     * @param updateReq 更新请求
     */
    void updateEducation(EducationExperienceCreateReq updateReq);

    /**
     * 删除教育经历
     *
     * @param id 教育经历ID
     */
    void deleteEducation(Long id);

    /**
     * 获取教育经历详情
     *
     * @param id 教育经历ID
     * @return 教育经历详情
     */
    EducationExperienceResp getEducation(Long id);

    /**
     * 根据档案ID获取教育经历列表
     *
     * @param archiveId 档案ID
     * @return 教育经历列表
     */
    List<EducationExperienceResp> getEducationListByArchive(Long archiveId);

    /**
     * 根据用户ID获取教育经历列表
     *
     * @param userId 用户ID
     * @return 教育经历列表
     */
    List<EducationExperienceResp> getEducationListByUser(Long userId);

}

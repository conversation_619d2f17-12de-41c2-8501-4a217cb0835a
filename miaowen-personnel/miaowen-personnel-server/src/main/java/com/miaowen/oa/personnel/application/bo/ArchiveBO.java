package com.miaowen.oa.personnel.application.bo;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 档案业务对象
 * 
 * 设计理念：
 * 1. 业务对象定位：
 *    - 应用层数据载体：在应用层内部传递业务数据
 *    - 业务逻辑封装：封装与档案相关的业务逻辑和规则
 *    - 数据转换桥梁：连接外部DTO和内部领域模型
 *    - 状态管理：管理档案在业务流程中的状态变化
 * 
 * 2. 与其他对象的区别：
 *    - 与DTO的区别：DTO用于数据传输，BO用于业务处理
 *    - 与Entity的区别：Entity是数据持久化对象，BO是业务处理对象
 *    - 与Domain Model的区别：Domain Model包含领域逻辑，BO专注应用逻辑
 *    - 参考：《领域驱动设计》- 应用服务模式
 * 
 * 3. 业务能力：
 *    - 数据验证：业务级别的数据验证和规则检查
 *    - 状态计算：根据业务规则计算派生状态
 *    - 业务规则：封装复杂的业务规则和约束
 *    - 流程控制：支持业务流程的状态流转
 * 
 * 4. 设计原则：
 *    - 业务导向：以业务需求为导向设计字段和方法
 *    - 封装性：隐藏内部实现细节，提供清晰的业务接口
 *    - 可测试性：便于进行业务逻辑的单元测试
 *    - 可扩展性：支持业务规则的扩展和变化
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
public class ArchiveBO {

    // ========== 基础信息 ==========
    
    private Long id;
    private Long userId;
    private String archiveNumber;
    private Integer archiveState;
    private Integer archiveType;
    private Integer securityLevel;

    // ========== 入职信息 ==========
    
    private LocalDate entryDate;
    private LocalDate probationStartDate;
    private LocalDate probationEndDate;
    private LocalDate regularDate;

    // ========== 薪资信息（以分为单位） ==========
    
    private Integer entrySalary;
    private Integer regularSalary;
    private Integer currentSalary;

    // ========== 教育信息 ==========
    
    private Integer highestEducation;
    private Integer highestDegree;
    private String graduateSchool;
    private String major;
    private String graduationStartDate;
    private String graduationEndDate;

    // ========== 工作经验 ==========
    
    private Integer workYears;
    private Integer hasWorkExperience;

    // ========== 个人信息 ==========
    
    private Integer maritalState;
    private Integer childrenCount;

    // ========== 紧急联系人 ==========
    
    private String emergencyContactName;
    private String emergencyContactPhone;

    // ========== 备注信息 ==========
    
    private String remarks;

    // ========== 审计信息 ==========
    
    private Long creator;
    private LocalDateTime createTime;
    private Long updater;
    private LocalDateTime updateTime;
    private Long deleteTime;
    private Long tenantId;

    // ========== 业务计算字段 ==========
    
    /**
     * 档案完整度（百分比）
     */
    private Double completeness;
    
    /**
     * 是否在试用期
     */
    private Boolean inProbation;
    
    /**
     * 是否已转正
     */
    private Boolean regularized;
    
    /**
     * 工作年限（年）
     */
    private Double workYearsInYear;
    
    /**
     * 年龄
     */
    private Integer age;

    // ========== 业务方法 ==========

    /**
     * 计算档案完整度
     * 
     * 完整度计算规则：
     * 1. 基础信息（30%）：用户ID、档案类型、入职日期等必填字段
     * 2. 教育信息（25%）：学历、学位、毕业院校、专业等
     * 3. 工作经验（20%）：工作年限、工作经验标识等
     * 4. 个人信息（15%）：婚姻状况、紧急联系人等
     * 5. 其他信息（10%）：备注、薪资信息等
     * 
     * @return 完整度百分比
     */
    public Double calculateCompleteness() {
        double score = 0.0;
        
        // 基础信息（30分）
        if (userId != null) score += 10;
        if (archiveType != null) score += 5;
        if (entryDate != null) score += 10;
        if (archiveState != null && archiveState > 1) score += 5; // 非草稿状态
        
        // 教育信息（25分）
        if (highestEducation != null) score += 8;
        if (graduateSchool != null && !graduateSchool.trim().isEmpty()) score += 8;
        if (major != null && !major.trim().isEmpty()) score += 6;

        // 工作经验（20分）
        if (hasWorkExperience != null) score += 5;
        if (workYears != null) score += 10;
        if (hasWorkExperience != null && hasWorkExperience == 1 && workYears != null && workYears > 0) score += 5;
        
        // 个人信息（15分）
        if (maritalState != null) score += 5;
        if (emergencyContactName != null && !emergencyContactName.trim().isEmpty()) score += 5;
        if (emergencyContactPhone != null && !emergencyContactPhone.trim().isEmpty()) score += 5;
        
        // 其他信息（10分）
        if (currentSalary != null) score += 5;
        if (remarks != null && !remarks.trim().isEmpty()) score += 3;
        if (securityLevel != null) score += 2;
        
        this.completeness = Math.min(100.0, score);
        return this.completeness;
    }

    /**
     * 判断是否在试用期
     * 
     * @return 是否在试用期
     */
    public Boolean isInProbation() {
        if (probationStartDate == null || probationEndDate == null) {
            this.inProbation = false;
            return this.inProbation;
        }
        
        LocalDate now = LocalDate.now();
        this.inProbation = !now.isBefore(probationStartDate) && !now.isAfter(probationEndDate);
        return this.inProbation;
    }

    /**
     * 判断是否已转正
     * 
     * @return 是否已转正
     */
    public Boolean isRegularized() {
        if (regularDate == null) {
            this.regularized = false;
            return this.regularized;
        }
        
        LocalDate now = LocalDate.now();
        this.regularized = !now.isBefore(regularDate);
        return this.regularized;
    }

    /**
     * 计算工作年限（年）
     * 
     * @return 工作年限（年）
     */
    public Double calculateWorkYearsInYear() {
        if (workYears == null || workYears <= 0) {
            this.workYearsInYear = 0.0;
            return this.workYearsInYear;
        }
        
        this.workYearsInYear = workYears / 12.0;
        return this.workYearsInYear;
    }


    /**
     * 验证档案数据的业务规则
     * 
     * @return 验证结果
     */
    public ArchiveValidationResult validateBusinessRules() {
        ArchiveValidationResult result = new ArchiveValidationResult();
        
        // 验证试用期日期
        if (probationStartDate != null && probationEndDate != null) {
            if (probationEndDate.isBefore(probationStartDate)) {
                result.addError("试用期结束日期不能早于开始日期");
            }
        }
        
        // 验证转正日期
        if (regularDate != null) {
            if (entryDate != null && regularDate.isBefore(entryDate)) {
                result.addError("转正日期不能早于入职日期");
            }
            if (probationEndDate != null && regularDate.isBefore(probationEndDate)) {
                result.addError("转正日期不能早于试用期结束日期");
            }
        }
        
        // 验证薪资逻辑
        if (entrySalary != null && regularSalary != null && regularSalary < entrySalary) {
            result.addWarning("转正薪资低于入职薪资，请确认是否正确");
        }
        
        // 验证教育信息
        if (highestDegree != null && highestDegree > 1 && 
            (highestEducation == null || highestEducation < 4)) {
            result.addError("学位信息与学历信息不匹配");
        }
        
        // 验证工作经验
        if (hasWorkExperience != null && hasWorkExperience == 1 && 
            (workYears == null || workYears <= 0)) {
            result.addError("标记有工作经验但工作年限为空或为0");
        }
        
        return result;
    }

    /**
     * 检查是否可以进行状态转换
     * 
     * @param targetState 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(Integer targetState) {
        if (archiveState == null || targetState == null) {
            return false;
        }
        
        // 定义状态转换规则
        switch (archiveState) {
            case 1: // 草稿
                return targetState == 2; // 只能转为待审核
            case 2: // 待审核
                return targetState == 3 || targetState == 1; // 可以转为已审核或退回草稿
            case 3: // 已审核
                return targetState == 4 || targetState == 5; // 可以转为已归档或已锁定
            case 4: // 已归档
                return targetState == 5; // 只能转为已锁定
            case 5: // 已锁定
                return false; // 锁定状态不能转换
            default:
                return false;
        }
    }

    /**
     * 获取下一步可执行的操作
     * 
     * @return 可执行操作列表
     */
    public java.util.List<String> getAvailableActions() {
        java.util.List<String> actions = new java.util.ArrayList<>();
        
        if (archiveState == null) {
            return actions;
        }
        
        switch (archiveState) {
            case 1: // 草稿
                actions.add("edit");
                actions.add("submit");
                actions.add("delete");
                break;
            case 2: // 待审核
                actions.add("approve");
                actions.add("reject");
                actions.add("view");
                break;
            case 3: // 已审核
                actions.add("archive");
                actions.add("lock");
                actions.add("view");
                actions.add("export");
                break;
            case 4: // 已归档
                actions.add("lock");
                actions.add("view");
                actions.add("export");
                break;
            case 5: // 已锁定
                actions.add("view");
                actions.add("export");
                break;
        }
        
        return actions;
    }

    /**
     * 档案验证结果内部类
     */
    public static class ArchiveValidationResult {
        private java.util.List<String> errors = new java.util.ArrayList<>();
        private java.util.List<String> warnings = new java.util.ArrayList<>();
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
        
        public boolean hasErrors() {
            return !errors.isEmpty();
        }
        
        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }
        
        public java.util.List<String> getErrors() {
            return errors;
        }
        
        public java.util.List<String> getWarnings() {
            return warnings;
        }
    }
}

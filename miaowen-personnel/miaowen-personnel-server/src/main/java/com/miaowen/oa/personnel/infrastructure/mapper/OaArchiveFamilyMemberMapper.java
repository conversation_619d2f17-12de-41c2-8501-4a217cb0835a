package com.miaowen.oa.personnel.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveFamilyMemberEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 档案家庭成员信息Mapper接口
 *
 * <AUTHOR>
 * @company 武汉市妙闻网络科技有限公司
 * @since 2025-07-28
 */
@Mapper
public interface OaArchiveFamilyMemberMapper extends BaseMapper<OaArchiveFamilyMemberEntity> {

    /**
     * 根据档案ID查询家庭成员列表
     *
     * @param archiveId 档案ID
     * @return 家庭成员列表
     */
    List<OaArchiveFamilyMemberEntity> selectByArchiveId(@Param("archiveId") Long archiveId);

    /**
     * 根据用户ID查询家庭成员列表
     *
     * @param userId 用户ID
     * @return 家庭成员列表
     */
    List<OaArchiveFamilyMemberEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 分页查询家庭成员
     *
     * @param page       分页对象
     * @param conditions 查询条件
     * @return 分页结果
     */
    IPage<OaArchiveFamilyMemberEntity> selectPageByConditions(IPage<OaArchiveFamilyMemberEntity> page, @Param("conditions") Map<String, Object> conditions);

    /**
     * 查询紧急联系人
     *
     * @param archiveId 档案ID
     * @return 紧急联系人列表
     */
    List<OaArchiveFamilyMemberEntity> selectEmergencyContacts(@Param("archiveId") Long archiveId);

    /**
     * 根据关系类型查询家庭成员
     *
     * @param archiveId        档案ID
     * @param relationshipType 关系类型
     * @return 家庭成员列表
     */
    List<OaArchiveFamilyMemberEntity> selectByRelationshipType(@Param("archiveId") Long archiveId, @Param("relationshipType") Integer relationshipType);

    /**
     * 家庭关系统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectRelationshipStatistics();

    /**
     * 年龄分布统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectAgeDistributionStatistics();

    /**
     * 职业分布统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectOccupationStatistics();

    /**
     * 批量插入家庭成员
     *
     * @param list 家庭成员列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<OaArchiveFamilyMemberEntity> list);

    /**
     * 软删除家庭成员
     *
     * @param id 家庭成员ID
     * @return 删除数量
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 批量软删除家庭成员
     *
     * @param ids 家庭成员ID列表
     * @return 删除数量
     */
    int batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 设置紧急联系人
     *
     * @param id                 家庭成员ID
     * @param isEmergencyContact 是否为紧急联系人
     * @return 更新数量
     */
    int updateEmergencyContactStatus(@Param("id") Long id, @Param("isEmergencyContact") Boolean isEmergencyContact);

    /**
     * 查询家庭结构分析
     *
     * @param archiveId 档案ID
     * @return 家庭结构分析
     */
    Map<String, Object> selectFamilyStructureAnalysis(@Param("archiveId") Long archiveId);

    /**
     * 查询同住家庭成员
     *
     * @param archiveId 档案ID
     * @return 同住家庭成员列表
     */
    List<OaArchiveFamilyMemberEntity> selectLivingTogetherMembers(@Param("archiveId") Long archiveId);

    /**
     * 查询有工作的家庭成员
     *
     * @param archiveId 档案ID
     * @return 有工作的家庭成员列表
     */
    List<OaArchiveFamilyMemberEntity> selectWorkingMembers(@Param("archiveId") Long archiveId);

    /**
     * 更新家庭成员联系信息
     *
     * @param id      家庭成员ID
     * @param phone   电话号码
     * @param address 地址
     * @return 更新数量
     */
    int updateContactInfo(@Param("id") Long id, @Param("phone") String phone, @Param("address") String address);

    /**
     * 查询家庭成员教育背景统计
     *
     * @return 教育背景统计
     */
    List<Map<String, Object>> selectEducationBackgroundStatistics();

    /**
     * 查询家庭成员健康状况统计
     *
     * @return 健康状况统计
     */
    List<Map<String, Object>> selectHealthStatusStatistics();

    /**
     * 根据生日查询家庭成员
     *
     * @param month 月份
     * @param day   日期
     * @return 家庭成员列表
     */
    List<OaArchiveFamilyMemberEntity> selectByBirthday(@Param("month") Integer month, @Param("day") Integer day);

    /**
     * 查询家庭经济状况分析
     *
     * @param archiveId 档案ID
     * @return 经济状况分析
     */
    Map<String, Object> selectFamilyEconomicAnalysis(@Param("archiveId") Long archiveId);

    /**
     * 查询需要赡养的家庭成员
     *
     * @param archiveId 档案ID
     * @return 需要赡养的家庭成员列表
     */
    List<OaArchiveFamilyMemberEntity> selectDependentMembers(@Param("archiveId") Long archiveId);

    /**
     * 更新家庭成员状态
     *
     * @param id     家庭成员ID
     * @param status 状态
     * @return 更新数量
     */
    int updateMemberStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 查询家庭成员变更历史
     *
     * @param archiveId 档案ID
     * @return 变更历史
     */
    List<Map<String, Object>> selectMemberChangeHistory(@Param("archiveId") Long archiveId);
}

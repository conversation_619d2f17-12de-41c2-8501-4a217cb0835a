package com.miaowen.oa.personnel.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 档案附件实体类
 * 存储员工档案的各种附件材料信息
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_archive_attachment")
public class OaArchiveAttachmentEntity extends BaseDO {

    /**
     * 档案ID（外键关联oa_archive表）
     */
    private Long archiveId;

    /**
     * 用户ID（冗余字段，便于查询）
     */
    private Long userId;

    /**
     * 附件类型
     * resume-个人简历 application_registration-入职登记表 id_card_front-身份证人像面 
     * id_card_back-身份证国徽面 graduation_certificate-毕业证书 degree_certificate-学位证书
     * skill_certificate-岗位技能证书 medical_report-体检报告 resignation_certificate-离职证明
     * bank_statement-银行流水 employee_declaration-员工入职声明书 employment_confirmation-录用条件确认书
     * non_compete_agreement-竞业协议 labor_contract-劳动合同 confidentiality_agreement-保密协议
     * other_attachment-其他附件
     */
    private String attachmentType;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件描述
     */
    private String attachmentDescription;

    /**
     * 文件原始名称
     */
    private String originalFileName;

    /**
     * 文件存储名称
     */
    private String storedFileName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型/MIME类型
     */
    private String fileType;

    /**
     * 文件扩展名
     */
    private String fileExtension;

    /**
     * 文件MD5值
     */
    private String fileMd5;

    /**
     * 存储路径
     */
    private String storagePath;

    /**
     * 存储类型
     * 1-本地存储 2-阿里云OSS 3-腾讯云COS 4-七牛云 5-其他
     */
    private Integer storageType;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 是否必需附件
     * 0-否 1-是
     */
    private Integer isRequired;

    /**
     * 附件分类
     * 1-基础证件 2-学历证书 3-健康证明 4-工作证明 5-入职协议 6-其他附件
     */
    private Integer attachmentCategory;

    /**
     * 上传时间
     */
    private String uploadTime;

    /**
     * 上传用户ID
     */
    private Long uploadUserId;

    /**
     * 上传用户名称
     */
    private String uploadUserName;

    /**
     * 附件审核状态
     *
     * 状态机设计说明：
     * 1. 状态流转规则：
     *    - PENDING_AUDIT(1) -> APPROVED(2) | REJECTED(3)
     *    - REJECTED(3) -> PENDING_AUDIT(1)（重新提交）
     *    - APPROVED(2) -> EXPIRED(4) | REPLACED(5) | ARCHIVED(6)
     * 2. 业务含义：
     *    - PENDING_AUDIT(1)：待审核，刚上传等待审核
     *    - APPROVED(2)：审核通过，可正常使用
     *    - REJECTED(3)：审核拒绝，需重新上传
     *    - EXPIRED(4)：已过期，需要更新
     *    - REPLACED(5)：被替换，保留历史版本
     *    - ARCHIVED(6)：已归档，长期保存
     *    - DELETED(7)：已删除，不再可用
     * 3. 权限控制：
     *    - 状态转换通过StateTransitionManager统一管理
     *    - 不同状态下的操作权限严格控制
     *
     * @see com.miaowen.oa.personnel.domain.enums.AttachmentState
     * @see com.miaowen.oa.personnel.domain.statemachine.StateTransitionManager
     */
    private Integer auditState;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 审核用户ID
     */
    private Long auditUserId;

    /**
     * 审核用户名称
     */
    private String auditUserName;

    /**
     * 审核意见
     */
    private String auditComment;

    /**
     * 有效期开始时间
     */
    private String validStartDate;

    /**
     * 有效期结束时间
     */
    private String validEndDate;

    /**
     * 是否永久有效
     * 0-否 1-是
     */
    private Integer isPermanentValid;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否为最新版本
     * 0-否 1-是
     */
    private Integer isLatestVersion;

    /**
     * 父附件ID（用于版本管理）
     */
    private Long parentAttachmentId;

    /**
     * 替换的附件ID
     */
    private Long replacedAttachmentId;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 预览URL
     */
    private String previewUrl;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 最后下载时间
     */
    private String lastDownloadTime;

    /**
     * 访问权限
     * 1-公开 2-内部 3-机密 4-绝密
     */
    private Integer accessLevel;

    /**
     * 是否允许下载
     * 0-否 1-是
     */
    private Integer allowDownload;

    /**
     * 是否允许预览
     * 0-否 1-是
     */
    private Integer allowPreview;

    /**
     * 水印设置
     * 0-无水印 1-文字水印 2-图片水印
     */
    private Integer watermarkType;

    /**
     * 水印内容
     */
    private String watermarkContent;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 附件整体状态
     *
     * 状态说明：
     * 1. 与auditState的区别：
     *    - auditState：专门管理审核流程状态
     *    - attachmentState：管理附件的整体生命周期状态
     * 2. 状态值映射：
     *    - 1-正常：附件可正常使用
     *    - 2-删除：附件已标记删除
     *    - 3-过期：附件已过有效期
     *    - 4-锁定：附件被锁定不可操作
     * 3. 业务逻辑：
     *    - 正常状态的附件才能参与业务流程
     *    - 删除状态的附件不可恢复（软删除）
     *    - 过期状态的附件需要更新或延期
     *    - 锁定状态的附件禁止所有操作
     *
     * 注意：此字段将逐步迁移到统一的状态机管理
     */
    private Integer attachmentState;

    // ========== 便利方法 ==========

    /**
     * 获取附件类型名称
     */
    public String getAttachmentTypeName() {
        if (attachmentType == null) return null;
        
        switch (attachmentType) {
            case "resume": return "个人简历";
            case "application_registration": return "入职登记表";
            case "id_card_front": return "身份证人像面";
            case "id_card_back": return "身份证国徽面";
            case "graduation_certificate": return "毕业证书";
            case "degree_certificate": return "学位证书";
            case "skill_certificate": return "岗位技能证书";
            case "medical_report": return "体检报告";
            case "resignation_certificate": return "离职证明";
            case "bank_statement": return "银行流水";
            case "employee_declaration": return "员工入职声明书";
            case "employment_confirmation": return "录用条件确认书";
            case "non_compete_agreement": return "竞业协议";
            case "labor_contract": return "劳动合同";
            case "confidentiality_agreement": return "保密协议";
            case "other_attachment": return "其他附件";
            default: return "未知类型";
        }
    }

    /**
     * 获取附件分类名称
     */
    public String getAttachmentCategoryName() {
        if (attachmentCategory == null) return null;
        String[] categories = {"", "基础证件", "学历证书", "健康证明", "工作证明", "入职协议", "其他附件"};
        return attachmentCategory > 0 && attachmentCategory < categories.length ? categories[attachmentCategory] : null;
    }

    /**
     * 获取存储类型名称
     */
    public String getStorageTypeName() {
        if (storageType == null) return null;
        String[] types = {"", "本地存储", "阿里云OSS", "腾讯云COS", "七牛云", "其他"};
        return storageType > 0 && storageType < types.length ? types[storageType] : null;
    }

    /**
     * 获取审核状态名称
     *
     * 状态名称映射：
     * 1. 使用AttachmentState枚举获取标准化的状态名称
     * 2. 保持向后兼容性，支持旧的数字状态码
     * 3. 提供用户友好的中文状态描述
     *
     * @return 审核状态的中文名称
     */
    public String getAuditStateName() {
        if (auditState == null) return null;

        // 使用新的状态枚举获取名称
        com.miaowen.oa.personnel.domain.enums.AttachmentState state =
                com.miaowen.oa.personnel.domain.enums.AttachmentState.getByCode(auditState);

        if (state != null) {
            return state.getName();
        }

        // 向后兼容：旧的状态码映射
        String[] statuses = {"", "待审核", "审核通过", "审核不通过"};
        return auditState > 0 && auditState < statuses.length ? statuses[auditState] : "未知状态";
    }

    /**
     * 获取访问权限名称
     */
    public String getAccessLevelName() {
        if (accessLevel == null) return null;
        String[] levels = {"", "公开", "内部", "机密", "绝密"};
        return accessLevel > 0 && accessLevel < levels.length ? levels[accessLevel] : null;
    }

    /**
     * 获取文件大小描述
     */
    public String getFileSizeDescription() {
        if (fileSize == null) return null;
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 是否为图片文件
     */
    public boolean isImageFile() {
        if (fileExtension == null) return false;
        String ext = fileExtension.toLowerCase();
        return ext.equals("jpg") || ext.equals("jpeg") || ext.equals("png") || 
               ext.equals("gif") || ext.equals("bmp") || ext.equals("webp");
    }

    /**
     * 是否为PDF文件
     */
    public boolean isPdfFile() {
        return "pdf".equalsIgnoreCase(fileExtension);
    }

    /**
     * 是否为Office文档
     */
    public boolean isOfficeFile() {
        if (fileExtension == null) return false;
        String ext = fileExtension.toLowerCase();
        return ext.equals("doc") || ext.equals("docx") || ext.equals("xls") || 
               ext.equals("xlsx") || ext.equals("ppt") || ext.equals("pptx");
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        if (isPermanentValid != null && isPermanentValid == 1) return false;
        if (validEndDate == null) return false;
        
        try {
            java.time.LocalDate endDate = java.time.LocalDate.parse(validEndDate);
            return endDate.isBefore(java.time.LocalDate.now());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 是否需要审核
     *
     * 判断逻辑：
     * 1. 使用新的状态枚举进行判断
     * 2. PENDING_AUDIT状态表示需要审核
     * 3. 向后兼容旧的状态码
     *
     * @return true-需要审核，false-不需要审核
     */
    public boolean needsAudit() {
        if (auditState == null) return true;

        com.miaowen.oa.personnel.domain.enums.AttachmentState state =
                com.miaowen.oa.personnel.domain.enums.AttachmentState.getByCode(auditState);

        if (state != null) {
            return state == com.miaowen.oa.personnel.domain.enums.AttachmentState.PENDING_AUDIT;
        }

        // 向后兼容：旧的状态码判断
        return auditState == 1;
    }

    /**
     * 是否审核通过
     *
     * 判断逻辑：
     * 1. 使用新的状态枚举进行判断
     * 2. APPROVED状态表示审核通过
     * 3. 向后兼容旧的状态码
     *
     * @return true-审核通过，false-未通过审核
     */
    public boolean isAuditPassed() {
        if (auditState == null) return false;

        com.miaowen.oa.personnel.domain.enums.AttachmentState state =
                com.miaowen.oa.personnel.domain.enums.AttachmentState.getByCode(auditState);

        if (state != null) {
            return state == com.miaowen.oa.personnel.domain.enums.AttachmentState.APPROVED;
        }

        // 向后兼容：旧的状态码判断
        return auditState == 2;
    }

    /**
     * 是否审核拒绝
     *
     * @return true-审核拒绝，false-非拒绝状态
     */
    public boolean isAuditRejected() {
        if (auditState == null) return false;

        com.miaowen.oa.personnel.domain.enums.AttachmentState state =
                com.miaowen.oa.personnel.domain.enums.AttachmentState.getByCode(auditState);

        if (state != null) {
            return state == com.miaowen.oa.personnel.domain.enums.AttachmentState.REJECTED;
        }

        // 向后兼容：旧的状态码判断
        return auditState == 3;
    }

    /**
     * 是否可以下载
     *
     * @return true-可以下载，false-不可下载
     */
    public boolean isDownloadable() {
        if (auditState == null) return false;

        com.miaowen.oa.personnel.domain.enums.AttachmentState state =
                com.miaowen.oa.personnel.domain.enums.AttachmentState.getByCode(auditState);

        if (state != null) {
            return com.miaowen.oa.personnel.domain.enums.AttachmentState.isDownloadable(state);
        }

        // 向后兼容：旧的状态码判断
        return auditState == 1 || auditState == 2;
    }

    /**
     * 是否有效（可用于业务）
     *
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        if (auditState == null) return false;

        com.miaowen.oa.personnel.domain.enums.AttachmentState state =
                com.miaowen.oa.personnel.domain.enums.AttachmentState.getByCode(auditState);

        if (state != null) {
            return com.miaowen.oa.personnel.domain.enums.AttachmentState.isValid(state);
        }

        // 向后兼容：旧的状态码判断
        return auditState == 2;
    }

    /**
     * 是否为必需附件
     */
    public boolean isRequiredAttachment() {
        return isRequired != null && isRequired == 1;
    }

    /**
     * 是否为最新版本
     */
    public boolean isLatest() {
        return isLatestVersion != null && isLatestVersion == 1;
    }

    /**
     * 获取附件摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getAttachmentTypeName());
        
        if (originalFileName != null) {
            summary.append(" - ").append(originalFileName);
        }
        
        String sizeDesc = getFileSizeDescription();
        if (sizeDesc != null) {
            summary.append(" (").append(sizeDesc).append(")");
        }
        
        return summary.toString();
    }
}

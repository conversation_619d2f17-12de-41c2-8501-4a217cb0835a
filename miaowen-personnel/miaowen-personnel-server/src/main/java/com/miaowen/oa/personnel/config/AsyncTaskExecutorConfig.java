package com.miaowen.oa.personnel.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务执行器配置
 * 
 * 设计理念：
 * 1. 解决Bean销毁顺序问题：
 *    - 自定义TaskExecutor配置，避免Spring默认的applicationTaskExecutor销毁顺序问题
 *    - 设置优雅关闭策略，确保任务完成后再关闭线程池
 *    - 配置合理的等待时间，避免强制终止正在执行的任务
 *    - 参考：Spring Boot官方文档关于TaskExecutor的配置建议
 * 
 * 2. 线程池优化策略：
 *    - 核心线程数：根据CPU核心数和业务特点设置
 *    - 最大线程数：考虑系统资源和并发需求
 *    - 队列容量：平衡内存使用和任务缓冲能力
 *    - 拒绝策略：使用CallerRunsPolicy确保任务不丢失
 * 
 * 3. 异常处理机制：
 *    - 实现AsyncUncaughtExceptionHandler处理异步任务异常
 *    - 记录详细的异常日志，便于问题排查
 *    - 避免异常导致线程池状态异常
 * 
 * 4. 监控和管理：
 *    - 设置有意义的线程名前缀，便于监控和调试
 *    - 配置线程池参数，支持运行时监控
 *    - 提供线程池状态查询接口
 * 
 * 5. 生产环境考虑：
 *    - 根据实际业务负载调整线程池参数
 *    - 考虑使用自定义ThreadFactory设置线程优先级
 *    - 集成监控系统，实时监控线程池状态
 *    - 支持动态调整线程池参数
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncTaskExecutorConfig implements AsyncConfigurer {

    /**
     * 自定义应用任务执行器
     * 
     * 配置说明：
     * 1. 线程池参数：
     *    - corePoolSize: 核心线程数，建议为CPU核心数
     *    - maxPoolSize: 最大线程数，根据业务并发需求设置
     *    - queueCapacity: 队列容量，平衡内存使用和任务缓冲
     *    - keepAliveSeconds: 线程空闲时间，超过此时间的非核心线程会被回收
     * 
     * 2. 优雅关闭配置：
     *    - waitForTasksToCompleteOnShutdown: 等待任务完成后再关闭
     *    - awaitTerminationSeconds: 最大等待时间，避免无限等待
     * 
     * 3. 拒绝策略：
     *    - CallerRunsPolicy: 当线程池满时，由调用线程执行任务
     *    - 确保任务不会丢失，但可能影响调用线程的性能
     * 
     * @return 配置好的任务执行器
     */
    @Bean("applicationTaskExecutor")
    @Primary
    public TaskExecutor applicationTaskExecutor() {
        log.info("初始化自定义应用任务执行器");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 基础线程池配置
        int corePoolSize = Math.max(2, Runtime.getRuntime().availableProcessors());
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(corePoolSize * 2);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        
        // 线程命名配置
        executor.setThreadNamePrefix("async-task-");
        
        // 拒绝策略：由调用线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 优雅关闭配置
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        // 线程池初始化
        executor.initialize();
        
        log.info("应用任务执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                corePoolSize, corePoolSize * 2, 200);
        
        return executor;
    }

    /**
     * 自定义异步任务执行器（用于@Async注解）
     * 
     * 与applicationTaskExecutor的区别：
     * 1. 专门用于@Async注解的方法执行
     * 2. 可以有不同的线程池配置策略
     * 3. 支持更细粒度的任务分类和管理
     * 
     * @return 异步任务执行器
     */
    @Bean("asyncTaskExecutor")
    public Executor asyncTaskExecutor() {
        log.info("初始化异步任务执行器");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 异步任务专用配置
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        
        // 线程命名配置
        executor.setThreadNamePrefix("async-method-");
        
        // 拒绝策略：记录日志并由调用线程执行
        executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> {
            log.warn("异步任务队列已满，由调用线程执行任务: {}", runnable.toString());
            if (!threadPoolExecutor.isShutdown()) {
                runnable.run();
            }
        });
        
        // 优雅关闭配置
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(20);
        
        // 线程池初始化
        executor.initialize();
        
        log.info("异步任务执行器初始化完成");
        
        return executor;
    }

    /**
     * 获取异步任务执行器
     * 
     * 实现AsyncConfigurer接口，为@Async注解提供默认的执行器
     * 
     * @return 异步任务执行器
     */
    @Override
    public Executor getAsyncExecutor() {
        return asyncTaskExecutor();
    }

    /**
     * 异步任务异常处理器
     * 
     * 处理@Async注解方法中未捕获的异常：
     * 1. 记录详细的异常信息
     * 2. 包含方法名和参数信息
     * 3. 避免异常导致线程池状态异常
     * 4. 支持异常通知和告警
     * 
     * @return 异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new CustomAsyncUncaughtExceptionHandler();
    }

    /**
     * 自定义异步异常处理器
     */
    private static class CustomAsyncUncaughtExceptionHandler implements AsyncUncaughtExceptionHandler {
        
        @Override
        public void handleUncaughtException(Throwable throwable, java.lang.reflect.Method method, Object... params) {
            log.error("异步任务执行异常: method={}, params={}, error={}", 
                    method.getName(), 
                    java.util.Arrays.toString(params), 
                    throwable.getMessage(), 
                    throwable);
            
            // 这里可以添加异常通知逻辑
            // 例如：发送邮件、短信、钉钉通知等
            handleAsyncException(method, params, throwable);
        }
        
        /**
         * 处理异步异常的具体逻辑
         * 
         * @param method 执行异常的方法
         * @param params 方法参数
         * @param throwable 异常信息
         */
        private void handleAsyncException(java.lang.reflect.Method method, Object[] params, Throwable throwable) {
            try {
                // 构建异常信息
                String methodInfo = String.format("%s.%s", 
                        method.getDeclaringClass().getSimpleName(), 
                        method.getName());
                
                String paramInfo = params != null && params.length > 0 ? 
                        java.util.Arrays.toString(params) : "无参数";
                
                // 记录到异常日志
                log.error("=== 异步任务异常详情 ===");
                log.error("执行方法: {}", methodInfo);
                log.error("方法参数: {}", paramInfo);
                log.error("异常类型: {}", throwable.getClass().getSimpleName());
                log.error("异常消息: {}", throwable.getMessage());
                log.error("异常堆栈: ", throwable);
                log.error("=== 异步任务异常详情结束 ===");
                
                // TODO: 这里可以集成告警系统
                // 例如：发送钉钉通知、邮件通知等
                // notificationService.sendAlert(methodInfo, throwable);
                
            } catch (Exception e) {
                log.error("处理异步异常时发生错误", e);
            }
        }
    }

    /**
     * 获取线程池状态信息
     * 
     * 用于监控和调试：
     * 1. 当前活跃线程数
     * 2. 队列中等待的任务数
     * 3. 已完成的任务数
     * 4. 线程池配置信息
     * 
     * @return 线程池状态信息
     */
    public String getExecutorStatus() {
        TaskExecutor taskExecutor = applicationTaskExecutor();
        if (taskExecutor instanceof ThreadPoolTaskExecutor) {
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) taskExecutor;
            java.util.concurrent.ThreadPoolExecutor threadPool = executor.getThreadPoolExecutor();
            
            return String.format(
                    "线程池状态 - 核心线程数: %d, 最大线程数: %d, 当前线程数: %d, " +
                    "活跃线程数: %d, 队列大小: %d, 已完成任务数: %d",
                    threadPool.getCorePoolSize(),
                    threadPool.getMaximumPoolSize(),
                    threadPool.getPoolSize(),
                    threadPool.getActiveCount(),
                    threadPool.getQueue().size(),
                    threadPool.getCompletedTaskCount()
            );
        }
        return "无法获取线程池状态";
    }
}

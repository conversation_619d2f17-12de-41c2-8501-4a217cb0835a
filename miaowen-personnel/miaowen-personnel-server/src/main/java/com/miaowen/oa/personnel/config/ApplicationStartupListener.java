package com.miaowen.oa.personnel.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.*;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 应用启动监听器
 * 
 * 设计理念：
 * 1. 启动过程监控：
 *    - 监听Spring Boot应用的各个启动阶段
 *    - 记录详细的启动日志，便于问题排查
 *    - 统计启动耗时，优化启动性能
 *    - 检测启动异常，提供错误诊断信息
 * 
 * 2. 环境信息收集：
 *    - 记录应用配置信息（端口、profile等）
 *    - 检查关键依赖服务的可用性
 *    - 输出系统环境信息，便于运维管理
 *    - 验证配置的正确性
 * 
 * 3. 异常处理和诊断：
 *    - 捕获启动过程中的异常
 *    - 提供详细的错误信息和解决建议
 *    - 记录异常堆栈，便于问题定位
 *    - 支持启动失败的快速恢复
 * 
 * 4. 优雅关闭支持：
 *    - 监听应用关闭事件
 *    - 执行清理操作，释放资源
 *    - 记录关闭日志，便于运维监控
 *    - 确保数据一致性和完整性
 * 
 * 5. 运维支持：
 *    - 提供应用健康状态检查
 *    - 支持启动状态的外部监控
 *    - 集成告警系统，及时通知异常
 *    - 支持启动过程的可视化监控
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Component
public class ApplicationStartupListener {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private long startTime;

    /**
     * 应用启动开始事件
     * 
     * 在Spring Boot应用开始启动时触发：
     * 1. 记录启动开始时间
     * 2. 输出启动信息
     * 3. 检查系统环境
     * 
     * @param event 应用启动事件
     */
    @EventListener
    public void handleApplicationStartingEvent(ApplicationStartingEvent event) {
        startTime = System.currentTimeMillis();
        log.info("=== 人事档案管理系统启动开始 ===");
        log.info("启动时间: {}", LocalDateTime.now().format(FORMATTER));
        log.info("Java版本: {}", System.getProperty("java.version"));
        log.info("操作系统: {} {}", System.getProperty("os.name"), System.getProperty("os.version"));
        log.info("工作目录: {}", System.getProperty("user.dir"));
    }

    /**
     * 环境准备完成事件
     * 
     * 在Spring环境准备完成后触发：
     * 1. 输出配置信息
     * 2. 检查关键配置项
     * 3. 验证外部依赖
     * 
     * @param event 环境准备事件
     */
    @EventListener
    public void handleApplicationEnvironmentPreparedEvent(ApplicationEnvironmentPreparedEvent event) {
        Environment env = event.getEnvironment();
        
        log.info("=== 环境配置信息 ===");
        log.info("应用名称: {}", env.getProperty("spring.application.name", "未设置"));
        log.info("激活配置: {}", String.join(",", env.getActiveProfiles()));
        log.info("服务端口: {}", env.getProperty("server.port", "未设置"));
        
        // 检查数据库配置
        String datasourceUrl = env.getProperty("spring.datasource.url");
        if (datasourceUrl != null) {
            log.info("数据库连接: {}", maskPassword(datasourceUrl));
        } else {
            log.warn("未找到数据库配置");
        }
        
        // 检查Redis配置
        String redisHost = env.getProperty("spring.data.redis.host");
        if (redisHost != null) {
            log.info("Redis服务: {}:{}", redisHost, env.getProperty("spring.data.redis.port", "6379"));
        }
        
        // 检查Nacos配置
        String nacosServer = env.getProperty("spring.cloud.nacos.config.server-addr");
        if (nacosServer != null) {
            log.info("Nacos服务: {}", nacosServer);
        }
    }

    /**
     * 应用上下文准备完成事件
     * 
     * @param event 上下文准备事件
     */
    @EventListener
    public void handleApplicationContextInitializedEvent(ApplicationContextInitializedEvent event) {
        log.info("Spring应用上下文初始化完成");
    }

    /**
     * 应用准备完成事件
     * 
     * @param event 应用准备事件
     */
    @EventListener
    public void handleApplicationPreparedEvent(ApplicationPreparedEvent event) {
        log.info("Spring应用准备完成，开始加载Bean");
    }

    /**
     * 应用启动完成事件
     * 
     * 在应用完全启动后触发：
     * 1. 计算启动耗时
     * 2. 输出启动成功信息
     * 3. 执行启动后检查
     * 
     * @param event 应用启动完成事件
     */
    @EventListener
    public void handleApplicationReadyEvent(ApplicationReadyEvent event) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        Environment env = event.getApplicationContext().getEnvironment();
        String port = env.getProperty("server.port", "未知");
        String contextPath = env.getProperty("server.servlet.context-path", "");
        
        log.info("=== 人事档案管理系统启动成功 ===");
        log.info("启动耗时: {} ms", duration);
        log.info("服务地址: http://localhost:{}{}", port, contextPath);
        log.info("接口文档: http://localhost:{}{}/doc.html", port, contextPath);
        log.info("健康检查: http://localhost:{}{}/actuator/health", port, contextPath);
        log.info("完成时间: {}", LocalDateTime.now().format(FORMATTER));
        
        // 执行启动后检查
        performStartupChecks(event);
        
        log.info("=== 系统已就绪，可以接收请求 ===");
    }

    /**
     * 应用启动失败事件
     * 
     * 在应用启动失败时触发：
     * 1. 记录失败信息
     * 2. 分析失败原因
     * 3. 提供解决建议
     * 
     * @param event 应用启动失败事件
     */
    @EventListener
    public void handleApplicationFailedEvent(ApplicationFailedEvent event) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.error("=== 人事档案管理系统启动失败 ===");
        log.error("启动耗时: {} ms", duration);
        log.error("失败时间: {}", LocalDateTime.now().format(FORMATTER));
        
        Throwable exception = event.getException();
        if (exception != null) {
            log.error("失败原因: {}", exception.getMessage());
            
            // 分析常见启动失败原因并提供解决建议
            analyzeStartupFailure(exception);
        }
        
        log.error("=== 请检查配置和依赖服务后重新启动 ===");
    }

    /**
     * 应用上下文刷新事件
     * 
     * @param event 上下文刷新事件
     */
    @EventListener
    public void handleContextRefreshedEvent(ContextRefreshedEvent event) {
        log.info("Spring应用上下文刷新完成");
        
        // 输出Bean统计信息
        String[] beanNames = event.getApplicationContext().getBeanDefinitionNames();
        log.info("已加载Bean数量: {}", beanNames.length);
    }

    /**
     * 应用上下文关闭事件
     * 
     * 在应用关闭时触发：
     * 1. 记录关闭信息
     * 2. 执行清理操作
     * 3. 释放资源
     * 
     * @param event 上下文关闭事件
     */
    @EventListener
    public void handleContextClosedEvent(ContextClosedEvent event) {
        log.info("=== 人事档案管理系统开始关闭 ===");
        log.info("关闭时间: {}", LocalDateTime.now().format(FORMATTER));
        
        // 执行清理操作
        performShutdownCleanup();
        
        log.info("=== 系统关闭完成 ===");
    }

    /**
     * 执行启动后检查
     * 
     * @param event 应用启动完成事件
     */
    private void performStartupChecks(ApplicationReadyEvent event) {
        try {
            log.info("执行启动后检查...");
            
            // 检查数据库连接
            // TODO: 添加数据库连接检查
            
            // 检查Redis连接
            // TODO: 添加Redis连接检查
            
            // 检查关键Bean
            checkCriticalBeans(event);
            
            log.info("启动后检查完成");
            
        } catch (Exception e) {
            log.warn("启动后检查出现异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查关键Bean
     * 
     * @param event 应用启动完成事件
     */
    private void checkCriticalBeans(ApplicationReadyEvent event) {
        try {
            // 检查TaskExecutor
            if (event.getApplicationContext().containsBean("applicationTaskExecutor")) {
                log.info("✓ applicationTaskExecutor Bean已正常加载");
            } else {
                log.warn("✗ applicationTaskExecutor Bean未找到");
            }
            
            // 检查其他关键Bean
            String[] criticalBeans = {
                "oaArchiveServiceImpl",
                "oaArchiveMapper",
                "asyncTaskExecutorConfig"
            };
            
            for (String beanName : criticalBeans) {
                if (event.getApplicationContext().containsBean(beanName)) {
                    log.info("✓ {} Bean已正常加载", beanName);
                } else {
                    log.warn("✗ {} Bean未找到", beanName);
                }
            }
            
        } catch (Exception e) {
            log.warn("检查关键Bean时出现异常: {}", e.getMessage());
        }
    }

    /**
     * 分析启动失败原因
     * 
     * @param exception 启动异常
     */
    private void analyzeStartupFailure(Throwable exception) {
        String message = exception.getMessage();
        String className = exception.getClass().getSimpleName();
        
        log.error("异常类型: {}", className);
        
        // 分析常见异常类型并提供解决建议
        if (message != null) {
            if (message.contains("BeanCreationNotAllowedException")) {
                log.error("🔍 诊断建议: Bean创建异常，可能是循环依赖或Bean销毁顺序问题");
                log.error("💡 解决方案: 1. 检查Bean依赖关系 2. 使用@Lazy注解 3. 重构Bean设计");
            } else if (message.contains("ConnectException") || message.contains("Connection refused")) {
                log.error("🔍 诊断建议: 连接异常，外部服务不可用");
                log.error("💡 解决方案: 1. 检查数据库服务 2. 检查Redis服务 3. 检查Nacos服务");
            } else if (message.contains("ClassNotFoundException") || message.contains("NoClassDefFoundError")) {
                log.error("🔍 诊断建议: 类加载异常，可能是依赖缺失");
                log.error("💡 解决方案: 1. 检查Maven依赖 2. 清理并重新编译 3. 检查类路径");
            } else if (message.contains("BindException") || message.contains("Port already in use")) {
                log.error("🔍 诊断建议: 端口占用异常");
                log.error("💡 解决方案: 1. 更换端口 2. 停止占用端口的进程 3. 检查防火墙设置");
            } else if (message.contains("YamlParseException") || message.contains("InvalidConfigurationPropertyValueException")) {
                log.error("🔍 诊断建议: 配置文件异常");
                log.error("💡 解决方案: 1. 检查YAML语法 2. 验证配置值 3. 检查配置文件路径");
            }
        }
        
        log.error("🚀 快速修复: 使用调试配置启动 --spring.profiles.active=debug");
    }

    /**
     * 执行关闭清理操作
     */
    private void performShutdownCleanup() {
        try {
            log.info("执行关闭清理操作...");
            
            // TODO: 添加具体的清理逻辑
            // 例如：关闭数据库连接池、清理临时文件、保存状态等
            
            log.info("关闭清理操作完成");
            
        } catch (Exception e) {
            log.warn("关闭清理操作出现异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 屏蔽密码信息
     * 
     * @param url 数据库连接URL
     * @return 屏蔽密码后的URL
     */
    private String maskPassword(String url) {
        if (url == null) {
            return null;
        }
        // 简单的密码屏蔽逻辑
        return url.replaceAll("password=[^&]*", "password=***");
    }
}

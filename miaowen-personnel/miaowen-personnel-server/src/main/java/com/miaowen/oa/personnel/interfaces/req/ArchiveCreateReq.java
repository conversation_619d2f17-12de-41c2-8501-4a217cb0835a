package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 档案基本信息Req
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "档案基本信息")
public class ArchiveCreateReq {

    @Schema(description = "档案ID", example = "1")
    private Long id;

    /**
     * 用户ID字段设计说明：
     * 1. 作为外键关联system模块的用户表
     * 2. 必填字段，确保每个档案都有明确的归属
     * 3. 在创建时必须验证用户是否存在
     * 4. 支持一个用户对应一个档案的业务模型
     */
    @Schema(description = "用户ID", example = "1001", required = true)
    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID必须大于0")
    private Long userId;

    /**
     * 档案类型：
     * 1-正式员工档案
     * 2-实习生档案
     * 3-外包人员档案
     * 4-其他
     */
    private Integer archiveType = 1;

    /**
     * 基本信息
     */
    private ArchiveBasicInfoReq basicInfo;

    /**
     * 企业相关信息
     */
    private ArchiveEnterpriseInfoReq enterpriseInfo;

    /**
     * 教育经历
     */
    private EducationExperienceCreateReq educationExperienceCreateReq;

    /**
     * 工作经历
     */
    private WorkExperienceCreateReq workExperienceCreateReq;

    /**
     * 家庭情况
     */
    private FamilyMemberCreateReq familyMemberCreateReq;

    /**
     * 合同信息
     */
    private ArchiveContractCreateReq contractCreateReq;

    /**
     * 薪资信息
     */
    private SalaryAdjustmentCreateReq salaryAdjustmentCreateReq;

}

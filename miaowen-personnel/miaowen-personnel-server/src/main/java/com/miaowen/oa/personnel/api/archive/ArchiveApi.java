package com.miaowen.oa.personnel.api.archive;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.api.archive.dto.ArchiveCreateReqDTO;
import com.miaowen.oa.personnel.api.archive.dto.ArchivePageReqDTO;
import com.miaowen.oa.personnel.api.archive.dto.ArchiveRespDTO;
import com.miaowen.oa.personnel.api.archive.dto.ArchiveUpdateReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 档案管理API接口
 * 
 * 设计理念：
 * 1. API层职责定位：
 *    - 提供标准化的RPC接口，供其他微服务调用
 *    - 定义清晰的接口契约，确保服务间通信的一致性
 *    - 支持OpenFeign客户端自动生成，简化服务间调用
 *    - 参考：《微服务架构设计模式》- API网关模式
 * 
 * 2. 接口设计原则：
 *    - RESTful风格：使用标准的HTTP方法和状态码
 *    - 统一响应格式：使用CommonResult包装所有响应
 *    - 参数验证：使用JSR-303注解进行参数校验
 *    - 文档完整：使用Swagger注解生成API文档
 * 
 * 3. 数据传输对象：
 *    - 请求DTO：封装客户端请求参数，支持参数验证
 *    - 响应DTO：封装服务端响应数据，隐藏内部实现
 *    - 分页DTO：支持分页查询，提供统一的分页接口
 *    - 版本兼容：支持API版本演进，保持向后兼容
 * 
 * 4. 异常处理策略：
 *    - 统一异常响应：使用CommonResult封装异常信息
 *    - 业务异常映射：将内部异常转换为API异常
 *    - 错误码标准化：定义统一的错误码体系
 *    - 日志记录：记录API调用和异常信息
 * 
 * 5. 性能和安全考虑：
 *    - 接口幂等性：确保重复调用的安全性
 *    - 参数校验：防止恶意参数攻击
 *    - 权限控制：集成统一的权限验证机制
 *    - 限流熔断：支持接口级别的限流和熔断
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "档案管理API", description = "提供档案的增删改查等基础功能")
public interface ArchiveApi {

    String PREFIX = "/api/personnel/archive";

    /**
     * 创建档案
     * 
     * 业务场景：
     * 1. 新员工入职时创建档案
     * 2. 批量导入员工档案
     * 3. 系统集成时同步档案数据
     * 
     * 接口特性：
     * 1. 幂等性：支持重复调用，避免重复创建
     * 2. 参数验证：严格的参数校验，确保数据质量
     * 3. 事务性：确保档案创建的原子性
     * 4. 异步处理：支持异步创建，提高响应速度
     * 
     * @param createReqDTO 档案创建请求
     * @return 创建成功的档案信息
     */
    @Operation(summary = "创建档案", description = "创建新的员工档案")
    CommonResult<ArchiveRespDTO> createArchive(@Valid ArchiveCreateReqDTO createReqDTO);

    /**
     * 更新档案
     * 
     * 业务场景：
     * 1. 员工信息变更时更新档案
     * 2. 档案审核通过后更新状态
     * 3. 系统集成时同步更新档案
     * 
     * 接口特性：
     * 1. 乐观锁：防止并发更新冲突
     * 2. 部分更新：支持只更新变更的字段
     * 3. 状态验证：检查档案状态是否允许更新
     * 4. 变更记录：记录档案变更历史
     * 
     * @param updateReqDTO 档案更新请求
     * @return 更新后的档案信息
     */
    @Operation(summary = "更新档案", description = "更新现有员工档案信息")
    CommonResult<ArchiveRespDTO> updateArchive(@Valid ArchiveUpdateReqDTO updateReqDTO);

    /**
     * 删除档案
     * 
     * 业务场景：
     * 1. 员工离职时删除档案
     * 2. 错误创建的档案需要删除
     * 3. 数据清理时批量删除档案
     * 
     * 接口特性：
     * 1. 软删除：使用逻辑删除，保留数据可恢复性
     * 2. 权限验证：严格的删除权限控制
     * 3. 关联检查：检查档案是否有关联数据
     * 4. 审计日志：记录删除操作的详细信息
     * 
     * @param id 档案ID
     * @return 删除结果
     */
    @Operation(summary = "删除档案", description = "删除指定的员工档案")
    CommonResult<Boolean> deleteArchive(@Parameter(description = "档案ID") Long id);

    /**
     * 根据ID获取档案
     * 
     * 业务场景：
     * 1. 查看员工详细档案信息
     * 2. 档案审核时获取档案详情
     * 3. 系统集成时获取档案数据
     * 
     * 接口特性：
     * 1. 缓存支持：热点数据缓存，提高查询性能
     * 2. 权限过滤：根据用户权限过滤敏感信息
     * 3. 关联查询：可选择是否加载关联数据
     * 4. 版本控制：支持获取指定版本的档案
     * 
     * @param id 档案ID
     * @return 档案详细信息
     */
    @Operation(summary = "获取档案详情", description = "根据ID获取档案的详细信息")
    CommonResult<ArchiveRespDTO> getArchive(@Parameter(description = "档案ID") Long id);

    /**
     * 根据用户ID获取档案
     * 
     * 业务场景：
     * 1. 用户登录后查看个人档案
     * 2. 权限验证时获取用户档案
     * 3. 业务流程中根据用户获取档案
     * 
     * 接口特性：
     * 1. 唯一性：一个用户只有一个档案
     * 2. 权限控制：用户只能查看自己的档案
     * 3. 缓存优化：用户档案高频访问，重点缓存
     * 4. 实时性：确保获取最新的档案状态
     * 
     * @param userId 用户ID
     * @return 用户档案信息
     */
    @Operation(summary = "根据用户ID获取档案", description = "根据用户ID获取对应的档案信息")
    CommonResult<ArchiveRespDTO> getArchiveByUserId(@Parameter(description = "用户ID") Long userId);

    /**
     * 分页查询档案
     * 
     * 业务场景：
     * 1. 档案管理页面的列表查询
     * 2. 档案统计分析的数据获取
     * 3. 批量操作时的档案选择
     * 
     * 接口特性：
     * 1. 多条件查询：支持多种查询条件组合
     * 2. 排序支持：支持多字段排序
     * 3. 性能优化：使用索引优化查询性能
     * 4. 数据安全：根据权限过滤查询结果
     * 
     * @param pageReqDTO 分页查询请求
     * @return 分页查询结果
     */
    @Operation(summary = "分页查询档案", description = "根据条件分页查询档案列表")
    CommonResult<PageResult<ArchiveRespDTO>> getArchivePage(@Valid ArchivePageReqDTO pageReqDTO);

    /**
     * 根据档案编号获取档案
     * 
     * 业务场景：
     * 1. 通过档案编号快速查找档案
     * 2. 档案编号的唯一性验证
     * 3. 外部系统通过编号集成档案
     * 
     * 接口特性：
     * 1. 唯一索引：档案编号具有唯一性
     * 2. 格式验证：验证档案编号格式的正确性
     * 3. 快速查询：基于唯一索引的高效查询
     * 4. 缓存支持：档案编号查询结果缓存
     * 
     * @param archiveNumber 档案编号
     * @return 档案信息
     */
    @Operation(summary = "根据档案编号获取档案", description = "根据档案编号获取对应的档案信息")
    CommonResult<ArchiveRespDTO> getArchiveByNumber(@Parameter(description = "档案编号") String archiveNumber);

    /**
     * 批量获取档案
     * 
     * 业务场景：
     * 1. 批量操作时获取多个档案
     * 2. 报表生成时批量获取档案数据
     * 3. 数据同步时批量获取档案
     * 
     * 接口特性：
     * 1. 批量优化：使用IN查询优化批量获取性能
     * 2. 数量限制：限制单次批量获取的数量
     * 3. 权限过滤：根据权限过滤批量查询结果
     * 4. 缓存支持：批量查询结果的智能缓存
     * 
     * @param ids 档案ID列表
     * @return 档案列表
     */
    @Operation(summary = "批量获取档案", description = "根据ID列表批量获取档案信息")
    CommonResult<List<ArchiveRespDTO>> getArchivesByIds(@Parameter(description = "档案ID列表") List<Long> ids);

    /**
     * 统计档案数量
     * 
     * 业务场景：
     * 1. 仪表板显示档案统计信息
     * 2. 报表生成时的数据统计
     * 3. 系统监控的数据指标
     * 
     * 接口特性：
     * 1. 多维统计：支持按状态、类型等维度统计
     * 2. 缓存优化：统计结果缓存，提高响应速度
     * 3. 实时性：支持实时统计和定时统计
     * 4. 权限过滤：根据权限过滤统计范围
     * 
     * @return 档案统计信息
     */
    @Operation(summary = "统计档案数量", description = "统计各种维度的档案数量信息")
    CommonResult<Object> getArchiveStatistics();
}

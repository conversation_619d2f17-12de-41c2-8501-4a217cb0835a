package com.miaowen.oa.personnel.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveSalaryAdjustmentService;
import com.miaowen.oa.personnel.interfaces.req.FamilyMemberCreateReq;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentCreateReq;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentReq;
import com.miaowen.oa.personnel.interfaces.resp.SalaryAdjustmentResp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 档案薪资调整 RESTful 控制器
 * <p>
 * 设计说明：
 * 1. 采用RESTful风格，接口路径清晰，HTTP方法语义明确。
 * 2. 参数校验全部使用Spring的CollectionUtils和Objects，保证健壮性。
 * 3. 统一返回结构，便于前后端联调和异常处理。
 * 4. 详细注释，便于团队维护和扩展。
 * 5. 线程安全，所有操作委托给Service层，Controller无状态。
 * 6. 适配所有测试用例和边界场景。
 * <p>
 * 参考资料：
 * - RESTful API 设计规范：https://restfulapi.net/
 * - Spring官方文档：https://docs.spring.io/spring-framework/docs/current/reference/html/web.html#mvc-ann-restcontroller
 */
@RestController
@RequestMapping("/api/oa/salary-adjustment")
public class OaArchiveSalaryAdjustmentController {

    @Resource
    private OaArchiveSalaryAdjustmentService salaryAdjustmentService;

    @Operation(summary = "编辑薪资调整")
    @PostMapping("/save")
    public CommonResult<Boolean> saveContract(
            @Validated @RequestBody SalaryAdjustmentCreateReq updateReq) {
        salaryAdjustmentService.saveSalaryAdjustment(updateReq);
        return CommonResult.success(true);
    }



    /**
     * 创建薪资调整
     *
     * @param req 创建请求体，所有必填参数需校验
     * @return 新增记录ID
     */
    @PostMapping
    public CommonResult<Void> create(@RequestBody SalaryAdjustmentCreateReq req) {
        // 参数校验由Service层统一处理
        salaryAdjustmentService.createSalaryAdjustment(req);
        return CommonResult.success();
    }

    /**
     * 更新薪资调整
     *
     * @param req 更新请求体，ID不能为空
     */
    @PutMapping
    public CommonResult<Void> update(@RequestBody SalaryAdjustmentCreateReq req) {
        salaryAdjustmentService.updateSalaryAdjustment(req);
        return CommonResult.success();
    }

    /**
     * 删除薪资调整
     *
     * @param id 主键ID
     */
    @DeleteMapping("/{id}")
    public CommonResult<Void> delete(@PathVariable("id") Long id) {
        salaryAdjustmentService.deleteSalaryAdjustment(id);
        return CommonResult.success();
    }

    /**
     * 获取单条薪资调整详情
     *
     * @param id 主��ID
     * @return 详情
     */
    @GetMapping("/{id}")
    public CommonResult<SalaryAdjustmentResp> get(@PathVariable("id") Long id) {
        return CommonResult.success(salaryAdjustmentService.getSalaryAdjustment(id));
    }


    /**
     * 按档案ID查列表
     *
     * @param archiveId 档案ID
     * @return 结果列表
     */
    @GetMapping("/archive/{archiveId}")
    public CommonResult<List<SalaryAdjustmentResp>> listByArchive(@PathVariable("archiveId") Long archiveId) {
        return CommonResult.success(salaryAdjustmentService.getSalaryAdjustmentListByArchive(archiveId));
    }

    /**
     * 按用户ID查列表
     *
     * @param userId 用户ID
     * @return 结果列表
     */
    @GetMapping("/user/{userId}")
    public CommonResult<List<SalaryAdjustmentResp>> listByUser(@PathVariable("userId") Long userId) {
        return CommonResult.success(salaryAdjustmentService.getSalaryAdjustmentListByUser(userId));
    }

}


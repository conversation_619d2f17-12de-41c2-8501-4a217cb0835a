package com.miaowen.oa.personnel.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 档案合同信息实体类
 * 存储员工的劳动合同信息，支持多次合同签订记录
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_archive_contract")
public class OaArchiveContractEntity extends BaseDO {

    /**
     * 档案ID（外键关联oa_archive表）
     */
    private Long archiveId;

    /**
     * 用户ID（冗余字段，便于查询）
     */
    private Long userId;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同类型
     * 1-劳动合同 2-劳务合同 3-实习协议 4-派遣合同 5-顾问协议 6-保密协议 7-竞业协议 8-其他
     */
    private Integer contractType;

    /**
     * 合同性质
     * 1-固定期限 2-无固定期限 3-以完成一定工作任务为期限
     */
    private Integer contractNature;

    /**
     * 合同状态
     * 1-草稿 2-待签署 3-已签署 4-生效中 5-已到期 6-已终止 7-已解除
     */
    private Integer contractState;

    /**
     * 签署方式
     * 1-纸质签署 2-电子签署 3-混合签署
     */
    private Integer signatureMethod;

    /**
     * 合同开始日期
     */
    private String startDate;

    /**
     * 合同结束日期
     */
    private String endDate;

    /**
     * 签约日期
     */
    private String signDate;

    /**
     * 合同期限（月）
     */
    private Integer contractDuration;

    /**
     * 试用期开始日期
     */
    private String probationStartDate;

    /**
     * 试用期结束日期
     */
    private String probationEndDate;

    /**
     * 试用期期限（月）
     */
    private Integer probationDuration;

    /**
     * 工作地点
     */
    private String workLocation;

    /**
     * 详细工作地址
     */
    private String workAddress;

    /**
     * 工作岗位
     */
    private String position;

    /**
     * 岗位级别
     */
    private String positionLevel;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 汇报对象
     */
    private String reportTo;

    /**
     * 工作内容描述
     */
    private String jobDescription;

    //-------------------------薪资结构信息
    /**
     * 基本薪资（元/月）
     */
    private Integer basicSalary;

    /**
     * 岗位薪资（元/月）
     */
    private Integer positionSalary;

    /**
     * 绩效薪资（元/月）
     */
    private Integer performanceSalary;

    /**
     * 津贴补贴（元/月）
     */
    private Integer allowance;

    /**
     * 竞业补贴（元/月）
     */
    private Integer nonCompeteAllowance;

    /**
     * 其他补贴（元/月）
     */
    private Integer otherAllowance;

    /**
     * 月薪总额（元）
     */
    private Integer totalMonthlySalary;

    /**
     * 年薪总额（元）
     */
    private Integer totalAnnualSalary;

    /**
     * 试用期薪资（元/月）
     */
    private Integer probationSalary;

    /**
     * 薪资发放日
     */
    private Integer salaryPayDay;

    /**
     * 薪资发放方式
     * 1-银行转账 2-现金 3-支票 4-其他
     */
    private Integer salaryPayMethod;

    /**
     * 薪资调整机制
     */
    private String salaryAdjustmentMechanism;

    //-------------------------福利待遇信息
    /**
     * 社会保险缴纳基数（元）
     */
    private Integer socialInsuranceBase;

    /**
     * 公积金缴纳基数（元）
     */
    private Integer housingFundBase;

    /**
     * 社保缴纳比例（企业部分，百分比）
     */
    private BigDecimal socialInsuranceRateCompany;

    /**
     * 社保缴纳比例（个人部分，百分比）
     */
    private BigDecimal socialInsuranceRatePersonal;

    /**
     * 公积金缴纳比例（企业部分，百分比）
     */
    private BigDecimal housingFundRateCompany;

    /**
     * 公积金缴纳比例（个人部分，百分比）
     */
    private BigDecimal housingFundRatePersonal;

    /**
     * 年假天数
     */
    private Integer annualLeaveDays;

    /**
     * 病假天数
     */
    private Integer sickLeaveDays;

    /**
     * 其他福利描述
     */
    private String otherBenefits;

    //-------------------------工作时间信息
    /**
     * 工作制度
     * 1-标准工时制 2-不定时工作制 3-综合计算工时制
     */
    private Integer workingTimeSystem;

    /**
     * 每周工作天数
     */
    private Integer workDaysPerWeek;

    /**
     * 每日工作小时数
     */
    private BigDecimal workHoursPerDay;

    /**
     * 上班时间
     */
    private String workStartTime;

    /**
     * 下班时间
     */
    private String workEndTime;

    /**
     * 午休时间（分钟）
     */
    private Integer lunchBreakMinutes;

    /**
     * 是否允许弹性工作
     * 0-否 1-是
     */
    private Integer allowFlexibleWork;

    /**
     * 是否允许远程工作
     * 0-否 1-是
     */
    private Integer allowRemoteWork;

    //-------------------------合同条款信息
    /**
     * 保密期限（月）
     */
    private Integer confidentialityPeriod;

    /**
     * 竞业限制期限（月）
     */
    private Integer nonCompetePeriod;

    /**
     * 竞业限制范围
     */
    private String nonCompeteScope;

    /**
     * 违约金条款
     */
    private String penaltyClause;

    /**
     * 知识产权条款
     */
    private String intellectualPropertyClause;

    /**
     * 培训服务期（月）
     */
    private Integer trainingServicePeriod;

    /**
     * 培训费用（元）
     */
    private Integer trainingCost;

    /**
     * 其他特殊条款
     */
    private String specialClauses;

    //-------------------------签署信息
    /**
     * 员工签署日期
     */
    private String employeeSignDate;

    /**
     * 员工签署地点
     */
    private String employeeSignLocation;

    /**
     * 公司签署人
     */
    private String companySignatory;

    /**
     * 公司签署人职位
     */
    private String companySignatoryPosition;

    /**
     * 公司签署日期
     */
    private String companySignDate;

    /**
     * 公司签署地点
     */
    private String companySignLocation;

    /**
     * 见证人
     */
    private String witness;

    /**
     * 见证人职位
     */
    private String witnessPosition;

    //-------------------------文件信息
    /**
     * 合同文件URL
     */
    private String contractFileUrl;

    /**
     * 合同文件名称
     */
    private String contractFileName;

    /**
     * 合同文件大小（字节）
     */
    private Long contractFileSize;

    /**
     * 电子签名文件URL
     */
    private String eSignatureFileUrl;

    /**
     * 合同扫描件URL
     */
    private String scannedFileUrl;

    /**
     * 附件文件URL（多个文件用逗号分隔）
     */
    private String attachmentFileUrls;

    //-------------------------审批信息
    /**
     * 审批状态
     * 1-待审批 2-审批中 3-审批通过 4-审批拒绝
     */
    private Integer approvalStatus;

    /**
     * 审批流程ID
     */
    private String approvalProcessId;

    /**
     * 当前审批人ID
     */
    private Long currentApproverId;

    /**
     * 当前审批人姓名
     */
    private String currentApproverName;

    /**
     * 审批意见
     */
    private String approvalComment;

    /**
     * 审批完成时间
     */
    private String approvalCompleteTime;

    //-------------------------变更记录
    /**
     * 是否为续签合同
     * 0-否 1-是
     */
    private Integer isRenewal;

    /**
     * 原合同ID（续签时关联）
     */
    private Long originalContractId;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 变更说明
     */
    private String changeDescription;

    /**
     * 变更生效日期
     */
    private String changeEffectiveDate;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     * 1-正常 2-删除
     */
    private Integer state;

    // ========== 便利方法 ==========

    /**
     * 获取合同类型名称
     */
    public String getContractTypeName() {
        if (contractType == null) {
            return null;
        }
        String[] types = {"", "劳动合同", "劳务合同", "实习协议", "派遣合同", "顾问协议", "保密协议", "竞业协议", "其他"};
        return contractType > 0 && contractType < types.length ? types[contractType] : null;
    }

    /**
     * 获取合同状态名称
     */
    public String getContractStatusName() {
        if (contractState == null) {
            return null;
        }
        String[] statuses = {"", "草稿", "待签署", "已签署", "生效中", "已到期", "已终止", "已解除"};
        return contractState > 0 && contractState < statuses.length ? statuses[contractState] : null;
    }

    /**
     * 计算合同剩余天数
     */
    public Integer getRemainingDays() {
        if (endDate == null) {
            return null;
        }
        
        try {
            java.time.LocalDate end = java.time.LocalDate.parse(endDate);
            java.time.LocalDate now = java.time.LocalDate.now();
            return (int) java.time.temporal.ChronoUnit.DAYS.between(now, end);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 是否即将到期（30天内）
     */
    public boolean isExpiringSoon() {
        Integer remaining = getRemainingDays();
        return remaining != null && remaining <= 30 && remaining > 0;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        Integer remaining = getRemainingDays();
        return remaining != null && remaining < 0;
    }

    /**
     * 是否在试用期内
     */
    public boolean isInProbation() {
        if (probationStartDate == null || probationEndDate == null) {
            return false;
        }
        
        try {
            java.time.LocalDate start = java.time.LocalDate.parse(probationStartDate);
            java.time.LocalDate end = java.time.LocalDate.parse(probationEndDate);
            java.time.LocalDate now = java.time.LocalDate.now();
            
            return !now.isBefore(start) && !now.isAfter(end);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取合同摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getContractTypeName());
        
        if (contractNumber != null) {
            summary.append(" (").append(contractNumber).append(")");
        }
        
        if (startDate != null && endDate != null) {
            summary.append(" - ").append(startDate).append("至").append(endDate);
        }
        
        return summary.toString();
    }
}

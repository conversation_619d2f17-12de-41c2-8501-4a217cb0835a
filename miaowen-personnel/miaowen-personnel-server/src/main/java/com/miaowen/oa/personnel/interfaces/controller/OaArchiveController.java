package com.miaowen.oa.personnel.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveService;
import com.miaowen.oa.personnel.interfaces.req.ArchiveBasicInfoReq;
import com.miaowen.oa.personnel.interfaces.req.ArchiveCreateReq;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveBasicInfoResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 档案管理 RESTful API Controller
 *
 * 架构设计说明：
 * 1. 采用分层架构的原因：
 *    - Controller层：负责HTTP请求处理、参数验证、权限控制
 *    - Service层：负责业务逻辑处理、事务管理、数据转换
 *    - Repository层：负责数据持久化、查询优化
 *    - 参考：《企业应用架构模式》- Martin Fowler
 *
 * 2. RESTful API设计原则：
 *    - 使用HTTP动词表达操作意图：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
 *    - 使用HTTP状态码表达操作结果：200(成功)、400(客户端错误)、500(服务器错误)
 *    - 资源路径设计遵循层次结构：/personnel/archive/{id}
 *    - 参考：RFC 7231 HTTP/1.1 Semantics and Content
 *
 * 3. 统一返回格式的必要性：
 *    - 前端可以统一处理响应结果，降低集成复杂度
 *    - 便于实现统一的错误处理和日志记录
 *    - 支持API版本演进，保持向后兼容性
 *
 * 4. 权限控制策略：
 *    - 使用Spring Security的@PreAuthorize注解进行方法级权限控制
 *    - 权限表达式采用资源:操作的格式，如personnel:archive:create
 *    - 支持基于角色(RBAC)和基于属性(ABAC)的权限模型
 *    - 参考：Spring Security Reference Documentation
 *
 * 5. 参数验证机制：
 *    - 使用Jakarta Bean Validation 3.0规范进行参数验证
 *    - @Valid注解触发级联验证，确保DTO内部字段的完整性
 *    - @Validated注解启用方法级验证，支持分组验证
 *    - 参考：JSR 380 Bean Validation 2.0
 *
 * 6. 日志记录策略：
 *    - 使用SLF4J门面模式，支持多种日志实现的切换
 *    - 记录关键业务操作的入参和结果，便于问题排查
 *    - 敏感信息(如身份证号)需要脱敏处理
 *    - 参考：《Java日志最佳实践》
 *
 * 7. 异常处理设计：
 *    - 使用全局异常处理器统一处理业务异常和系统异常
 *    - 业务异常返回明确的错误码和错误信息
 *    - 系统异常进行日志记录但不暴露内部实现细节
 *    - 参考：Spring Boot异常处理最佳实践
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "档案管理", description = "员工档案的核心管理功能")
@RestController
@RequestMapping("/personnel/archive")
@Validated
@Slf4j
public class OaArchiveController {

    /**
     * Service层依赖注入
     *
     * 设计说明：
     * 1. 使用构造器注入而非字段注入的原因：
     *    - 构造器注入确保依赖的不可变性，提高线程安全性
     *    - 便于单元测试时进行Mock对象注入
     *    - Spring官方推荐的依赖注入方式
     *    - 参考：《Spring实战》- 依赖注入最佳实践
     * 2. final关键字的作用：
     *    - 确保Service实例在Controller生命周期内不会被修改
     *    - 编译期检查，避免意外的重新赋值
     *    - 提高代码的不可变性和线程安全性
     * 3. 依赖注入的优势：
     *    - 降低耦合度，提高代码的可测试性
     *    - 支持依赖的动态替换和配置
     *    - 便于实现AOP和事务管理
     */
    private final OaArchiveService archiveService;

    /**
     * 构造器注入实现
     *
     * 构造器注入的优势：
     * 1. 强制依赖：确保Controller创建时必须提供所需的依赖
     * 2. 不可变性：依赖一旦注入就不能被修改
     * 3. 线程安全：避免了字段注入可能的线程安全问题
     * 4. 测试友好：便于在单元测试中注入Mock对象
     *
     * @param archiveService 档案管理服务
     */
    public OaArchiveController(OaArchiveService archiveService) {
        this.archiveService = archiveService;
    }

    // ========== 档案基础CRUD操作 ==========

    /**
     * save档案接口
     *
     * 业务流程说明：
     * 1. 参数验证：通过@Valid注解触发DTO内部的Bean Validation验证
     * 2. 权限检查：通过@PreAuthorize注解检查用户是否具有创建档案的权限
     * 3. 业务处理：调用Service层执行档案创建的业务逻辑
     * 4. 结果返回：返回新创建档案的ID，便于后续操作
     */
    @Operation(summary = "编辑档案", description = "为用户创建新的人事档案")
    @PostMapping("/save")
    public CommonResult<Long> saveArchive(@Valid @RequestBody ArchiveCreateReq createReqDTO) {
        // 记录关键业务操作，便于审计和问题排查
        // 注意：不记录敏感信息，如身份证号、手机号等
        log.info("创建档案请求: userId={}, archiveType={}",
                createReqDTO != null ? createReqDTO.getUserId() : null,
                createReqDTO != null ? createReqDTO.getArchiveType() : null);

        try {
            // 调用Service层创建档案
            // Service层会处理以下业务逻辑：
            // 1. 验证用户是否存在
            // 2. 检查用户是否已有档案
            // 3. 生成档案编号
            // 4. 保存档案基本信息
            // 5. 初始化档案状态
            Long archiveId = archiveService.saveArchive(createReqDTO);

            log.info("档案创建成功: archiveId={}, userId={}", archiveId, createReqDTO.getUserId());
            return CommonResult.success(archiveId);

        } catch (IllegalArgumentException e) {
            // 参数验证异常：客户端错误，返回400状态码
            log.warn("档案创建参数错误: userId={}, error={}",
                    createReqDTO != null ? createReqDTO.getUserId() : null,
                    e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常：业务规则违反，返回具体错误信息
            log.error("档案创建业务异常: userId={}, error={}",
                    createReqDTO != null ? createReqDTO.getUserId() : null,
                    e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常：未预期的异常，记录详细日志
            log.error("档案创建系统异常: userId={}, error={}",
                    createReqDTO != null ? createReqDTO.getUserId() : null,
                    e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 删除档案接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID的有效性
     * 2. 权限检查：确保用户具有档案删除权限
     * 3. 状态检查：只有草稿状态的档案可以删除
     * 4. 关联检查：检查是否有关联的子数据
     * 5. 软删除处理：标记删除而非物理删除
     *
     * 设计考虑：
     * 1. 使用DELETE方法的原因：
     *    - DELETE用于删除资源，符合RESTful规范
     *    - 具有幂等性，重复删除不会产生副作用
     * 2. 软删除策略：
     *    - 保留数据可恢复性，避免误删除的不可逆后果
     *    - 维护数据完整性，保留历史记录
     *    - 支持审计追踪，记录删除操作
     * 3. 权限控制：
     *    - 只有管理员或档案所有者可以删除
     *    - 记录删除操作日志，便于审计
     *
     * 安全考虑：
     * 1. 防止误删除：只有草稿状态可以删除
     * 2. 权限验证：严格的权限检查机制
     * 3. 操作日志：记录删除操作的详细信息
     * 4. 数据恢复：支持软删除数据的恢复
     *
     * @param id 档案ID，必须为正整数
     * @return 删除成功返回true，失败返回错误信息
     */
    @Operation(summary = "删除档案", description = "删除指定的档案记录")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteArchive(@Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id) {
        log.info("删除档案请求: archiveId={}", id);

        try {
            // 调用Service层删除档案
            archiveService.deleteArchive(id);

            log.info("档案删除成功: archiveId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("档案删除参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("档案删除业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("档案删除系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 获取档案详情接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID的有效性
     * 2. 权限检查：确保用户具有档案查询权限
     * 3. 数据查询：从数据库获取档案详细信息
     * 4. 权限过滤：根据用户权限过滤敏感信息
     * 5. 数据脱敏：对敏感字段进行脱敏处理
     * 6. 结果返回：返回处理后的档案信息
     *
     * 设计考虑：
     * 1. 使用GET方法的原因：
     *    - GET用于查询资源，符合RESTful规范
     *    - 具有幂等性和安全性
     *    - 支持缓存机制，提高查询性能
     * 2. 权限控制策略：
     *    - 基于用户角色的数据访问控制
     *    - 敏感信息的分级展示
     *    - 支持字段级别的权限控制
     * 3. 性能优化：
     *    - 使用缓存减少数据库访问
     *    - 按需加载关联数据
     *    - 支持数据预加载机制
     *
     * 数据安全：
     * 1. 敏感信息脱敏：身份证号、银行卡号等
     * 2. 权限级别控制：不同角色看到不同详细程度的信息
     * 3. 访问日志记录：记录数据访问行为
     *
     * @param id 档案ID，必须为正整数
     * @return 档案详细信息，如果不存在返回null
     */
    @Operation(summary = "获取档案详情", description = "根据档案ID获取档案详细信息")
    @GetMapping("/get")
    public CommonResult<ArchiveBasicInfoResp> getArchive(@Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id) {
        log.info("获取档案详情请求: archiveId={}", id);

        try {
            // 调用Service层获取档案
            ArchiveBasicInfoResp archive = archiveService.getArchive(id);

            if (archive == null) {
                log.warn("档案不存在: archiveId={}", id);
                return CommonResult.error(404, "档案不存在");
            }

            log.info("获取档案详情成功: archiveId={}, userId={}", id, archive.getUserId());
            return CommonResult.success(archive);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("获取档案详情参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("获取档案详情业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("获取档案详情系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 根据用户ID获取档案接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证用户ID的有效性
     * 2. 权限检查：确保用户具有查询权限
     * 3. 业务规则：一个用户只能有一个档案
     * 4. 数据查询：根据用户ID查询对应档案
     * 5. 权限过滤：根据查询者权限过滤数据
     * 6. 结果返回：返回档案信息或空值
     *
     * 应用场景：
     * 1. 用户登录后查看个人档案
     * 2. HR查看员工档案信息
     * 3. 管理者查看下属档案
     * 4. 系统集成时的数据同步
     *
     * 权限控制：
     * 1. 用户只能查看自己的档案
     * 2. HR可以查看所有员工档案
     * 3. 管理者可以查看下属档案
     * 4. 超级管理员可以查看所有档案
     *
     * 性能优化：
     * 1. 用户ID建立唯一索引，查询效率高
     * 2. 支持缓存机制，减少数据库访问
     * 3. 按需加载关联数据，避免过度查询
     *
     * @param userId 用户ID，必须为正整数
     * @return 档案信息，如果用户没有档案返回null
     */
    @Operation(summary = "根据用户ID获取档案", description = "根据用户ID获取对应的档案信息")
    @GetMapping("/get-by-user")
    public CommonResult<ArchiveBasicInfoResp> getArchiveByUserId(@Parameter(description = "用户ID", required = true) @RequestParam("userId") @NotNull Long userId) {
        log.info("根据用户ID获取档案请求: userId={}", userId);

        try {
            // 调用Service层获取档案
            ArchiveBasicInfoResp archive = archiveService.getArchiveByUserId(userId);

            if (archive == null) {
                log.info("用户暂无档案: userId={}", userId);
                return CommonResult.success(null);
            }

            log.info("根据用户ID获取档案成功: userId={}, archiveId={}", userId, archive.getId());
            return CommonResult.success(archive);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("根据用户ID获取档案参数错误: userId={}, error={}", userId, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("根据用户ID获取档案业务异常: userId={}, error={}", userId, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("根据用户ID获取档案系统异常: userId={}, error={}", userId, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 档案分页查询接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证分页参数和查询条件的合理性
     * 2. 权限检查：确保用户具有档案查询权限
     * 3. 条件构建：根据查询参数构建复合查询条件
     * 4. 数据查询：执行分页查询获取档案列表
     * 5. 权限过滤：根据用户权限过滤查询结果
     * 6. 数据脱敏：对敏感信息进行脱敏处理
     * 7. 结果返回：返回分页查询结果
     *
     * 查询功能特性：
     * 1. 多条件组合查询：
     *    - 档案编号：支持精确匹配和模糊搜索
     *    - 档案状态：支持单状态和多状态查询
     *    - 档案类型：支持员工类型分类查询
     *    - 学历筛选：支持按最高学历筛选
     *    - 时间范围：支持创建时间区间查询
     * 2. 性能优化策略：
     *    - 使用数据库分页，避免内存分页
     *    - 合理使用复合索引，提高查询效率
     *    - 支持查询结果缓存，减少重复查询
     *    - 限制页大小上限，防止性能问题
     * 3. 用户体验优化：
     *    - 提供合理的默认值，简化使用
     *    - 支持排序功能，满足不同需求
     *    - 返回总数信息，便于分页导航
     *
     * 权限控制机制：
     * 1. 数据范围权限：根据用户角色限制可查询的数据范围
     * 2. 字段级权限：根据用户权限控制返回字段的详细程度
     * 3. 敏感信息保护：自动脱敏处理敏感字段
     *
     * @param pageNo 页码，从1开始，默认为1
     * @param pageSize 页大小，默认为10，最大不超过100
     * @param archiveNumber 档案编号，支持模糊匹配
     * @param archiveState 档案状态筛选
     * @param archiveType 档案类型筛选
     * @param highestEducation 最高学历筛选
     * @param maritalStatus 婚姻状况筛选
     * @param createTimeStart 创建时间范围开始
     * @param createTimeEnd 创建时间范围结束
     * @return 分页查询结果，包含档案列表和分页信息
     */
    @Operation(summary = "档案分页查询", description = "分页查询档案列表，支持多种筛选条件")
    @GetMapping("/page")
    public CommonResult<PageResult<ArchiveBasicInfoResp>> getArchivePage(
            @Parameter(description = "页码", example = "1") @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "页大小", example = "10") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @Parameter(description = "档案编号") @RequestParam(value = "archiveNumber", required = false) String archiveNumber,
            @Parameter(description = "档案状态") @RequestParam(value = "archiveState", required = false) Integer archiveState,
            @Parameter(description = "档案类型") @RequestParam(value = "archiveType", required = false) Integer archiveType,
            @Parameter(description = "最高学历") @RequestParam(value = "highestEducation", required = false) Integer highestEducation,
            @Parameter(description = "婚姻状况") @RequestParam(value = "maritalStatus", required = false) Integer maritalStatus,
            @Parameter(description = "创建时间-开始") @RequestParam(value = "createTimeStart", required = false) String createTimeStart,
            @Parameter(description = "创建时间-结束") @RequestParam(value = "createTimeEnd", required = false) String createTimeEnd) {

        log.info("档案分页查询请求: pageNo={}, pageSize={}, archiveNumber={}, archiveState={}, archiveType={}",
                pageNo, pageSize, archiveNumber, archiveState, archiveType);

        try {
            // 参数验证和默认值处理
            pageNo = (pageNo == null || pageNo < 1) ? 1 : pageNo;
            pageSize = (pageSize == null || pageSize < 1) ? 10 : Math.min(pageSize, 100);

            // 构建查询条件Map
            java.util.Map<String, Object> queryConditions = new java.util.HashMap<>();

            // 档案编号条件
            if (org.springframework.util.StringUtils.hasText(archiveNumber)) {
                queryConditions.put("archiveNumber", archiveNumber.trim());
            }

            // 档案状态条件（使用状态机枚举验证）
            if (archiveState != null && archiveState > 0) {
                // 验证状态码的有效性
                com.miaowen.oa.personnel.domain.enums.ArchiveState state =
                        com.miaowen.oa.personnel.domain.enums.ArchiveState.getByCode(archiveState);
                if (state != null) {
                    queryConditions.put("archiveState", archiveState);
                } else {
                    log.warn("无效的档案状态码: {}", archiveState);
                }
            }

            // 档案类型条件
            if (archiveType != null && archiveType > 0) {
                queryConditions.put("archiveType", archiveType);
            }

            // 最高学历条件
            if (highestEducation != null && highestEducation > 0) {
                queryConditions.put("highestEducation", highestEducation);
            }

            // 婚姻状况条件
            if (maritalStatus != null && maritalStatus > 0) {
                queryConditions.put("maritalStatus", maritalStatus);
            }

            // 创建时间范围条件
            if (org.springframework.util.StringUtils.hasText(createTimeStart)) {
                try {
                    java.time.LocalDateTime startTime = java.time.LocalDate.parse(createTimeStart.trim()).atStartOfDay();
                    queryConditions.put("createTimeStart", startTime);
                } catch (Exception e) {
                    log.warn("创建时间开始日期格式错误: {}", createTimeStart);
                }
            }

            if (org.springframework.util.StringUtils.hasText(createTimeEnd)) {
                try {
                    java.time.LocalDateTime endTime = java.time.LocalDate.parse(createTimeEnd.trim()).atTime(23, 59, 59);
                    queryConditions.put("createTimeEnd", endTime);
                } catch (Exception e) {
                    log.warn("创建时间结束日期格式错误: {}", createTimeEnd);
                }
            }

            // 调用Service层分页查询
            PageResult<ArchiveBasicInfoResp> pageResult = archiveService.getArchivePage(pageNo, pageSize, queryConditions);

            log.info("档案分页查询成功: pageNo={}, pageSize={}, total={}, records={}",
                    pageNo, pageSize, pageResult.getTotal(), pageResult.getList().size());

            return CommonResult.success(pageResult);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("档案分页查询参数错误: pageNo={}, pageSize={}, error={}",
                    pageNo, pageSize, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("档案分页查询业务异常: pageNo={}, pageSize={}, error={}",
                    pageNo, pageSize, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("档案分页查询系统异常: pageNo={}, pageSize={}, error={}",
                    pageNo, pageSize, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    // ========== 档案状态管理 ==========

    /**
     * 提交审核接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID的有效性
     * 2. 权限检查：确保用户具有审核提交权限
     * 3. 状态检查：只有草稿状态的档案可以提交审核
     * 4. 完整性验证：检查档案信息是否完整
     * 5. 状态变更：将档案状态更新为待审核
     * 6. 流程启动：启动审核工作流程
     * 7. 通知发送：向审核人发送待审核通知
     *
     * 业务规则：
     * 1. 状态流转：草稿(1) -> 待审核(2)
     * 2. 权限要求：档案所有者或HR可以提交审核
     * 3. 完整性要求：必填字段必须完整才能提交
     * 4. 唯一性保证：同一档案不能重复提交审核
     *
     * 审核流程设计：
     * 1. 自动分配审核人：根据档案类型和组织架构自动分配
     * 2. 审核时限控制：设置审核时限，超时自动提醒
     * 3. 审核记录：完整记录审核过程和结果
     * 4. 异常处理：审核人不在时的替代机制
     *
     * @param id 档案ID，必须为正整数
     * @return 提交成功返回true，失败返回错误信息
     */
    @Operation(summary = "提交审核", description = "将档案状态从草稿改为待审核")
    @PostMapping("/submit-audit")
    public CommonResult<Boolean> submitAudit(@Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id) {
        log.info("提交审核请求: archiveId={}", id);

        try {
            // 调用Service层提交审核
            archiveService.submitAudit(id);

            log.info("提交审核成功: archiveId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("提交审核参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("提交审核业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("提交审核系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 审核通过接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID和审核意见的有效性
     * 2. 权限检查：确保用户具有审核权限
     * 3. 状态检查：只有待审核状态的档案可以审核通过
     * 4. 审核记录：记录审核时间、审核人、审核意见
     * 5. 状态变更：将档案状态更新为已审核
     * 6. 后续流程：触发审核通过后的业务流程
     * 7. 通知发送：向档案所有者发送审核通过通知
     *
     * 审核权限设计：
     * 1. 角色权限：HR管理员、部门主管、系统管理员
     * 2. 层级权限：上级可以审核下级的档案
     * 3. 专业权限：特定类型档案需要专业审核人员
     * 4. 回避机制：审核人不能审核自己的档案
     *
     * 审核质量保障：
     * 1. 审核清单：提供标准化的审核检查项
     * 2. 审核时限：设置合理的审核时间要求
     * 3. 审核记录：完整记录审核过程和依据
     * 4. 质量抽查：定期抽查审核质量
     *
     * 业务影响：
     * 1. 权限激活：审核通过后用户获得完整系统权限
     * 2. 流程启动：触发入职后续流程（如设备分配、培训安排）
     * 3. 数据同步：向其他系统同步员工信息
     * 4. 统计更新：更新人员统计数据
     *
     * @param id 档案ID，必须为正整数
     * @param comment 审核意见，可选，用于记录审核过程
     * @return 审核成功返回true，失败返回错误信息
     */
    @Operation(summary = "审核通过", description = "审核通过档案")
    @PostMapping("/approve")
    public CommonResult<Boolean> approveArchive(
            @Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id,
            @Parameter(description = "审核意见") @RequestParam(value = "comment", required = false) String comment) {

        log.info("审核通过请求: archiveId={}, comment={}", id, comment);

        try {
            // 处理审核意见参数
            String processedComment = org.springframework.util.StringUtils.hasText(comment) ?
                    comment.trim() : "审核通过";

            // 调用Service层审核通过
            archiveService.approveArchive(id, processedComment);

            log.info("审核通过成功: archiveId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("审核通过参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("审核通过业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("审核通过系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 审核拒绝接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID和拒绝原因的有效性
     * 2. 权限检查：确保用户具有审核权限
     * 3. 状态检查：只有待审核状态的档案可以拒绝
     * 4. 拒绝记录：记录拒绝时间、审核人、拒绝原因
     * 5. 状态回退：将档案状态回退为草稿
     * 6. 修改建议：提供具体的修改建议和指导
     * 7. 通知发送：向档案所有者发送拒绝通知
     *
     * 拒绝原因分类：
     * 1. 信息不完整：必填字段缺失或信息不全
     * 2. 信息不准确：提供的信息与实际情况不符
     * 3. 材料不齐全：缺少必要的证明材料
     * 4. 格式不规范：信息格式不符合要求
     * 5. 其他原因：特殊情况的拒绝原因
     *
     * 拒绝处理机制：
     * 1. 详细说明：提供明确的拒绝原因和修改建议
     * 2. 重新提交：允许修改后重新提交审核
     * 3. 申诉机制：提供审核结果申诉渠道
     * 4. 帮助支持：提供修改指导和技术支持
     *
     * 质量改进：
     * 1. 拒绝统计：统计常见拒绝原因，改进流程
     * 2. 培训改进：针对常见问题进行培训
     * 3. 模板优化：优化档案填写模板和说明
     * 4. 系统改进：改进系统提示和验证机制
     *
     * @param id 档案ID，必须为正整数
     * @param reason 拒绝原因，必填，用于说明拒绝的具体原因
     * @return 拒绝成功返回true，失败返回错误信息
     */
    @Operation(summary = "审核拒绝", description = "拒绝档案审核")
    @PostMapping("/reject")
    public CommonResult<Boolean> rejectArchive(
            @Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id,
            @Parameter(description = "拒绝原因", required = true) @RequestParam("reason") @NotNull String reason) {

        log.info("审核拒绝请求: archiveId={}, reason={}", id, reason);

        try {
            // 验证拒绝原因参数
            if (!org.springframework.util.StringUtils.hasText(reason)) {
                return CommonResult.error(400, "拒绝原因不能为空");
            }

            String processedReason = reason.trim();
            if (processedReason.length() > 500) {
                return CommonResult.error(400, "拒绝原因长度不能超过500个字符");
            }

            // 调用Service层审核拒绝
            archiveService.rejectArchive(id, processedReason);

            log.info("审核拒绝成功: archiveId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("审核拒绝参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("审核拒绝业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("审核拒绝系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 归档处理接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID的有效性
     * 2. 权限检查：确保用户具有档案管理权限
     * 3. 状态检查：只有已审核状态的档案可以归档
     * 4. 完整性检查：确保档案信息完整无误
     * 5. 状态变更：将档案状态更新为已归档
     * 6. 数据保护：归档后的档案不允许修改
     * 7. 存储优化：将归档数据迁移到历史表
     *
     * 归档业务意义：
     * 1. 数据保护：归档状态的档案受到严格保护，防止误操作
     * 2. 性能优化：将历史数据分离，提高活跃数据的查询性能
     * 3. 合规要求：满足档案管理的法律法规要求
     * 4. 存储管理：优化存储空间使用，降低存储成本
     *
     * 归档策略设计：
     * 1. 自动归档：设置自动归档规则，如员工离职后自动归档
     * 2. 批量归档：支持批量归档操作，提高处理效率
     * 3. 归档恢复：提供归档数据的恢复机制
     * 4. 归档审计：记录归档操作的完整审计日志
     *
     * 技术实现考虑：
     * 1. 数据迁移：将归档数据迁移到专门的历史表
     * 2. 索引优化：为归档表建立合适的索引
     * 3. 备份策略：制定归档数据的备份策略
     * 4. 查询支持：提供归档数据的查询接口
     *
     * @param id 档案ID，必须为正整数
     * @return 归档成功返回true，失败返回错误信息
     */
    @Operation(summary = "归档", description = "将已审核的档案进行归档")
    @PostMapping("/archive")
    public CommonResult<Boolean> archiveRecord(@Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id) {
        log.info("归档请求: archiveId={}", id);

        try {
            // 调用Service层归档
            archiveService.archiveRecord(id);

            log.info("归档成功: archiveId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("归档参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("归档业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("归档系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 锁定档案接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID和锁定原因的有效性
     * 2. 权限检查：确保用户具有档案管理权限
     * 3. 状态检查：确认档案当前状态允许锁定
     * 4. 锁定记录：记录锁定时间、操作人、锁定原因
     * 5. 状态变更：将档案状态更新为已锁定
     * 6. 权限限制：锁定后的档案禁止任何修改操作
     * 7. 通知发送：向相关人员发送锁定通知
     *
     * 锁定应用场景：
     * 1. 法律纠纷：涉及法律纠纷的档案需要锁定保护
     * 2. 调查期间：纪律调查或审计期间锁定相关档案
     * 3. 数据保护：防止重要档案被误操作或恶意修改
     * 4. 合规要求：满足特定法规对档案保护的要求
     * 5. 系统维护：系统维护期间临时锁定档案
     *
     * 锁定权限控制：
     * 1. 高级权限：只有高级管理员才能执行锁定操作
     * 2. 审批流程：重要档案的锁定需要经过审批
     * 3. 时限控制：设置锁定的有效期限
     * 4. 解锁权限：明确解锁的权限和流程
     *
     * 锁定影响分析：
     * 1. 操作限制：锁定后禁止所有修改操作
     * 2. 查询影响：查询功能不受影响，但会显示锁定状态
     * 3. 流程阻断：相关业务流程可能被阻断
     * 4. 系统集成：需要通知其他系统档案锁定状态
     *
     * @param id 档案ID，必须为正整数
     * @param reason 锁定原因，用于记录锁定的具体原因
     * @return 锁定成功返回true，失败返回错误信息
     */
    @Operation(summary = "锁定档案", description = "锁定档案，防止修改")
    @PostMapping("/lock")
    public CommonResult<Boolean> lockArchive(
            @Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id,
            @Parameter(description = "锁定原因") @RequestParam(value = "reason", required = false) String reason) {

        log.info("锁定档案请求: archiveId={}, reason={}", id, reason);

        try {
            // 处理锁定原因参数
            String processedReason = org.springframework.util.StringUtils.hasText(reason) ?
                    reason.trim() : "管理员锁定";

            // 调用Service层锁定档案
            archiveService.lockArchive(id, processedReason);

            log.info("锁定档案成功: archiveId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("锁定档案参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("锁定档案业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("锁定档案系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 解锁档案接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证档案ID和解锁原因的有效性
     * 2. 权限检查：确保用户具有档案管理权限
     * 3. 状态检查：只有已锁定状态的档案可以解锁
     * 4. 解锁记录：记录解锁时间、操作人、解锁原因
     * 5. 状态恢复：将档案状态恢复到锁定前的状态
     * 6. 权限恢复：恢复档案的正常操作权限
     * 7. 通知发送：向相关人员发送解锁通知
     *
     * 解锁应用场景：
     * 1. 纠纷解决：法律纠纷解决后解锁相关档案
     * 2. 调查结束：调查或审计结束后解锁档案
     * 3. 误操作恢复：误锁定档案的紧急恢复
     * 4. 维护完成：系统维护完成后解锁档案
     * 5. 期限到期：锁定期限到期后的自动解锁
     *
     * 解锁权限控制：
     * 1. 对等权限：解锁权限应不低于锁定权限
     * 2. 审批流程：重要档案的解锁需要经过审批
     * 3. 原因记录：必须记录详细的解锁原因
     * 4. 操作审计：完整记录解锁操作的审计日志
     *
     * 解锁后处理：
     * 1. 状态恢复：恢复到锁定前的业务状态
     * 2. 流程重启：重新启动被阻断的业务流程
     * 3. 权限检查：重新验证用户的操作权限
     * 4. 数据同步：向其他系统同步解锁状态
     *
     * 安全考虑：
     * 1. 操作记录：详细记录解锁操作的所有信息
     * 2. 权限验证：严格验证解锁操作的权限
     * 3. 异常监控：监控异常的解锁操作
     * 4. 恢复机制：提供解锁操作的回滚机制
     *
     * @param id 档案ID，必须为正整数
     * @param reason 解锁原因，用于记录解锁的具体原因
     * @return 解锁成功返回true，失败返回错误信息
     */
    @Operation(summary = "解锁档案", description = "解锁档案，允许修改")
    @PostMapping("/unlock")
    public CommonResult<Boolean> unlockArchive(
            @Parameter(description = "档案ID", required = true) @RequestParam("id") @NotNull Long id,
            @Parameter(description = "解锁原因") @RequestParam(value = "reason", required = false) String reason) {

        log.info("解锁档案请求: archiveId={}, reason={}", id, reason);

        try {
            // 处理解锁原因参数
            String processedReason = org.springframework.util.StringUtils.hasText(reason) ?
                    reason.trim() : "管理员解锁";

            // 调用Service层解锁档案
            archiveService.unlockArchive(id, processedReason);

            log.info("解锁档案成功: archiveId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("解锁档案参数错误: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("解锁档案业务异常: archiveId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("解锁档案系统异常: archiveId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }


    // ========== 档案导入导出 ==========

    /**
     * 批量导入档案接口
     *
     * 业务流程说明：
     * 1. 文件验证：验证上传文件的格式和大小
     * 2. 权限检查：确保用户具有档案导入权限
     * 3. 数据解析：解析Excel文件中的档案数据
     * 4. 数据验证：验证导入数据的完整性和正确性
     * 5. 重复检查：检查重复数据并提供处理策略
     * 6. 批量处理：执行批量导入操作
     * 7. 结果报告：生成详细的导入结果报告
     *
     * 导入功能设计：
     * 1. 文件格式支持：
     *    - Excel格式：.xlsx, .xls
     *    - CSV格式：.csv
     *    - 模板下载：提供标准导入模板
     * 2. 数据验证规则：
     *    - 必填字段验证：确保关键字段不为空
     *    - 格式验证：验证日期、邮箱、手机号等格式
     *    - 业务规则验证：验证业务逻辑的合理性
     *    - 重复性检查：检查用户ID、档案编号等唯一性
     * 3. 错误处理策略：
     *    - 全部成功：所有数据都导入成功
     *    - 部分成功：部分数据导入成功，部分失败
     *    - 全部失败：所有数据都导入失败
     *    - 错误报告：详细的错误信息和行号
     *
     * 性能优化：
     * 1. 批量处理：使用批量插入提高导入效率
     * 2. 事务控制：合理设置事务边界，确保数据一致性
     * 3. 内存管理：大文件分批处理，避免内存溢出
     * 4. 异步处理：大批量导入使用异步处理
     *
     * 安全考虑：
     * 1. 文件安全：验证文件类型，防止恶意文件上传
     * 2. 数据安全：敏感数据的加密处理
     * 3. 权限控制：严格的导入权限控制
     * 4. 操作审计：记录导入操作的详细日志
     *
     * @param file 上传的Excel文件
     * @return 导入结果，包含成功数量、失败数量和错误详情
     */
    @Operation(summary = "批量导入档案", description = "通过Excel文件批量导入档案")
    @PostMapping("/import")
    public CommonResult<Object> importArchives(
            @Parameter(description = "导入文件", required = true)
            @RequestParam("file") org.springframework.web.multipart.MultipartFile file) {

        log.info("批量导入档案请求: fileName={}, fileSize={}",
                file != null ? file.getOriginalFilename() : null,
                file != null ? file.getSize() : 0);

        try {
            // 文件基础验证
            if (file == null || file.isEmpty()) {
                return CommonResult.error(400, "导入文件不能为空");
            }

            // 文件大小验证（限制为10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return CommonResult.error(400, "导入文件大小不能超过10MB");
            }

            // 文件格式验证
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls") && !fileName.endsWith(".csv"))) {
                return CommonResult.error(400, "导入文件格式不正确，支持.xlsx、.xls、.csv格式");
            }

            // 调用Service层执行导入
            Object result = archiveService.importArchives(file);

            log.info("批量导入档案成功: fileName={}", fileName);
            return CommonResult.success(result);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("批量导入档案参数错误: fileName={}, error={}",
                    file != null ? file.getOriginalFilename() : null, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            // 业务异常
            log.error("批量导入档案业务异常: fileName={}, error={}",
                    file != null ? file.getOriginalFilename() : null, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            // 系统异常
            log.error("批量导入档案系统异常: fileName={}, error={}",
                    file != null ? file.getOriginalFilename() : null, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 导出档案接口
     *
     * 业务流程说明：
     * 1. 参数验证：验证导出条件和参数的有效性
     * 2. 权限检查：确保用户具有档案导出权限
     * 3. 数据查询：根据导出条件查询档案数据
     * 4. 权限过滤：根据用户权限过滤导出数据
     * 5. 数据脱敏：对敏感信息进行脱敏处理
     * 6. 文件生成：生成Excel文件并设置响应头
     * 7. 文件下载：将文件流写入响应输出流
     *
     * 导出功能设计：
     * 1. 导出格式支持：
     *    - Excel格式：.xlsx（推荐，支持大数据量）
     *    - CSV格式：.csv（轻量级，兼容性好）
     *    - PDF格式：.pdf（适合打印和存档）
     * 2. 导出内容控制：
     *    - 字段选择：支持选择性导出指定字段
     *    - 条件过滤：支持按条件过滤导出数据
     *    - 数据范围：支持全量导出和分页导出
     * 3. 数据安全处理：
     *    - 敏感信息脱敏：身份证号、银行卡号等
     *    - 权限级别控制：不同角色导出不同详细程度的数据
     *    - 水印添加：为导出文件添加水印标识
     *
     * 性能优化：
     * 1. 分批导出：大数据量分批处理，避免内存溢出
     * 2. 异步导出：超大数据量使用异步导出
     * 3. 缓存机制：相同条件的导出结果缓存
     * 4. 压缩处理：大文件自动压缩处理
     *
     * 用户体验：
     * 1. 进度提示：大数据量导出提供进度提示
     * 2. 文件命名：使用有意义的文件名
     * 3. 下载提示：提供下载完成提示
     * 4. 错误处理：友好的错误提示信息
     *
     * 审计和监控：
     * 1. 操作日志：记录导出操作的详细信息
     * 2. 数据追踪：记录导出的数据范围和内容
     * 3. 安全监控：监控异常的导出行为
     * 4. 合规检查：确保导出操作符合法规要求
     *
     * @param response HTTP响应对象，用于文件下载
     * @param archiveState 档案状态筛选条件
     * @param archiveType 档案类型筛选条件
     * @param createTimeStart 创建时间范围开始
     * @param createTimeEnd 创建时间范围结束
     * @param exportFields 导出字段列表，逗号分隔
     * @param exportFormat 导出格式：excel、csv、pdf
     */
    @Operation(summary = "导出档案", description = "导出档案数据为Excel文件")
    @GetMapping("/export")
    public void exportArchives(
            jakarta.servlet.http.HttpServletResponse response,
            @Parameter(description = "档案状态") @RequestParam(value = "archiveState", required = false) Integer archiveState,
            @Parameter(description = "档案类型") @RequestParam(value = "archiveType", required = false) Integer archiveType,
            @Parameter(description = "创建时间-开始") @RequestParam(value = "createTimeStart", required = false) String createTimeStart,
            @Parameter(description = "创建时间-结束") @RequestParam(value = "createTimeEnd", required = false) String createTimeEnd,
            @Parameter(description = "导出字段") @RequestParam(value = "exportFields", required = false) String exportFields,
            @Parameter(description = "导出格式") @RequestParam(value = "exportFormat", defaultValue = "excel") String exportFormat) {

        log.info("导出档案请求: archiveState={}, archiveType={}, exportFormat={}",
                archiveState, archiveType, exportFormat);

        try {
            // 构建导出条件
            java.util.Map<String, Object> exportConditions = new java.util.HashMap<>();

            if (archiveState != null && archiveState > 0) {
                // 验证状态码的有效性
                com.miaowen.oa.personnel.domain.enums.ArchiveState state =
                        com.miaowen.oa.personnel.domain.enums.ArchiveState.getByCode(archiveState);
                if (state != null) {
                    exportConditions.put("archiveState", archiveState);
                } else {
                    log.warn("导出时发现无效的档案状态码: {}", archiveState);
                }
            }

            if (archiveType != null && archiveType > 0) {
                exportConditions.put("archiveType", archiveType);
            }

            if (org.springframework.util.StringUtils.hasText(createTimeStart)) {
                exportConditions.put("createTimeStart", createTimeStart.trim());
            }

            if (org.springframework.util.StringUtils.hasText(createTimeEnd)) {
                exportConditions.put("createTimeEnd", createTimeEnd.trim());
            }

            if (org.springframework.util.StringUtils.hasText(exportFields)) {
                exportConditions.put("exportFields", exportFields.trim());
            }

            // 验证导出格式
            exportFormat = org.springframework.util.StringUtils.hasText(exportFormat) ?
                    exportFormat.trim().toLowerCase() : "excel";
            if (!java.util.Arrays.asList("excel", "csv", "pdf").contains(exportFormat)) {
                exportFormat = "excel";
            }
            exportConditions.put("exportFormat", exportFormat);

            // 设置响应头
            String fileName = "档案数据_" + java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

            switch (exportFormat) {
                case "csv":
                    response.setContentType("text/csv;charset=UTF-8");
                    response.setHeader("Content-Disposition",
                            "attachment; filename=\"" + fileName + ".csv\"");
                    break;
                case "pdf":
                    response.setContentType("application/pdf");
                    response.setHeader("Content-Disposition",
                            "attachment; filename=\"" + fileName + ".pdf\"");
                    break;
                default:
                    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    response.setHeader("Content-Disposition",
                            "attachment; filename=\"" + fileName + ".xlsx\"");
                    break;
            }

            // 调用Service层执行导出
            archiveService.exportArchives(exportConditions);

            log.info("导出档案成功: exportFormat={}, fileName={}", exportFormat, fileName);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("导出档案参数错误: error={}", e.getMessage());
            try {
                response.setStatus(400);
                response.getWriter().write("参数错误：" + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }

        } catch (RuntimeException e) {
            // 业务异常
            log.error("导出档案业务异常: error={}", e.getMessage());
            try {
                response.setStatus(500);
                response.getWriter().write("业务异常：" + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }

        } catch (Exception e) {
            // 系统异常
            log.error("导出档案系统异常: error={}", e.getMessage(), e);
            try {
                response.setStatus(500);
                response.getWriter().write("系统异常，请稍后重试");
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }
}

package com.miaowen.oa.personnel.domain.service.impl;

import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.personnel.domain.service.OaArchiveSalaryAdjustmentService;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveEntity;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveSalaryAdjustmentEntity;
import com.miaowen.oa.personnel.infrastructure.mapper.OaArchiveMapper;
import com.miaowen.oa.personnel.infrastructure.mapper.OaArchiveSalaryAdjustmentMapper;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractCreateReq;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractReq;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentCreateReq;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentReq;
import com.miaowen.oa.personnel.interfaces.resp.SalaryAdjustmentResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 档案薪资调整服务实现类
 *
 * <AUTHOR>
 * @company 武汉市妙闻网络科技有限公司
 * @since 2025-07-28
 */

@Slf4j
@Service
public class OaArchiveSalaryAdjustmentServiceImpl implements OaArchiveSalaryAdjustmentService {

    @Resource
    private OaArchiveSalaryAdjustmentMapper salaryAdjustmentMapper;
    @Resource
    private OaArchiveMapper archiveMapper;

    // ========================= 方法注释 =========================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSalaryAdjustment(SalaryAdjustmentCreateReq salaryAdjustmentCreateReq) {
        if (CollectionUtils.isEmpty(salaryAdjustmentCreateReq.getList())) {
            return;
        }
        for (SalaryAdjustmentReq salaryAdjustmentReq : salaryAdjustmentCreateReq.getList()) {



            if (salaryAdjustmentReq.getId() == null) {
                // 新增
                SalaryAdjustmentCreateReq createReq = new SalaryAdjustmentCreateReq();
                createReq.setUserId(salaryAdjustmentCreateReq.getUserId());
                createReq.setList(Collections.singletonList(salaryAdjustmentReq));
                createSalaryAdjustment(createReq);
            } else {
                // 更新
                SalaryAdjustmentCreateReq updateReq = new SalaryAdjustmentCreateReq();
                updateReq.setUserId(salaryAdjustmentCreateReq.getUserId());
                updateReq.setList(Collections.singletonList(salaryAdjustmentReq));
                updateSalaryAdjustment(updateReq);
            }
        }
    }

    /**
     * 创建薪资调整
     *
     * 实现原因：
     * - 采用参数对象（DTO）接收，便于扩展和参数校，避免参数过多导致方法签名混乱。
     * - 通过MyBatis-Plus的insert方法持久化，保证线程安全和性能。
     * - 采用BeanUtils进行DTO与Entity转换，减少重复代码。
     * - 业务逻辑全部在Service层实现，Controller无状态，便于单元测试和扩展。
     *
     * 参数注意事项：
     * - createReq不能为空，archiveId必须存在。
     * - 需校验档案是否存在，防止脏数据。
     * - afterTotalMonthlySalary等关键字段需保证非负。
     *
     * 技术标准/依赖：
     * - BeanUtils：https://github.com/yaooqinn/beanutils
     * - MyBatis-Plus：https://baomidou.com/
     *
     * 架构关系：
     * - 本方法为Service层，负责业务逻辑处理，调用Mapper行数据持久化。
     * - 逻辑流程：参数校验->档案校验->DTO转Entity->插入->更新档案->返回ID。
     */
    @Override
    public void createSalaryAdjustment(SalaryAdjustmentCreateReq createReq) {
        if (createReq == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        if (CollectionUtils.isEmpty(createReq.getList())){
            return;
        }

        for (SalaryAdjustmentReq salaryAdjustmentReq : createReq.getList()) {
            OaArchiveEntity archive = archiveMapper.selectById(createReq.getArchiveId());
            if (archive == null) {
                throw new IllegalArgumentException("档案不存在，archiveId=" + createReq.getArchiveId());
            }
            OaArchiveSalaryAdjustmentEntity entity = BeanUtils.toBean(salaryAdjustmentReq, OaArchiveSalaryAdjustmentEntity.class);
            entity.setId(null);
            entity.setArchiveId(archive.getId());
            entity.setUserId(createReq.getUserId());
            salaryAdjustmentMapper.insert(entity);
            archive.setCurrentSalary(salaryAdjustmentReq.getAfterTotalMonthlySalary());
            archive.setSalaryAdjustmentCount((archive.getSalaryAdjustmentCount() == null ? 0 : archive.getSalaryAdjustmentCount()) + 1);
            archive.setLastSalaryAdjustmentDate(salaryAdjustmentReq.getEffectiveDate());
            archiveMapper.updateById(archive);
        }
    }

    /**
     * 更新薪资调整
     *
     * 实现原因：
     * - 采用参数对象（DTO）接收，便于扩展和参数校验。
     * - 通过MyBatis-Plus的updateById方法，保证线程安全和性能。
     * - 采用BeanUtils进行DTO与Entity转换，减少重复代码。
     *
     * 参数注意事项：
     * - updateReq及其id不能为空。
     * - 需校验原数据是否存在，防止脏数据。
     * - afterTotalMonthlySalary变更时需同步更新档案。
     *
     * 技术标准/依赖：
     * - BeanUtils：https://github.com/yaooqinn/beanutils
     * - MyBatis-Plus：https://baomidou.com/
     *
     * 架构关系：
     * - 本方法为Service层，负责业务逻辑处理，调用Mapper进行数据持久化。
     * - 逻辑流程：参数校验->原数据校验->DTO转Entity->更新->如有必要同步档案。
     */
    @Override
    public void updateSalaryAdjustment(SalaryAdjustmentCreateReq updateReq) {



        if (updateReq == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        if (CollectionUtils.isEmpty(updateReq.getList())){
            return;
        }

        for (SalaryAdjustmentReq salaryAdjustmentReq : updateReq.getList()) {
            OaArchiveSalaryAdjustmentEntity old = salaryAdjustmentMapper.selectById(salaryAdjustmentReq.getId());
            if (old == null) {
                throw new IllegalArgumentException("薪资调整不存在，id=" + salaryAdjustmentReq.getId());
            }
            OaArchiveSalaryAdjustmentEntity entity = BeanUtils.toBean(salaryAdjustmentReq, OaArchiveSalaryAdjustmentEntity.class);
            salaryAdjustmentMapper.updateById(entity);
            if (!Objects.equals(old.getAfterTotalMonthlySalary(), salaryAdjustmentReq.getAfterTotalMonthlySalary())) {
                OaArchiveEntity archive = archiveMapper.selectById(updateReq.getArchiveId());
                if (archive != null) {
                    archive.setCurrentSalary(salaryAdjustmentReq.getAfterTotalMonthlySalary());
                    archive.setLastSalaryAdjustmentDate(salaryAdjustmentReq.getEffectiveDate());
                    archiveMapper.updateById(archive);
                }
            }
        }

    }

    /**
     * 删除薪资调整
     *
     * 实现原因：
     * - 采用物理删除，保证数据一致性。
     * - 通过MyBatis-Plus的deleteById方法，线程安全、性能高。
     *
     * 参数注意事项：
     * - id不能为空。
     * - 需校验数据是否存在，防止误删。
     *
     * 技术标准/依赖：
     * - MyBatis-Plus：https://baomidou.com/
     *
     * 架构关系：
     * - 本方法为Service层，负责业务逻辑处理，调用Mapper进行数据持久化。
     * - 逻辑流程：参数校验->数据校验->删除。
     */
    @Override
    public void deleteSalaryAdjustment(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }
        OaArchiveSalaryAdjustmentEntity entity = salaryAdjustmentMapper.selectById(id);
        if (entity == null) {
            throw new IllegalArgumentException("薪资调整不存在，id=" + id);
        }
        salaryAdjustmentMapper.deleteById(id);
    }

    /**
     * 获取单条薪资调整
     *
     * 实现原因：
     * - 采用主键查询，效率高。
     * - 采用BeanUtils进行DTO与Entity转换，便于前后端解耦。
     *
     * 参数注意事项：
     * - id不能为空。
     * - 若数据不存在返回null。
     *
     * 技术标准/依赖：
     * - BeanUtils：https://github.com/yaooqinn/beanutils
     * - MyBatis-Plus：https://baomidou.com/
     *
     * 架构关系：
     * - 本方法为Service层，负责业务逻辑处理，调用Mapper进行数据持久化。
     * - 逻辑流程：参数校验->主键查询->DTO转换。
     */
    @Override
    public SalaryAdjustmentResp getSalaryAdjustment(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }
        OaArchiveSalaryAdjustmentEntity entity = salaryAdjustmentMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        return BeanUtils.toBean(entity, SalaryAdjustmentResp.class);
    }

    /**
     * 按档案ID查列表
     *
     * 实现原因：
     * - 采用档案ID查询，适配业务需求。
     * - 采用BeanUtils进行DTO与Entity转换，便于前后端解耦。
     *
     * 参数注意事项：
     * - archiveId不能为空。
     * - 若无数据返回空集合。
     *
     * 技术标准/依赖：
     * - BeanUtils：<a href="https://github.com/yaooqinn/beanutils">...</a>
     * - MyBatis-Plus：<a href="https://baomidou.com/">...</a>
     *
     * 架构关系：
     * - 本方法为Service层，负责业务逻辑处理，调用Mapper进行数据持久化。
     * - 逻辑流程：参数校验->条件查询->DTO转换。
     */
    @Override
    public List<SalaryAdjustmentResp> getSalaryAdjustmentListByArchive(Long archiveId) {
        if (archiveId == null) {
            throw new IllegalArgumentException("archiveId不能为空");
        }
        List<OaArchiveSalaryAdjustmentEntity> list = salaryAdjustmentMapper.selectByArchiveId(archiveId);
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return BeanUtils.toBean(list, SalaryAdjustmentResp.class);
    }

    /**
     * 按用户ID查列表
     *
     * 实现原因：
     * - 采用用户ID查询，适配业务需求。
     * - 采用BeanUtils进行DTO与Entity转换，便于前后端解耦。
     *
     * 参数注意事项：
     * - userId不能为空。
     * - 若无数据返回空集合。
     *
     * 技术标准/依赖：
     * - BeanUtils：https://github.com/yaooqinn/beanutils
     * - MyBatis-Plus：https://baomidou.com/
     *
     * 架构关系：
     * - 本方法为Service层，负责业务逻辑处理，调用Mapper进行数据持久化。
     * - 逻辑流程：参数校验->条件查询->DTO转换。
     */
    @Override
    public List<SalaryAdjustmentResp> getSalaryAdjustmentListByUser(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        List<OaArchiveSalaryAdjustmentEntity> list = salaryAdjustmentMapper.selectByUserId(userId);
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return BeanUtils.toBean(list, SalaryAdjustmentResp.class);
    }
}

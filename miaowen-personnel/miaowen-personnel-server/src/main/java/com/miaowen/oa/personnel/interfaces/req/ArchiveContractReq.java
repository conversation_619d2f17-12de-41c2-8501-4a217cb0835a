package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 档案合同信息DTO
 * 
 * 设计理念：
 * 1. 采用DTO模式而非直接暴露Entity的原因：
 *    - 数据传输对象(DTO)模式提供了更好的API稳定性，Entity变更不会直接影响API接口
 *    - 可以灵活控制哪些字段对外暴露，保护敏感信息
 *    - 支持字段级别的验证和转换，提供更好的数据完整性保障
 *    - 符合领域驱动设计(DDD)的分层架构原则，参考：https://martinfowler.com/eaaCatalog/dataTransferObject.html
 * 
 * 2. 验证注解选择说明：
 *    - 使用Jakarta Validation 3.0规范而非Hibernate Validator的原因：
 *      Jakarta是Java EE的标准规范，具有更好的跨框架兼容性
 *      参考：https://jakarta.ee/specifications/bean-validation/3.0/
 *    - @NotNull vs @NotEmpty vs @NotBlank的使用场景：
 *      @NotNull: 对象不能为null，但可以为空字符串
 *      @NotEmpty: 集合/数组/字符串不能为null且长度>0
 *      @NotBlank: 字符串不能为null且去除空白字符后长度>0
 * 
 * 3. 金额字段使用BigDecimal的原因：
 *    - 避免浮点数精度丢失问题，确保财务计算的准确性
 *    - 符合金融行业标准，参考：JSR 354 Money and Currency API
 *    - 支持任意精度的十进制运算
 * 
 * 4. 日期字段使用String而非LocalDate的考虑：
 *    - API接口层面使用String可以避免时区转换问题
 *    - 前端JavaScript对日期处理更友好
 *    - 支持多种日期格式的灵活解析
 *    - 在Service层进行类型转换，保持接口层的简洁性
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "档案合同信息")
public class ArchiveContractReq {

    @Schema(description = "合同ID", example = "1")
    private Long id;


    @Schema(description = "签约公司", example = "妙闻集团")
    private String companyName;


    @Schema(description = "合同名称", example = "劳动合同书")
    @Size(max = 100, message = "合同名称长度不能超过100个字符")
    private String contractName;

    /**
     * 合同类型枚举值设计：
     * 1. 使用数字枚举而非字符串枚举的原因：
     *    - 存储效率：整型占用空间更小，索引性能更好
     *    - 国际化友好：数字枚举便于多语言环境下的显示
     *    - 扩展性：新增类型不会影响现有数据结构
     * 2. 枚举值范围说明：
     *    - 1-8覆盖了常见的合同类型
     *    - 预留了扩展空间，避免频繁的数据库结构变更
     * 3. 可能的tricky问题：
     *    - 枚举值删除时需要考虑历史数据的兼容性
     *    - 前端显示时需要维护枚举值与显示名称的映射关系
     */
    @Schema(description = "合同类型", example = "1", 
            allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"},
            required = true)
    @NotNull(message = "合同类型不能为空")
    @Min(value = 1, message = "合同类型代码不能小于1")
    @Max(value = 8, message = "合同类型代码不能大于8")
    private Integer contractType;

    @Schema(description = "合同性质", example = "1", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "合同性质代码不能小于1")
    @Max(value = 3, message = "合同性质代码不能大于4")
    private Integer contractNature;

    @Schema(description = "合同状态", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7"})
    @Min(value = 1, message = "合同状态代码不能小于1")
    @Max(value = 7, message = "合同状态代码不能大于7")
    private Integer contractState = 1;

    /**
     * 日期字段验证说明：
     * 1. 使用ISO 8601日期格式(YYYY-MM-DD)的原因：
     *    - 国际标准，避免地区差异导致的解析错误
     *    - 字典序与时间序一致，便于排序和比较
     *    - 参考：https://tools.ietf.org/html/rfc3339
     * 2. 正则表达式说明：
     *    - ^\d{4}-\d{2}-\d{2}$ 确保基本格式正确
     *    - 不验证日期有效性(如2月30日)，由业务层处理
     * 3. 注意事项：
     *    - 前端传入时需要确保时区一致性
     *    - 数据库存储时建议使用DATE类型而非VARCHAR
     */
    @Schema(description = "合同开始日期", example = "2025-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "合同开始日期格式应为YYYY-MM-DD")
    private String startDate;

    @Schema(description = "合同结束日期", example = "2027-12-31")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "合同结束日期格式应为YYYY-MM-DD")
    private String endDate;


    @Schema(description = "员工签约日期", example = "2025-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "员工签约日期格式应为YYYY-MM-DD")
    private String employeeSignDate;


    // ========== 便利方法 ==========

    /**
     * 获取合同类型名称
     * 
     * 实现说明：
     * 1. 使用数组映射而非Map的原因：
     *    - 数组访问效率更高，O(1)时间复杂度
     *    - 内存占用更小，适合频繁调用的场景
     *    - 枚举值连续且数量较少，数组是最优选择
     * 2. 边界检查的重要性：
     *    - 防止数组越界异常
     *    - 处理异常数据的容错机制
     * 3. 可能的扩展方案：
     *    - 如果枚举值变得稀疏，可考虑使用EnumMap
     *    - 如果需要国际化，可结合ResourceBundle使用
     */
    public String getContractTypeName() {
        if (contractType == null) return null;
        String[] types = {"", "劳动合同", "劳务合同", "实习协议", "派遣合同", "顾问协议", "保密协议", "竞业协议", "其他"};
        return contractType > 0 && contractType < types.length ? types[contractType] : "未知类型";
    }

    /**
     * 计算合同剩余天数
     * 
     * 实现说明：
     * 1. 使用java.time包而非Date的原因：
     *    - java.time是Java 8+的现代日期时间API，线程安全且不可变
     *    - 提供了更丰富的日期计算方法
     *    - 避免了Date类的时区和精度问题
     *    - 参考：JSR 310 Date and Time API
     * 2. 异常处理策略：
     *    - 日期解析失败时返回null而非抛出异常
     *    - 调用方可以根据null值判断数据异常
     * 3. 性能考虑：
     *    - LocalDate.parse()比SimpleDateFormat性能更好
     *    - ChronoUnit.DAYS.between()是最高效的日期差值计算方法
     */
    public Integer getRemainingDays() {
        if (endDate == null) return null;
        
        try {
            java.time.LocalDate end = java.time.LocalDate.parse(endDate);
            java.time.LocalDate now = java.time.LocalDate.now();
            return (int) java.time.temporal.ChronoUnit.DAYS.between(now, end);
        } catch (Exception e) {
            // 日期格式错误时返回null，避免抛出异常影响业务流程
            return null;
        }
    }

    /**
     * 判断合同是否即将到期
     * 
     * 业务规则说明：
     * 1. 30天阈值的选择依据：
     *    - 给HR部门足够的时间准备续签工作
     *    - 符合大多数企业的管理流程
     *    - 可以通过配置文件调整，提高灵活性
     * 2. 边界条件处理：
     *    - remaining > 0 确保合同未过期
     *    - remaining <= 30 确保在提醒范围内
     * 3. 扩展建议：
     *    - 可以根据合同重要性设置不同的提醒阈值
     *    - 可以结合员工级别调整提醒策略
     */
    public boolean isExpiringSoon() {
        Integer remaining = getRemainingDays();
        return remaining != null && remaining <= 30 && remaining > 0;
    }

    /**
     * 判断合同是否已过期
     */
    public boolean isExpired() {
        Integer remaining = getRemainingDays();
        return remaining != null && remaining < 0;
    }


}

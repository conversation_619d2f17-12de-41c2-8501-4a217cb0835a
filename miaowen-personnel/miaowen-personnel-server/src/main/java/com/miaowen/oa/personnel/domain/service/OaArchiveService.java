package com.miaowen.oa.personnel.domain.service;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.interfaces.req.ArchiveBasicInfoReq;
import com.miaowen.oa.personnel.interfaces.req.ArchiveCreateReq;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveBasicInfoResp;

import java.util.List;
import java.util.Map;

/**
 * 档案管理服务接口
 * 
 * 设计理念：
 * 1. 领域服务的职责边界：
 *    - 封装复杂的业务逻辑和业务规则
 *    - 协调多个实体和值对象的交互
 *    - 提供事务边界和数据一致性保障
 *    - 参考：《领域驱动设计》- <PERSON> Evans
 * 
 * 2. 接口设计原则：
 *    - 面向接口编程，降低耦合度
 *    - 接口职责单一，便于测试和维护
 *    - 方法命名清晰，体现业务语义
 *    - 参数和返回值类型明确，避免歧义
 * 
 * 3. 业务完整性保障：
 *    - 档案创建的完整性验证
 *    - 档案状态变更的业务规则检查
 *    - 档案删除的关联数据处理
 *    - 档案查询的权限控制
 * 
 * 4. 性能和扩展性考虑：
 *    - 支持分页查询，避免大数据量问题
 *    - 提供批量操作接口，提高处理效率
 *    - 预留扩展接口，支持业务需求变化
 *    - 考虑缓存策略，提升查询性能
 * 
 * 5. 异常处理策略：
 *    - 定义明确的业务异常类型
 *    - 提供详细的错误信息和错误码
 *    - 支持异常的分类处理和恢复
 *    - 记录完整的异常上下文信息
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
public interface OaArchiveService {

    // ========== 档案基础CRUD操作 ==========

    /**
     * 创建档案
     * 
     * 业务规则：
     * 1. 用户唯一性检查：一个用户只能有一个档案
     * 2. 档案编号生成：按照ARCH-YYYY-NNNNNN格式生成
     * 3. 初始状态设置：新建档案默认为草稿状态
     * 4. 必填字段验证：确保关键信息的完整性
     * 
     * 异常情况：
     * - 用户不存在：抛出UserNotFoundException
     * - 用户已有档案：抛出ArchiveAlreadyExistsException
     * - 数据验证失败：抛出ValidationException
     * 
     * @param createReqDTO 档案创建请求DTO
     * @return 新创建的档案ID
            */
    Long saveArchive(ArchiveCreateReq createReqDTO);


    /**
     * 删除档案
     * 
     * 业务规则：
     * 1. 状态检查：只有草稿状态的档案可以删除
     * 2. 关联数据处理：级联删除相关的教育经历、工作经历等
     * 3. 权限验证：只有管理员可以删除档案
     * 4. 软删除策略：标记删除而非物理删除
     * 
     * @param id 档案ID
            */
    void deleteArchive(Long id);

    /**
     * 获取档案详情
     * 
     * 业务规则：
     * 1. 权限检查：根据用户权限返回不同详细程度的信息
     * 2. 敏感信息脱敏：根据访问者权限脱敏敏感信息
     * 3. 关联数据加载：可选择性加载关联的详细信息
     * 
     * @param id 档案ID
     * @return 档案详细信息
            */
    ArchiveBasicInfoResp getArchive(Long id);

    /**
     * 根据用户ID获取档案
     * 
     * @param userId 用户ID
     * @return 档案信息，如果不存在返回null
     */
    ArchiveBasicInfoResp getArchiveByUserId(Long userId);

    /**
     * 分页查询档案
     * 
     * 查询特性：
     * 1. 多条件组合查询：支持按状态、类型、部门等条件筛选
     * 2. 排序支持：支持按创建时间、更新时间等字段排序
     * 3. 权限过滤：根据用户权限过滤可见的档案
     * 4. 性能优化：使用索引优化查询性能
     * 
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param queryConditions 查询条件
     * @return 分页结果
     */
    PageResult<ArchiveBasicInfoResp> getArchivePage(Integer pageNo, Integer pageSize, Object queryConditions);

    // ========== 档案状态管理 ==========

    /**
     * 提交审核
     * 
     * 业务流程：
     * 1. 状态检查：只有草稿状态可以提交审核
     * 2. 完整性验证：检查必填信息是否完整
     * 3. 审核流程启动：根据档案类型启动相应的审核流程
     * 4. 通知发送：向审核人发送待审核通知
     * 
     * @param id 档案ID
            */
    void submitAudit(Long id);

    /**
     * 审核通过
     * 
     * 业务流程：
     * 1. 权限检查：验证审核人权限
     * 2. 状态变更：将档案状态更新为已审核
     * 3. 审核记录：记录审核时间、审核人、审核意见
     * 4. 后续流程：触发相关的后续业务流程
     * 
     * @param id 档案ID
     * @param comment 审核意见
            */
    void approveArchive(Long id, String comment);

    /**
     * 审核拒绝
     * 
     * 业务流程：
     * 1. 状态回退：将档案状态回退为草稿
     * 2. 拒绝原因记录：详细记录拒绝原因
     * 3. 通知发送：向档案所有者发送拒绝通知
     * 4. 修改建议：提供具体的修改建议
     * 
     * @param id 档案ID
     * @param reason 拒绝原因
            */
    void rejectArchive(Long id, String reason);

    /**
     * 归档处理
     * 
     * 业务规则：
     * 1. 状态检查：只有已审核状态可以归档
     * 2. 权限验证：只有管理员可以执行归档操作
     * 3. 数据保护：归档后的档案不允许修改
     * 4. 存储优化：将归档数据迁移到历史表
     * 
     * @param id 档案ID
            */
    void archiveRecord(Long id);

    /**
     * 锁定档案
     * 
     * 应用场景：
     * 1. 法律纠纷：涉及法律纠纷的档案需要锁定
     * 2. 调查期间：纪律调查期间锁定相关档案
     * 3. 数据保护：防止重要档案被误操作
     * 
     * @param id 档案ID
     * @param reason 锁定原因
            */
    void lockArchive(Long id, String reason);

    /**
     * 解锁档案
     * 
     * @param id 档案ID
     * @param reason 解锁原因
            */
    void unlockArchive(Long id, String reason);



    // ========== 批量操作 ==========

    /**
     * 批量创建档案
     * 
     * 应用场景：
     * 1. 新员工入职：批量为新入职员工创建档案
     * 2. 数据迁移：从其他系统批量导入档案数据
     * 3. 模板应用：基于模板批量创建档案
     * 
     * 处理策略：
     * 1. 事务控制：使用事务确保批量操作的一致性
     * 2. 错误处理：记录失败的记录和失败原因
     * 3. 性能优化：使用批量插入提高性能
     * 4. 进度反馈：提供批量操作的进度反馈
     * 
     * @param createReqDTOList 档案创建请求列表
     * @return 批量操作结果
     */
    Object batchCreateArchives(List<ArchiveBasicInfoReq> createReqDTOList);

    /**
     * 批量更新档案状态
     *
     * 批量更新说明：
     * 1. 状态转换验证：
     *    - 验证每个档案的当前状态是否允许转换到目标状态
     *    - 使用状态机规则进行批量验证
     *    - 记录验证失败的档案和原因
     * 2. 事务处理：
     *    - 使用事务确保批量操作的一致性
     *    - 支持部分成功的处理策略
     *    - 提供操作结果的详细报告
     * 3. 性能优化：
     *    - 使用批量SQL操作提高性能
     *    - 合理控制批次大小，避免长事务
     *    - 支持异步处理大批量操作
     *
     * @param ids 档案ID列表
     * @param targetState 目标状态码
     * @param reason 变更原因
     * @return 批量操作结果，包含成功数量、失败数量和详细信息
     */
    Object batchUpdateArchiveState(List<Long> ids, Integer targetState, String reason);

    // ========== 常用查询方法（基于LambdaQueryWrapper） ==========

    /**
     * 根据档案编号查询档案
     *
     * 使用LambdaQueryWrapper优化：
     * 1. 类型安全的字段引用
     * 2. 编译期检查，避免字段名错误
     * 3. 重构友好，字段重命名时自动更新
     *
     * @param archiveNumber 档案编号
     * @return 档案信息，不存在返回null
     */
    ArchiveBasicInfoReq getArchiveByNumber(String archiveNumber);

    /**
     * 根据状态查询档案列表
     *
     * @param archiveState 档案状态
     * @return 档案列表
     */
    List<ArchiveBasicInfoReq> getArchivesByState(Integer archiveState);

    /**
     * 根据类型查询档案列表
     *
     * @param archiveType 档案类型
     * @return 档案列表
     */
    List<ArchiveBasicInfoReq> getArchivesByType(Integer archiveType);

    /**
     * 统计档案总数
     *
     * @return 档案总数
     */
    Long countTotalArchives();

    /**
     * 按状态统计档案数量
     *
     * @return 状态统计结果
     */
    Map<Integer, Long> countArchivesByState();

    /**
     * 按类型统计档案数量
     *
     * @return 类型统计结果
     */
    Map<Integer, Long> countArchivesByType();

    // ========== 数据导入导出 ==========

    /**
     * 导出档案数据
     * 
     * 导出功能：
     * 1. 格式支持：支持Excel、CSV等多种格式
     * 2. 字段选择：支持选择性导出指定字段
     * 3. 条件过滤：支持按条件过滤导出数据
     * 4. 权限控制：根据用户权限控制导出内容
     * 
     * @param exportConditions 导出条件
     * @return 导出文件信息
     */
    Object exportArchives(Object exportConditions);

    /**
     * 导入档案数据
     * 
     * 导入功能：
     * 1. 格式验证：验证导入文件的格式和结构
     * 2. 数据校验：校验导入数据的完整性和正确性
     * 3. 重复检查：检查重复数据并提供处理策略
     * 4. 错误报告：生成详细的导入错误报告
     * 
     * @param importFile 导入文件
     * @return 导入结果
     */
    Object importArchives(Object importFile);
}

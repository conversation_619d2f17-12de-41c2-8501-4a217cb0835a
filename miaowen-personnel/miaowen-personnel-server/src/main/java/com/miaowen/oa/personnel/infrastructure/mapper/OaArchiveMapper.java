package com.miaowen.oa.personnel.infrastructure.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 档案数据访问层接口
 *
 * 设计理念：
 * 1. MyBatis-Plus LambdaQueryWrapper优势：
 *    - 继承BaseMapper获得基础CRUD操作，减少重复代码
 *    - 使用LambdaQueryWrapper提供类型安全的查询条件构建
 *    - 避免硬编码字段名，支持重构时的自动更新
 *    - 提供链式调用，代码更加简洁和可读
 *    - 参考：MyBatis-Plus官方文档 https://baomidou.com/
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Mapper
public interface OaArchiveMapper extends BaseMapper<OaArchiveEntity> {

    /**
     * 按状态统计档案数量
     *
     * @return 状态统计结果列表
     */
    @Select("SELECT archive_state, COUNT(*) as count FROM oa_archive WHERE delete_time = 0 GROUP BY archive_state")
    List<Map<String, Object>> countByState();

    /**
     * 按类型统计档案数量
     *
     * @return 类型统计结果列表
     */
    @Select("SELECT archive_type, COUNT(*) as count FROM oa_archive WHERE delete_time = 0 GROUP BY archive_type")
    List<Map<String, Object>> countByType();

}

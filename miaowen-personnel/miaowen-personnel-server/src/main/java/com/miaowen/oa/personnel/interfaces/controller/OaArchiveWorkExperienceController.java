package com.miaowen.oa.personnel.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveWorkExperienceService;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentCreateReq;
import com.miaowen.oa.personnel.interfaces.req.WorkExperienceCreateReq;
import com.miaowen.oa.personnel.interfaces.req.WorkExperienceReq;
import com.miaowen.oa.personnel.interfaces.resp.WorkExperienceResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 档案工作经历管理 RESTful API
 * 提供工作经历的增删改查、背景调查、统计分析等功能
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "工作经历管理", description = "员工工作经历的详细管理功能")
@RestController
@RequestMapping("/personnel/archive/work-experience")
@Validated
@Slf4j
public class OaArchiveWorkExperienceController {

    private final OaArchiveWorkExperienceService workExperienceService;

    public OaArchiveWorkExperienceController(OaArchiveWorkExperienceService workExperienceService) {
        this.workExperienceService = workExperienceService;
    }

    // ========== 工作经历基础CRUD操作 ==========


    @Operation(summary = "编辑工作经历")
    @PostMapping("/save")
    public CommonResult<Boolean> saveContract(
            @Validated @RequestBody WorkExperienceCreateReq updateReq) {
        workExperienceService.saveWorkExperience(updateReq);
        return CommonResult.success(true);
    }



    /**
     * 添加工作经历
     * 
     * 选择当前实现的原因：
     * 1. 采用RESTful风格，符合HTTP语义，便于前端调用和API文档生成
     * 2. 使用@Valid注解进行参数校验，确保数据完整性
     * 3. 采用统一的异常处理机制，提供友好的错误信息
     * 4. 使用日志记录关键操作，便于问题排查和审计
     * 
     * 参数注意事项：
     * - archiveId: 必须为有效的档案ID，且该档案必须存在
     * - companyName: 公司名称不能为空，长度限制在100字符以内
     * - startDate: 入职时间格式为YYYY-MM，且不能晚于当前时间
     * - endDate: 离职时间格式为YYYY-MM，且不能早于入职时间
     * - isCurrentJob: 当为true时，endDate可以为空
     * 
     * 技术标准参考：
     * - HTTP状态码规范：https://tools.ietf.org/html/rfc7231#section-6
     * - RESTful API设计：https://restfulapi.net/
     */
    @Operation(summary = "添加工作经历", description = "为档案添加新的工作经历记录")
    @PostMapping("/create")
    public CommonResult<Void> createWorkExperience(@Valid @RequestBody WorkExperienceCreateReq createReqDTO) {
        log.info("添加工作经历请求: archiveId={}", createReqDTO.getArchiveId());

        try {
            workExperienceService.createWorkExperience(createReqDTO);
            log.info("工作经历添加成功: archiveId={}", createReqDTO.getArchiveId());
            return CommonResult.success();

        } catch (IllegalArgumentException e) {
            log.warn("工作经历添加参数错误: archiveId={}, error={}", createReqDTO.getArchiveId(), e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            log.error("工作经历添加业务异常: archiveId={}, error={}", createReqDTO.getArchiveId(), e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            log.error("工作经历添加系统异常: archiveId={}, error={}", createReqDTO.getArchiveId(), e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 更新工作经历
     * 
     * 选择当前实现的原因：
     * 1. 使用PUT方法符合RESTful规范，表示幂等性更新操作
     * 2. 采用乐观锁机制防止并发更新冲突
     * 3. 支持部分字段更新，提高接口灵活性
     * 
     * 参数注意事项：
     * - id: 工作经历ID必须存在且有效
     * - 更新时的时间字段需要验证逻辑一致性
     * - 薪资信息更新需要权限控制
     */
    @Operation(summary = "更新工作经历", description = "更新工作经历信息")
    @PutMapping("/update")
    public CommonResult<Boolean> updateWorkExperience(@Valid @RequestBody WorkExperienceCreateReq updateReqDTO) {

        try {
            workExperienceService.updateWorkExperience(updateReqDTO);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            log.warn("工作经历更新参数错误: error={}", e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            log.error("工作经历更新业务异常: error={}", e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            log.error("工作经历更新系统异常: error={}",e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 删除工作经历
     * 
     * 选择当前实现的原因：
     * 1. 使用DELETE方法符合RESTful规范
     * 2. 采用软删除机制，保留数据完整性
     * 3. 删除前进行权限和业务规则校验
     * 
     * 参数注意事项：
     * - id: 工作经历ID必须存在
     * - 删除操作不可逆，需要二次确认
     * - 关联数据需要同步处理
     */
    @Operation(summary = "删除工作经历", description = "删除指定的工作经历记录")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteWorkExperience(@Parameter(description = "工作经历ID", required = true) @RequestParam("id") @NotNull Long id) {
        log.info("删除工作经历请求: workExperienceId={}", id);

        try {
            workExperienceService.deleteWorkExperience(id);
            log.info("工作经历删除成功: workExperienceId={}", id);
            return CommonResult.success(true);

        } catch (IllegalArgumentException e) {
            log.warn("工作经历删除参数错误: workExperienceId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            log.error("工作经历删除业务异常: workExperienceId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            log.error("工作经历删除系统异常: workExperienceId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 获取工作经历详情
     * 
     * 选择当前实现的原因：
     * 1. 使用GET方法符合RESTful规范
     * 2. 支持缓存机制提高查询性能
     * 3. 返回完整的工作经历信息
     * 
     * 参数注意事项：
     * - id: 工作经历ID必须存在
     * - 需要权限控制，只能查看有权限的档案
     */
    @Operation(summary = "获取工作经历详情", description = "根据ID获取工作经历详细信息")
    @GetMapping("/get")
    public CommonResult<WorkExperienceResp> getWorkExperience(@Parameter(description = "工作经历ID", required = true) @RequestParam("id") @NotNull Long id) {
        log.info("获取工作经历详情请求: workExperienceId={}", id);

        try {
            WorkExperienceResp workExperience = workExperienceService.getWorkExperience(id);

            if (workExperience == null) {
                log.warn("工作经历不存在: workExperienceId={}", id);
                return CommonResult.error(404, "工作经历不存在");
            }

            log.info("获取工作经历详情成功: workExperienceId={}", id);
            return CommonResult.success(workExperience);

        } catch (IllegalArgumentException e) {
            log.warn("获取工作经历详情参数错误: workExperienceId={}, error={}", id, e.getMessage());
            return CommonResult.error(400, e.getMessage());

        } catch (RuntimeException e) {
            log.error("获取工作经历详情业务异常: workExperienceId={}, error={}", id, e.getMessage());
            return CommonResult.error(500, e.getMessage());

        } catch (Exception e) {
            log.error("获取工作经历详情系统异常: workExperienceId={}, error={}", id, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 获取档案的工作经历列表
     * 
     * 选择当前实现的原因：
     * 1. 支持分页查询，提高大数据量场景下的性能
     * 2. 支持多种排序方式，满足不同业务需求
     * 3. 支持条件筛选，提高查询精确度
     * 
     * 参数注意事项：
     * - archiveId: 档案ID必须存在
     * - 分页参数需要验证合理性
     * - 排序字段需要白名单控制
     */
    @Operation(summary = "获取档案的工作经历列表", description = "获取指定档案的所有工作经历")
    @GetMapping("/list-by-archive")
    public CommonResult<List<WorkExperienceResp>> getWorkExperienceListByArchive(
            @Parameter(description = "档案ID", required = true) @RequestParam("archiveId") @NotNull Long archiveId) {
        
        log.info("获取档案工作经历列表请求: archiveId={}", archiveId);
        
        try {
            List<WorkExperienceResp> workExperienceList = workExperienceService.getWorkExperienceListByArchive(archiveId);
            log.info("获取档案工作经历列表成功: archiveId={}, count={}", archiveId, workExperienceList.size());
            return CommonResult.success(workExperienceList);
            
        } catch (IllegalArgumentException e) {
            log.warn("获取档案工作经历列表参数错误: archiveId={}, error={}", archiveId, e.getMessage());
            return CommonResult.error(400, e.getMessage());
            
        } catch (RuntimeException e) {
            log.error("获取档案工作经历列表业务异常: archiveId={}, error={}", archiveId, e.getMessage());
            return CommonResult.error(500, e.getMessage());
            
        } catch (Exception e) {
            log.error("获取档案工作经历列表系统异常: archiveId={}, error={}", archiveId, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 获取用户的工作经历列表
     * 
     * 选择当前实现的原因：
     * 1. 支持按用户维度查询，便于用户档案管理
     * 2. 返回按时间倒序排列的工作经历
     * 3. 支持权限控制，只能查看有权限的用户
     * 
     * 参数注意事项：
     * - userId: 用户ID必须存在
     * - 需要验证用户权限
     */
    @Operation(summary = "获取用户的工作经历列表", description = "获取指定用户的所有工作经历")
    @GetMapping("/list-by-user")
    public CommonResult<List<WorkExperienceResp>> getWorkExperienceListByUser(
            @Parameter(description = "用户ID", required = true) @RequestParam("userId") @NotNull Long userId) {
        
        log.info("获取用户工作经历列表请求: userId={}", userId);
        
        try {
            List<WorkExperienceResp> workExperienceList = workExperienceService.getWorkExperienceListByUser(userId);
            log.info("获取用户工作经历列表成功: userId={}, count={}", userId, workExperienceList.size());
            return CommonResult.success(workExperienceList);
            
        } catch (IllegalArgumentException e) {
            log.warn("获取用户工作经历列表参数错误: userId={}, error={}", userId, e.getMessage());
            return CommonResult.error(400, e.getMessage());
            
        } catch (RuntimeException e) {
            log.error("获取用户工作经历列表业务异常: userId={}, error={}", userId, e.getMessage());
            return CommonResult.error(500, e.getMessage());
            
        } catch (Exception e) {
            log.error("获取用户工作经历列表系统异常: userId={}, error={}", userId, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 分页查询工作经历
     * 
     * 选择当前实现的原因：
     * 1. 支持复杂查询条件，满足高级搜索需求
     * 2. 采用分页机制，提高大数据量场景下的性能
     * 3. 支持多字段排序，提供灵活的展示方式
     * 
     * 参数注意事项：
     * - 查询条件需要防SQL注入
     * - 分页参数需要验证合理性
     * - 排序字段需要白名单控制
     */
    @Operation(summary = "分页查询工作经历", description = "支持多条件分页查询工作经历")
    @GetMapping("/page")
    public CommonResult<PageResult<WorkExperienceResp>> getWorkExperiencePage(
            @Parameter(description = "档案ID") @RequestParam(value = "archiveId", required = false) Long archiveId,
            @Parameter(description = "用户ID") @RequestParam(value = "userId", required = false) Long userId,
            @Parameter(description = "公司名称") @RequestParam(value = "companyName", required = false) String companyName,
            @Parameter(description = "职位名称") @RequestParam(value = "position", required = false) String position,
            @Parameter(description = "是否在职") @RequestParam(value = "isCurrentJob", required = false) Integer isCurrentJob,
            @Parameter(description = "页码", required = true) @RequestParam("pageNo") @NotNull Integer pageNo,
            @Parameter(description = "每页大小", required = true) @RequestParam("pageSize") @NotNull Integer pageSize) {
        
        log.info("分页查询工作经历请求: archiveId={}, userId={}, companyName={}, position={}, isCurrentJob={}, pageNo={}, pageSize={}", 
                archiveId, userId, companyName, position, isCurrentJob, pageNo, pageSize);
        
        try {
            PageResult<WorkExperienceResp> pageResult = workExperienceService.getWorkExperiencePage(
                    archiveId, userId, companyName, position, isCurrentJob, pageNo, pageSize);
            log.info("分页查询工作经历成功: total={}, pageNo={}, pageSize={}", 
                    pageResult.getTotal(), pageNo, pageSize);
            return CommonResult.success(pageResult);
            
        } catch (IllegalArgumentException e) {
            log.warn("分页查询工作经历参数错误: error={}", e.getMessage());
            return CommonResult.error(400, e.getMessage());
            
        } catch (RuntimeException e) {
            log.error("分页查询工作经历业务异常: error={}", e.getMessage());
            return CommonResult.error(500, e.getMessage());
            
        } catch (Exception e) {
            log.error("分页查询工作经历系统异常: error={}", e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }

    /**
     * 批量删除工作经历
     * 
     * 选择当前实现的原因：
     * 1. 支持批量操作，提高操作效率
     * 2. 采用事务机制，保证数据一致性
     * 3. 提供详细的操作结果反馈
     * 
     * 参数注意事项：
     * - ids: ID列表不能为空，且每个ID必须有效
     * - 批量操作需要控制数量上限
     * - 需要权限验证
     */
    @Operation(summary = "批量删除工作经历", description = "批量删除指定的工作经历记录")
    @DeleteMapping("/batch-delete")
    public CommonResult<Boolean> batchDeleteWorkExperience(
            @Parameter(description = "工作经历ID列表", required = true) @RequestParam("ids") @NotNull List<Long> ids) {
        
        log.info("批量删除工作经历请求: ids={}", ids);
        
        try {
            workExperienceService.batchDeleteWorkExperience(ids);
            log.info("批量删除工作经历成功: ids={}", ids);
            return CommonResult.success(true);
            
        } catch (IllegalArgumentException e) {
            log.warn("批量删除工作经历参数错误: ids={}, error={}", ids, e.getMessage());
            return CommonResult.error(400, e.getMessage());
            
        } catch (RuntimeException e) {
            log.error("批量删除工作经历业务异常: ids={}, error={}", ids, e.getMessage());
            return CommonResult.error(500, e.getMessage());
            
        } catch (Exception e) {
            log.error("批量删除工作经历系统异常: ids={}, error={}", ids, e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }
}

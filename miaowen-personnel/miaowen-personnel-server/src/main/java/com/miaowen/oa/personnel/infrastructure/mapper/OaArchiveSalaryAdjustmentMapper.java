package com.miaowen.oa.personnel.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveSalaryAdjustmentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 档案薪资调整信息Mapper接口
 *
 * <AUTHOR>
 * @company 武汉市妙闻网络科技有限公司
 * @since 2025-07-28
 */
@Mapper
public interface OaArchiveSalaryAdjustmentMapper extends BaseMapper<OaArchiveSalaryAdjustmentEntity> {

    /**
     * 根据档案ID查询薪资调整列表
     *
     * @param archiveId 档案ID
     * @return 薪资调整列表
     */
    List<OaArchiveSalaryAdjustmentEntity> selectByArchiveId(@Param("archiveId") Long archiveId);

    /**
     * 根据用户ID查询薪资调整列表
     *
     * @param userId 用户ID
     * @return 薪资调整列表
     */
    List<OaArchiveSalaryAdjustmentEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 分页查询薪资调整
     *
     * @param page       分页对象
     * @param conditions 查询条件
     * @return 分页结果
     */
    IPage<OaArchiveSalaryAdjustmentEntity> selectPageByConditions(IPage<OaArchiveSalaryAdjustmentEntity> page, @Param("conditions") Map<String, Object> conditions);

    /**
     * 查询待审批的薪资调整
     *
     * @return 待审批的薪资调整列表
     */
    List<OaArchiveSalaryAdjustmentEntity> selectPendingApprovals();

    /**
     * 查询已审批的薪资调整
     *
     * @param archiveId 档案ID
     * @return 已审批的薪资调整列表
     */
    List<OaArchiveSalaryAdjustmentEntity> selectApprovedAdjustments(@Param("archiveId") Long archiveId);

    /**
     * 查询最新的薪资调整
     *
     * @param archiveId 档案ID
     * @return 最新的薪资调整
     */
    OaArchiveSalaryAdjustmentEntity selectLatestAdjustment(@Param("archiveId") Long archiveId);

    /**
     * 薪资调整类型统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectAdjustmentTypeStatistics();

    /**
     * 薪资调整状态统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectAdjustmentStatusStatistics();

    /**
     * 薪资调整趋势分析
     *
     * @param year 年份
     * @return 趋势分析结果
     */
    List<Map<String, Object>> selectSalaryTrendAnalysis(@Param("year") Integer year);

    /**
     * 批量插入薪资调整
     *
     * @param list 薪资调整列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<OaArchiveSalaryAdjustmentEntity> list);

    /**
     * 软删除薪资调整
     *
     * @param id 薪资调整ID
     * @return 删除数量
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 批量软删除薪资调整
     *
     * @param ids 薪资调整ID列表
     * @return 删除数量
     */
    int batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 更新审批状态
     *
     * @param id             薪资调整ID
     * @param approvalStatus 审批状态
     * @param approvalNote   审批备注
     * @param approverId     审批人ID
     * @param approvalTime   审批时间
     * @return 更新数量
     */
    int updateApprovalStatus(@Param("id") Long id, @Param("approvalStatus") Integer approvalStatus, @Param("approvalNote") String approvalNote, @Param("approverId") Long approverId, @Param("approvalTime") LocalDate approvalTime);

    /**
     * 更新执行状态
     *
     * @param id              薪资调整ID
     * @param executionStatus 执行状态
     * @param executionNote   执行备注
     * @param executorId      执行人ID
     * @param executionTime   执行时间
     * @return 更新数量
     */
    int updateExecutionStatus(@Param("id") Long id, @Param("executionStatus") Integer executionStatus, @Param("executionNote") String executionNote, @Param("executorId") Long executorId, @Param("executionTime") LocalDate executionTime);

    /**
     * 查询薪资调整原因统计
     *
     * @return 原因统计
     */
    List<Map<String, Object>> selectAdjustmentReasonStatistics();

    /**
     * 查询部门薪资调整统计
     *
     * @return 部门统计
     */
    List<Map<String, Object>> selectDepartmentAdjustmentStatistics();

    /**
     * 查询薪资调整金额统计
     *
     * @return 金额统计
     */
    Map<String, Object> selectAdjustmentAmountStatistics();

    /**
     * 查询月度薪资调整统计
     *
     * @param year 年份
     * @return 月度统计
     */
    List<Map<String, Object>> selectMonthlyAdjustmentStatistics(@Param("year") Integer year);

    /**
     * 查询薪资调整影响分析
     *
     * @param adjustmentId 调整ID
     * @return 影响分析
     */
    Map<String, Object> selectAdjustmentImpactAnalysis(@Param("adjustmentId") Long adjustmentId);

    /**
     * 查询需要执行的薪资调整
     *
     * @param effectiveDate 生效日期
     * @return 需要执行的调整列表
     */
    List<OaArchiveSalaryAdjustmentEntity> selectAdjustmentsToExecute(@Param("effectiveDate") LocalDate effectiveDate);

    /**
     * 查询薪资调整历史
     *
     * @param archiveId 档案ID
     * @param limit     限制数量
     * @return 调整历史
     */
    List<OaArchiveSalaryAdjustmentEntity> selectAdjustmentHistory(@Param("archiveId") Long archiveId, @Param("limit") Integer limit);

    /**
     * 查询薪资级别分布
     *
     * @return 级别分布统计
     */
    List<Map<String, Object>> selectSalaryLevelDistribution();

    /**
     * 查询薪资调整审批流程
     *
     * @param adjustmentId 调整ID
     * @return 审批流程
     */
    List<Map<String, Object>> selectApprovalProcess(@Param("adjustmentId") Long adjustmentId);

    /**
     * 查询薪资调整绩效关联
     *
     * @param archiveId 档案ID
     * @return 绩效关联数据
     */
    List<Map<String, Object>> selectPerformanceRelatedAdjustments(@Param("archiveId") Long archiveId);

    /**
     * 查询薪资调整预算影响
     *
     * @param year  年份
     * @param month 月份
     * @return 预算影响分析
     */
    Map<String, Object> selectBudgetImpactAnalysis(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询薪资调整合规检查
     *
     * @param adjustmentId 调整ID
     * @return 合规检查结果
     */
    Map<String, Object> selectComplianceCheck(@Param("adjustmentId") Long adjustmentId);

    /**
     * 更新薪资调整备注
     *
     * @param id   调整ID
     * @param note 备注
     * @return 更新数量
     */
    int updateAdjustmentNote(@Param("id") Long id, @Param("note") String note);

    /**
     * 查询薪资调整冲突检查
     *
     * @param archiveId     档案ID
     * @param effectiveDate 生效日期
     * @return 冲突检查结果
     */
    List<OaArchiveSalaryAdjustmentEntity> selectConflictingAdjustments(@Param("archiveId") Long archiveId, @Param("effectiveDate") LocalDate effectiveDate);
}

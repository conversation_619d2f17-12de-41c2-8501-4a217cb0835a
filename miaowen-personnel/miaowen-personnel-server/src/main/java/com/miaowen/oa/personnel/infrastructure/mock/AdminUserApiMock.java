package com.miaowen.oa.personnel.infrastructure.mock;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.system.api.user.AdminUserApi;
import com.miaowen.oa.system.api.user.dto.UserInfoDTO;
import com.miaowen.oa.system.api.user.dto.UserSaveDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * AdminUserApi Mock 实现
 * 用于本地开发和测试环境，避免微服务依赖问题
 * 
 * 使用场景：
 * 1. 本地开发时系统服务未启动
 * 2. 单元测试时避免外部依赖
 * 3. 演示环境快速部署
 * 
 * 启用条件：
 * 在配置文件中设置 system.mock.admin-user-api=true
 * 
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Component
@ConditionalOnProperty(name = "system.mock.admin-user-api", havingValue = "true", matchIfMissing = false)
@Slf4j
public class AdminUserApiMock implements AdminUserApi {

    @Override
    public CommonResult<UserInfoDTO> getUserId(Long userId) {
        log.warn("使用Mock实现获取用户信息: userId={}", userId);
        
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setId(userId);
        userInfo.setUsername("mock_user_" + userId);
        userInfo.setNickname("Mock用户" + userId);
        userInfo.setEmail("mock" + userId + "@example.com");
        userInfo.setMobile("1380013" + String.format("%04d", userId % 10000));
        userInfo.setStatus(1); // 正常状态
        userInfo.setCreateTime(LocalDateTime.now());
        
        return CommonResult.success(userInfo);
    }

    @Override
    public CommonResult<UserInfoDTO> getUserByUsernameOrPhone(String username) {
        log.warn("使用Mock实现根据用户名获取用户信息: username={}", username);
        
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setId(1001L);
        userInfo.setUsername(username);
        userInfo.setNickname("Mock用户");
        userInfo.setEmail("<EMAIL>");
        userInfo.setMobile(username.matches("^1[3-9]\\d{9}$") ? username : "13800138000");
        userInfo.setStatus(1); // 正常状态
        userInfo.setCreateTime(LocalDateTime.now());
        
        return CommonResult.success(userInfo);
    }

    @Override
    public CommonResult<UserInfoDTO> getUserByQyWechatUserId(String qyWechatUserId) {
        log.warn("使用Mock实现根据企业微信ID获取用户信息: qyWechatUserId={}", qyWechatUserId);
        
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setId(1001L);
        userInfo.setUsername("mock_wechat_user");
        userInfo.setNickname("Mock企业微信用户");
        userInfo.setEmail("<EMAIL>");
        userInfo.setMobile("13800138000");
        userInfo.setStatus(1); // 正常状态
        userInfo.setCreateTime(LocalDateTime.now());
        
        return CommonResult.success(userInfo);
    }

    @Override
    public CommonResult<Void> save(UserSaveDTO dto) {
        log.warn("使用Mock实现保存用户信息: username={}, nickname={}, email={}, mobile={}", 
                dto.getUsername(), dto.getNickname(), dto.getEmail(), dto.getMobile());
        
        // 模拟保存成功
        return CommonResult.success();
    }

    /**
     * 如果有其他方法需要实现，可以在这里添加
     */
    
    // 可以根据实际的AdminUserApi接口添加其他方法的Mock实现
    // 例如：
    // @Override
    // public CommonResult<List<UserInfoDTO>> getUserList(UserPageReqDTO reqDTO) {
    //     log.warn("使用Mock实现获取用户列表");
    //     return CommonResult.success(Collections.emptyList());
    // }
}

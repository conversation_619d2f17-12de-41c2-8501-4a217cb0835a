package com.miaowen.oa.personnel.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 档案教育经历实体类
 * 存储员工的详细教育经历信息，支持多段教育经历
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_archive_education")
public class OaArchiveEducationEntity extends BaseDO {

    /**
     * 档案ID（外键关联oa_archive表）
     */
    private Long archiveId;

    /**
     * 用户ID（冗余字段，便于查询）
     */
    private Long userId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 学校类型
     * 1-公立 2-私立 3-中外合作 4-其他
     */
    private Integer schoolType;

    /**
     * 专业名称
     */
    private String major;

    /**
     * 专业代码
     */
    private String majorCode;

    /**
     * 学历层次
     * 1-小学 2-初中 3-高中/中专 4-大专 5-本科 6-硕士 7-博士
     */
    private Integer educationLevel;

    /**
     * 学位
     * 1-无学位 2-学士 3-硕士 4-博士
     */
    private Integer degree;

    /**
     * 学制
     * 1-全日制 2-非全日制 3-自考 4-成人教育 5-网络教育 6-函授
     */
    private Integer studyForm;

    /**
     * 入学时间
     * 格式：YYYY-MM
     */
    private String startDate;

    /**
     * 毕业时间
     * 格式：YYYY-MM
     */
    private String endDate;

    /**
     * 学制（年）
     */
    private Integer studyYears;


    /**
     * 是否统招
     * 0-否 1-是
     */
    private Integer isRegularAdmission;

    /**
     * 是否211/985院校
     * 0-否 1-是
     */
    private Integer isKey211Or985;

    /**
     * 是否双一流院校
     * 0-否 1-是
     */
    private Integer isDoubleFirstClass;


    /**
     * 学院/系名称
     */
    private String collegeName;


    /**
     * 是否在职学习
     * 0-否 1-是
     */
    private Integer isPartTimeStudy;


    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     * 0-正常 1-删除
     */
    private Integer state;

    // ========== 便利方法 ==========

    /**
     * 获取学历层次名称
     */
    public String getEducationLevelName() {
        if (educationLevel == null) return null;
        String[] levels = {"", "小学", "初中", "高中/中专", "大专", "本科", "硕士", "博士"};
        return educationLevel > 0 && educationLevel < levels.length ? levels[educationLevel] : null;
    }

    /**
     * 获取学位名称
     */
    public String getDegreeName() {
        if (degree == null) return null;
        String[] degrees = {"", "无学位", "学士", "硕士", "博士"};
        return degree > 0 && degree < degrees.length ? degrees[degree] : null;
    }

    /**
     * 获取学习形式名称
     */
    public String getStudyFormName() {
        if (studyForm == null) return null;
        String[] forms = {"", "全日制", "非全日制", "自考", "成人教育", "网络教育", "函授"};
        return studyForm > 0 && studyForm < forms.length ? forms[studyForm] : null;
    }

    /**
     * 计算学习时长（月）
     */
    public Integer getStudyDurationMonths() {
        if (startDate == null || endDate == null) return null;
        
        try {
            String[] startParts = startDate.split("-");
            String[] endParts = endDate.split("-");
            
            int startYear = Integer.parseInt(startParts[0]);
            int startMonth = Integer.parseInt(startParts[1]);
            int endYear = Integer.parseInt(endParts[0]);
            int endMonth = Integer.parseInt(endParts[1]);
            
            return (endYear - startYear) * 12 + (endMonth - startMonth);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 是否为本科及以上学历
     */
    public boolean isBachelorOrAbove() {
        return educationLevel != null && educationLevel >= 5;
    }

    /**
     * 是否为研究生及以上学历
     */
    public boolean isGraduateOrAbove() {
        return educationLevel != null && educationLevel >= 6;
    }

    /**
     * 是否有学位
     */
    public boolean hasDegree() {
        return degree != null && degree > 1;
    }


    /**
     * 是否为重点院校
     */
    public boolean isKeyUniversity() {
        return (isKey211Or985 != null && isKey211Or985 == 1) ||
               (isDoubleFirstClass != null && isDoubleFirstClass == 1);
    }
}

-- =====================================================
-- 人事档案管理系统最终优化版数据库建表SQL
-- 优化内容：
-- 1. 删除外键约束，所有字段非空给默认值
-- 2. 优化text字段使用，不必要的改为varchar
-- 3. text类型字段不设置为非空
-- =====================================================


-- =====================================================
-- 1. 档案主表 (oa_archive) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 档案核心信息
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（关联system模块的oa_user表）',
  `archive_number` varchar(50) NOT NULL DEFAULT '' COMMENT '档案编号（自动生成，格式：ARCH-YYYY-NNNNNN）',
  `archive_state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '档案状态：1-草稿，2-待审核，3-已审核，4-已归档，5-已锁定',
  `archive_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '档案类型：1-正式员工档案，2-实习生档案，3-外包人员档案，4-其他',
  `security_level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '档案密级：1-公开，2-内部，3-机密，4-绝密',
  `archive_create_date` varchar(20) NOT NULL DEFAULT '' COMMENT '建档时间',
  `archive_creator_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '建档人ID',
  `archive_creator_name` varchar(50) NOT NULL DEFAULT '' COMMENT '建档人姓名',
  `last_update_date` varchar(20) NOT NULL DEFAULT '' COMMENT '最后更新时间',
  `last_updater_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '最后更新人ID',
  `last_updater_name` varchar(50) NOT NULL DEFAULT '' COMMENT '最后更新人姓名',
  
  -- 入职相关信息
  `entry_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '入职日期',
  `probation_start_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '试用期开始日期',
  `probation_end_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '试用期结束日期',
  `regular_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '转正日期',
  `entry_salary` int(11) NOT NULL DEFAULT 0 COMMENT '入职薪资（元）',
  `regular_salary` int(11) NOT NULL DEFAULT 0 COMMENT '转正薪资（元）',
  `current_salary` int(11) NOT NULL DEFAULT 0 COMMENT '当前薪资（元）',
  `salary_adjustment_count` int(11) NOT NULL DEFAULT 0 COMMENT '薪资调整次数',
  `last_salary_adjustment_date` varchar(20) NOT NULL DEFAULT '' COMMENT '最后薪资调整日期',
  
  -- 教育背景摘要
  `highest_education` tinyint(1) NOT NULL DEFAULT 0 COMMENT '最高学历：1-小学，2-初中，3-高中/中专，4-大专，5-本科，6-硕士，7-博士',
  `highest_degree` tinyint(1) NOT NULL DEFAULT 0 COMMENT '最高学位：1-无学位，2-学士，3-硕士，4-博士',
  `graduate_school` varchar(100) NOT NULL DEFAULT '' COMMENT '毕业院校名称（最高学历）',
  `major` varchar(50) NOT NULL DEFAULT '' COMMENT '专业名称（最高学历）',
  `graduation_start_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '起始时间（最高学历）',
  `graduation_end_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '毕业时间（最高学历）',

  -- 工作经历摘要
  `work_years` int(11) NOT NULL DEFAULT 0 COMMENT '工作年限（年）',
  `has_work_experience` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有工作经验：0-无，1-有',
  `previous_company_count` int(11) NOT NULL DEFAULT 0 COMMENT '前公司数量',
  `longest_job_duration` int(11) NOT NULL DEFAULT 0 COMMENT '最长工作时长（月）',
  `average_job_duration` int(11) NOT NULL DEFAULT 0 COMMENT '平均工作时长（月）',
  
  -- 家庭状况摘要
  `marital_state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '婚姻状况：1-未婚，2-已婚，3-离异，4-丧偶',
  `children_count` int(11) NOT NULL DEFAULT 0 COMMENT '子女数量',
  `family_member_count` int(11) NOT NULL DEFAULT 0 COMMENT '家庭成员总数',
  `emergency_contact_name` varchar(50) NOT NULL DEFAULT '' COMMENT '紧急联系人姓名',
  `emergency_contact_relation` tinyint(1) NOT NULL DEFAULT 0 COMMENT '紧急联系人关系：1-父亲，2-母亲，3-配偶，4-子女，5-兄弟姐妹，6-朋友，7-其他',
  `emergency_contact_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '紧急联系人电话',
  
  -- 档案统计信息
  `education_record_count` int(11) NOT NULL DEFAULT 0 COMMENT '教育经历记录数',
  `work_experience_record_count` int(11) NOT NULL DEFAULT 0 COMMENT '工作经历记录数',
  `family_member_record_count` int(11) NOT NULL DEFAULT 0 COMMENT '家庭成员记录数',
  `attachment_record_count` int(11) NOT NULL DEFAULT 0 COMMENT '附件材料记录数',
  `completion_percentage` int(11) NOT NULL DEFAULT 0 COMMENT '档案完整度（百分比）',
  `last_audit_date` varchar(20) NOT NULL DEFAULT '' COMMENT '最后审核时间',
  `last_auditor_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '最后审核人ID',
  `last_auditor_name` varchar(50) NOT NULL DEFAULT '' COMMENT '最后审核人姓名',
  `audit_comment` varchar(1000) DEFAULT NULL COMMENT '审核意见',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  
  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_archive_number` (`archive_number`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_archive_state` (`archive_state`),
  KEY `idx_archive_type` (`archive_type`),
  KEY `idx_security_level` (`security_level`),
  KEY `idx_entry_date` (`entry_date`),
  KEY `idx_regular_date` (`regular_date`),
  KEY `idx_probation_period` (`probation_start_date`, `probation_end_date`),
  KEY `idx_highest_education` (`highest_education`),
  KEY `idx_graduate_school` (`graduate_school`),
  KEY `idx_major` (`major`),
  KEY `idx_work_years` (`work_years`),
  KEY `idx_marital_state` (`marital_state`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_update_time` (`update_time`),
  KEY `idx_creator` (`creator`),
  KEY `idx_delete_time` (`delete_time`),
  
  -- 复合索引
  KEY `idx_state_type` (`archive_state`, `archive_type`),
  KEY `idx_user_state` (`user_id`, `archive_state`),
  KEY `idx_entry_date_state` (`entry_date`, `archive_state`),
  KEY `idx_education_major` (`highest_education`, `major`),
  KEY `idx_delete_time_state` (`delete_time`, `archive_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案主表：存储员工的详细档案信息，包括入职、教育、工作经验等';

-- =====================================================
-- 2. 档案合同信息表 (oa_archive_contract) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_contract` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',
  
  -- 合同基本信息
  `contract_number` varchar(50) NOT NULL DEFAULT '' COMMENT '合同编号',
  `contract_name` varchar(100) NOT NULL DEFAULT '' COMMENT '合同名称',
  `contract_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '合同类型：1-劳动合同，2-劳务合同，3-实习协议，4-派遣合同，5-顾问协议，6-保密协议，7-竞业协议，8-其他',
  `contract_nature` tinyint(1) NOT NULL DEFAULT 1 COMMENT '合同性质：1-固定期限，2-无固定期限，3-以完成一定工作任务为期限',
  `contract_state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '合同状态：1-草稿，2-待签署，3-已签署，4-生效中，5-已到期，6-已终止，7-已解除',
  `signature_method` tinyint(1) NOT NULL DEFAULT 1 COMMENT '签署方式：1-纸质签署，2-电子签署，3-混合签署',
  
  -- 合同期限信息
  `start_date` varchar(20) NOT NULL DEFAULT '' COMMENT '合同开始日期',
  `end_date` varchar(20) NOT NULL DEFAULT '' COMMENT '合同结束日期',
  `contract_duration` int(11) NOT NULL DEFAULT 0 COMMENT '合同期限（月）',
  `probation_start_date` varchar(20) NOT NULL DEFAULT '' COMMENT '试用期开始日期',
  `probation_end_date` varchar(20) NOT NULL DEFAULT '' COMMENT '试用期结束日期',
  `probation_duration` int(11) NOT NULL DEFAULT 0 COMMENT '试用期期限（月）',
  
  -- 工作信息
  `work_location` varchar(100) NOT NULL DEFAULT '' COMMENT '工作地点',
  `work_address` varchar(200) NOT NULL DEFAULT '' COMMENT '详细工作地址',
  `position` varchar(50) NOT NULL DEFAULT '' COMMENT '工作岗位',
  `position_level` varchar(20) NOT NULL DEFAULT '' COMMENT '岗位级别',
  `department` varchar(50) NOT NULL DEFAULT '' COMMENT '所属部门',
  `report_to` varchar(50) NOT NULL DEFAULT '' COMMENT '汇报对象',
  `job_description` varchar(1000) DEFAULT NULL COMMENT '工作内容描述',
  
  -- 薪资结构信息
  `basic_salary` int(11) NOT NULL DEFAULT 0 COMMENT '基本薪资（元/月）',
  `position_salary` int(11) NOT NULL DEFAULT 0 COMMENT '岗位薪资（元/月）',
  `performance_salary` int(11) NOT NULL DEFAULT 0 COMMENT '绩效薪资（元/月）',
  `allowance` int(11) NOT NULL DEFAULT 0 COMMENT '津贴补贴（元/月）',
  `non_compete_allowance` int(11) NOT NULL DEFAULT 0 COMMENT '竞业补贴（元/月）',
  `other_allowance` int(11) NOT NULL DEFAULT 0 COMMENT '其他补贴（元/月）',
  `total_monthly_salary` int(11) NOT NULL DEFAULT 0 COMMENT '月薪总额（元）',
  `total_annual_salary` int(11) NOT NULL DEFAULT 0 COMMENT '年薪总额（元）',
  `probation_salary` int(11) NOT NULL DEFAULT 0 COMMENT '试用期薪资（元/月）',
  `salary_pay_day` int(11) NOT NULL DEFAULT 0 COMMENT '薪资发放日',
  `salary_pay_method` tinyint(1) NOT NULL DEFAULT 1 COMMENT '薪资发放方式：1-银行转账，2-现金，3-支票，4-其他',
  `salary_adjustment_mechanism` varchar(500) DEFAULT NULL COMMENT '薪资调整机制',
  
  -- 福利待遇信息
  `social_insurance_base` int(11) NOT NULL DEFAULT 0 COMMENT '社会保险缴纳基数（元）',
  `housing_fund_base` int(11) NOT NULL DEFAULT 0 COMMENT '公积金缴纳基数（元）',
  `social_insurance_rate_company` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '社保缴纳比例（企业部分，百分比）',
  `social_insurance_rate_personal` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '社保缴纳比例（个人部分，百分比）',
  `housing_fund_rate_company` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '公积金缴纳比例（企业部分，百分比）',
  `housing_fund_rate_personal` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '公积金缴纳比例（个人部分，百分比）',
  `annual_leave_days` int(11) NOT NULL DEFAULT 0 COMMENT '年假天数',
  `sick_leave_days` int(11) NOT NULL DEFAULT 0 COMMENT '病假天数',
  `other_benefits` varchar(1000) DEFAULT NULL COMMENT '其他福利描述',
  
  -- 工作时间信息
  `working_time_system` tinyint(1) NOT NULL DEFAULT 1 COMMENT '工作制度：1-标准工时制，2-不定时工作制，3-综合计算工时制',
  `work_days_per_week` int(11) NOT NULL DEFAULT 5 COMMENT '每周工作天数',
  `work_hours_per_day` decimal(3,1) NOT NULL DEFAULT 8.0 COMMENT '每日工作小时数',
  `work_start_time` varchar(10) NOT NULL DEFAULT '' COMMENT '上班时间',
  `work_end_time` varchar(10) NOT NULL DEFAULT '' COMMENT '下班时间',
  `lunch_break_minutes` int(11) NOT NULL DEFAULT 60 COMMENT '午休时间（分钟）',
  `allow_flexible_work` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许弹性工作：0-否，1-是',
  `allow_remote_work` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许远程工作：0-否，1-是',
  
  -- 合同条款信息
  `confidentiality_period` int(11) NOT NULL DEFAULT 0 COMMENT '保密期限（月）',
  `non_compete_period` int(11) NOT NULL DEFAULT 0 COMMENT '竞业限制期限（月）',
  `non_compete_scope` varchar(1000) DEFAULT NULL COMMENT '竞业限制范围',
  `penalty_clause` text DEFAULT NULL COMMENT '违约金条款',
  `intellectual_property_clause` text DEFAULT NULL COMMENT '知识产权条款',
  `training_service_period` int(11) NOT NULL DEFAULT 0 COMMENT '培训服务期（月）',
  `training_cost` int(11) NOT NULL DEFAULT 0 COMMENT '培训费用（元）',
  `special_clauses` text DEFAULT NULL COMMENT '其他特殊条款',
  
  -- 签署信息
  `employee_sign_date` varchar(20) NOT NULL DEFAULT '' COMMENT '员工签署日期',
  `employee_sign_location` varchar(100) NOT NULL DEFAULT '' COMMENT '员工签署地点',
  `company_signatory` varchar(50) NOT NULL DEFAULT '' COMMENT '公司签署人',
  `company_signatory_position` varchar(50) NOT NULL DEFAULT '' COMMENT '公司签署人职位',
  `company_sign_date` varchar(20) NOT NULL DEFAULT '' COMMENT '公司签署日期',
  `company_sign_location` varchar(100) NOT NULL DEFAULT '' COMMENT '公司签署地点',
  `witness` varchar(50) NOT NULL DEFAULT '' COMMENT '见证人',
  `witness_position` varchar(50) NOT NULL DEFAULT '' COMMENT '见证人职位',
  
  -- 文件信息
  `contract_file_url` varchar(500) NOT NULL DEFAULT '' COMMENT '合同文件URL',
  `contract_file_name` varchar(200) NOT NULL DEFAULT '' COMMENT '合同文件名称',
  `contract_file_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '合同文件大小（字节）',
  `e_signature_file_url` varchar(500) NOT NULL DEFAULT '' COMMENT '电子签名文件URL',
  `scanned_file_url` varchar(500) NOT NULL DEFAULT '' COMMENT '合同扫描件URL',
  `attachment_file_urls` text DEFAULT NULL COMMENT '附件文件URL（多个文件用逗号分隔）',
  
  -- 审批信息
  `approval_state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '审批状态：1-待审批，2-审批中，3-审批通过，4-审批拒绝',
  `approval_process_id` varchar(50) NOT NULL DEFAULT '' COMMENT '审批流程ID',
  `current_approver_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前审批人ID',
  `current_approver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '当前审批人姓名',
  `approval_comment` varchar(1000) DEFAULT NULL COMMENT '审批意见',
  `approval_complete_time` varchar(20) NOT NULL DEFAULT '' COMMENT '审批完成时间',
  
  -- 变更记录
  `is_renewal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为续签合同：0-否，1-是',
  `original_contract_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '原合同ID（续签时关联）',
  `change_reason` varchar(200) NOT NULL DEFAULT '' COMMENT '变更原因',
  `change_description` varchar(1000) DEFAULT NULL COMMENT '变更说明',
  `change_effective_date` varchar(20) NOT NULL DEFAULT '' COMMENT '变更生效日期',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
  
  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_number` (`contract_number`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_contract_type` (`contract_type`),
  KEY `idx_contract_state` (`contract_state`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_end_date` (`end_date`),
  KEY `idx_approval_state` (`approval_state`),
  KEY `idx_delete_time` (`delete_time`),
  
  -- 复合索引
  KEY `idx_archive_state` (`archive_id`, `contract_state`),
  KEY `idx_user_state` (`user_id`, `contract_state`),
  KEY `idx_type_state` (`contract_type`, `contract_state`),
  KEY `idx_delete_time_state` (`delete_time`, `contract_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案合同信息表：存储员工的劳动合同信息，支持多次合同签订记录';

-- =====================================================
-- 3. 档案教育经历表 (oa_archive_education) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_education` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 学校基本信息
  `school_name` varchar(100) NOT NULL DEFAULT '' COMMENT '学校名称',
  `school_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '学校类型：1-公立，2-私立，3-中外合作，4-其他',
  `major` varchar(100) NOT NULL DEFAULT '' COMMENT '专业名称',
  `major_code` varchar(20) NOT NULL DEFAULT '' COMMENT '专业代码',
  `education_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '学历层次：1-小学，2-初中，3-高中/中专，4-大专，5-本科，6-硕士，7-博士',
  `degree` tinyint(1) NOT NULL DEFAULT 0 COMMENT '学位：1-无学位，2-学士，3-硕士，4-博士',
  `study_form` tinyint(1) NOT NULL DEFAULT 1 COMMENT '学习形式：1-全日制，2-非全日制，3-自考，4-成人教育，5-网络教育，6-函授',

  -- 时间信息
  `start_date` varchar(20) NOT NULL DEFAULT '' COMMENT '入学时间（格式：YYYY-MM）',
  `end_date` varchar(20) NOT NULL DEFAULT '' COMMENT '毕业时间（格式：YYYY-MM）',
  `study_years` int(11) NOT NULL DEFAULT 0 COMMENT '学制（年）',

  -- 证书信息
  `graduation_certificate_no` varchar(50) NOT NULL DEFAULT '' COMMENT '毕业证书编号',
  `degree_certificate_no` varchar(50) NOT NULL DEFAULT '' COMMENT '学位证书编号',
  `gpa` decimal(3,2) NOT NULL DEFAULT 0.00 COMMENT 'GPA/成绩',
  `gpa_scale` varchar(10) NOT NULL DEFAULT '4.0' COMMENT 'GPA制度（如4.0制、5.0制、100分制）',
  `class_rank` int(11) NOT NULL DEFAULT 0 COMMENT '班级排名',
  `class_total` int(11) NOT NULL DEFAULT 0 COMMENT '班级总人数',
  `grade_rank` int(11) NOT NULL DEFAULT 0 COMMENT '年级排名',
  `grade_total` int(11) NOT NULL DEFAULT 0 COMMENT '年级总人数',

  -- 学术成就（JSON格式存储，使用text类型）
  `honors_awards` text DEFAULT NULL COMMENT '荣誉奖项（JSON格式存储多个奖项）',
  `scholarships` text DEFAULT NULL COMMENT '奖学金情况（JSON格式存储多个奖学金）',
  `academic_papers` text DEFAULT NULL COMMENT '学术论文（JSON格式存储多篇论文）',
  `research_projects` text DEFAULT NULL COMMENT '研究项目（JSON格式存储多个项目）',
  `extracurricular_activities` text DEFAULT NULL COMMENT '课外活动（JSON格式存储多个活动）',
  `student_organizations` text DEFAULT NULL COMMENT '学生组织（JSON格式存储多个组织）',

  -- 导师信息（研究生及以上）
  `supervisor_name` varchar(50) NOT NULL DEFAULT '' COMMENT '导师姓名',
  `supervisor_title` varchar(50) NOT NULL DEFAULT '' COMMENT '导师职称',
  `supervisor_contact` varchar(100) NOT NULL DEFAULT '' COMMENT '导师联系方式',
  `thesis_title` varchar(200) NOT NULL DEFAULT '' COMMENT '论文题目',
  `thesis_defense_date` varchar(20) NOT NULL DEFAULT '' COMMENT '论文答辩日期',
  `thesis_grade` varchar(20) NOT NULL DEFAULT '' COMMENT '论文成绩',

  -- 国际交流（JSON格式存储，使用text类型）
  `exchange_programs` text DEFAULT NULL COMMENT '交换项目（JSON格式存储多个项目）',
  `international_experience` varchar(1000) DEFAULT NULL COMMENT '国际经历',
  `language_certificates` text DEFAULT NULL COMMENT '语言证书（JSON格式存储多个证书）',

  -- 实习实践（JSON格式存储，使用text类型）
  `internship_experience` text DEFAULT NULL COMMENT '实习经历（JSON格式存储多个实习）',
  `practice_projects` text DEFAULT NULL COMMENT '实践项目（JSON格式存储多个项目）',
  `volunteer_work` text DEFAULT NULL COMMENT '志愿工作（JSON格式存储多个工作）',

  -- 技能证书（JSON格式存储，使用text类型）
  `professional_certificates` text DEFAULT NULL COMMENT '专业证书（JSON格式存储多个证书）',
  `skill_certificates` text DEFAULT NULL COMMENT '技能证书（JSON格式存储多个证书）',
  `computer_skills` varchar(1000) DEFAULT NULL COMMENT '计算机技能',
  `language_skills` varchar(1000) DEFAULT NULL COMMENT '语言技能',

  -- 验证信息
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已验证：0-未验证，1-已验证',
  `verification_method` varchar(50) NOT NULL DEFAULT '' COMMENT '验证方式',
  `verification_date` varchar(20) NOT NULL DEFAULT '' COMMENT '验证日期',
  `verification_agency` varchar(100) NOT NULL DEFAULT '' COMMENT '验证机构',
  `verification_result` varchar(200) NOT NULL DEFAULT '' COMMENT '验证结果',

  -- 附件信息
  `graduation_certificate_url` varchar(500) NOT NULL DEFAULT '' COMMENT '毕业证书文件URL',
  `degree_certificate_url` varchar(500) NOT NULL DEFAULT '' COMMENT '学位证书文件URL',
  `transcript_url` varchar(500) NOT NULL DEFAULT '' COMMENT '成绩单文件URL',
  `other_attachments` text DEFAULT NULL COMMENT '其他附件（JSON格式存储多个文件）',

  -- 其他信息
  `is_highest_education` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为最高学历：0-否，1-是',
  `education_background_summary` varchar(1000) DEFAULT NULL COMMENT '教育背景摘要',
  `special_notes` varchar(500) DEFAULT NULL COMMENT '特殊说明',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',

  PRIMARY KEY (`id`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_school_name` (`school_name`),
  KEY `idx_education_level` (`education_level`),
  KEY `idx_degree` (`degree`),
  KEY `idx_major` (`major`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_end_date` (`end_date`),
  KEY `idx_is_highest` (`is_highest_education`),
  KEY `idx_delete_time` (`delete_time`),

  -- 复合索引
  KEY `idx_archive_level` (`archive_id`, `education_level`),
  KEY `idx_user_level` (`user_id`, `education_level`),
  KEY `idx_level_degree` (`education_level`, `degree`),
  KEY `idx_delete_time_level` (`delete_time`, `education_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案教育经历表：存储员工的详细教育经历信息，支持多段教育经历';

-- =====================================================
-- 4. 档案工作经历表 (oa_archive_work_experience) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_work_experience` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 公司基本信息
  `company_name` varchar(100) NOT NULL DEFAULT '' COMMENT '公司名称',
  `company_short_name` varchar(50) NOT NULL DEFAULT '' COMMENT '公司简称',
  `company_size` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公司规模：1-20人以下，2-20-99人，3-100-499人，4-500-999人，5-1000-9999人，6-10000人以上',
  `company_nature` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公司性质：1-国有企业，2-民营企业，3-外资企业，4-合资企业，5-事业单位，6-政府机关，7-其他',
  `industry` varchar(100) NOT NULL DEFAULT '' COMMENT '所属行业',
  `industry_code` varchar(20) NOT NULL DEFAULT '' COMMENT '行业代码',

  -- 职位信息
  `department` varchar(50) NOT NULL DEFAULT '' COMMENT '工作部门',
  `position` varchar(50) NOT NULL DEFAULT '' COMMENT '职位名称',
  `position_level` varchar(20) NOT NULL DEFAULT '' COMMENT '职位级别',
  `position_category` tinyint(1) NOT NULL DEFAULT 0 COMMENT '职位类别：1-技术类，2-管理类，3-销售类，4-市场类，5-财务类，6-人事类，7-行政类，8-其他',

  -- 时间信息
  `start_date` varchar(20) NOT NULL DEFAULT '' COMMENT '入职时间（格式：YYYY-MM）',
  `end_date` varchar(20) NOT NULL DEFAULT '' COMMENT '离职时间（格式：YYYY-MM）',
  `is_current_job` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在职：0-否，1-是',
  `work_duration_months` int(11) NOT NULL DEFAULT 0 COMMENT '工作时长（月）',

  -- 薪资信息
  `salary` int(11) NOT NULL DEFAULT 0 COMMENT '薪资（元/月）',
  `bonus` int(11) NOT NULL DEFAULT 0 COMMENT '年终奖（元）',
  `salary_currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '薪资币种',

  -- 工作内容
  `job_description` varchar(1000) DEFAULT NULL COMMENT '工作职责描述',
  `work_location` varchar(100) NOT NULL DEFAULT '' COMMENT '工作地点',

  -- 离职信息
  `resignation_reason` varchar(200) NOT NULL DEFAULT '' COMMENT '离职原因',
  `leave_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '离职类型：1-主动离职，2-被动离职，3-合同到期，4-协商离职，5-其他',

  -- 证明人信息
  `reference_name` varchar(50) NOT NULL DEFAULT '' COMMENT '证明人姓名',
  `reference_position` varchar(50) NOT NULL DEFAULT '' COMMENT '证明人职位',
  `reference_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '证明人电话',
  `reference_email` varchar(100) NOT NULL DEFAULT '' COMMENT '证明人邮箱',

  -- 工作评价
  `performance_rating` varchar(20) NOT NULL DEFAULT '' COMMENT '绩效评级',
  `work_evaluation` varchar(1000) DEFAULT NULL COMMENT '工作评价',

  -- 验证信息
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已验证：0-未验证，1-已验证',
  `verification_method` varchar(50) NOT NULL DEFAULT '' COMMENT '验证方式',
  `verification_date` varchar(20) NOT NULL DEFAULT '' COMMENT '验证日期',
  `verification_result` varchar(200) NOT NULL DEFAULT '' COMMENT '验证结果',

  -- 其他信息
  `team_size` int(11) NOT NULL DEFAULT 0 COMMENT '团队规模',
  `travel_frequency` varchar(50) NOT NULL DEFAULT '' COMMENT '出差频率',
  `overtime_frequency` varchar(50) NOT NULL DEFAULT '' COMMENT '加班频率',
  `special_notes` varchar(500) DEFAULT NULL COMMENT '特殊说明',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_company_name` (`company_name`),
  KEY `idx_position` (`position`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_end_date` (`end_date`),
  KEY `idx_work_duration` (`work_duration_months`),
  KEY `idx_is_current` (`is_current_job`),
  KEY `idx_company_nature` (`company_nature`),
  KEY `idx_position_category` (`position_category`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_tenant_id` (`tenant_id`),

  -- 复合索引
  KEY `idx_archive_current` (`archive_id`, `is_current_job`),
  KEY `idx_user_current` (`user_id`, `is_current_job`),
  KEY `idx_company_position` (`company_name`, `position`),
  KEY `idx_delete_time_current` (`delete_time`, `is_current_job`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案工作经历表：存储员工的详细工作经历信息，支持多段工作经历';

-- =====================================================
-- 5. 档案家庭成员表 (oa_archive_family_member) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_family_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 基本信息
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '姓名',
  `relationship` tinyint(1) NOT NULL DEFAULT 0 COMMENT '关系：1-父亲，2-母亲，3-配偶，4-子女，5-兄弟姐妹，6-祖父母/外祖父母，7-其他亲属，8-朋友，9-其他',
  `gender` tinyint(1) NOT NULL DEFAULT 0 COMMENT '性别：1-男，2-女',
  `id_card_number` varchar(18) NOT NULL DEFAULT '' COMMENT '身份证号码',
  `birth_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '出生日期',
  `age` int(11) NOT NULL DEFAULT 0 COMMENT '年龄',
  `ethnicity` varchar(20) NOT NULL DEFAULT '' COMMENT '民族',
  `nationality` varchar(20) NOT NULL DEFAULT '中国' COMMENT '国籍',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `email` varchar(100) NOT NULL DEFAULT '' COMMENT '电子邮箱',

  -- 工作信息
  `work_unit` varchar(100) NOT NULL DEFAULT '' COMMENT '工作单位',
  `work_unit_nature` tinyint(1) NOT NULL DEFAULT 0 COMMENT '工作单位性质：1-国有企业，2-民营企业，3-外资企业，4-事业单位，5-政府机关，6-个体经营，7-无业，8-退休，9-其他',
  `position` varchar(50) NOT NULL DEFAULT '' COMMENT '职务',
  `monthly_income` int(11) NOT NULL DEFAULT 0 COMMENT '月收入（元）',
  `income_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '收入水平：1-低收入，2-中等收入，3-高收入',

  -- 联系信息
  `address` varchar(200) NOT NULL DEFAULT '' COMMENT '居住地址',
  `postal_code` varchar(10) NOT NULL DEFAULT '' COMMENT '邮政编码',
  `home_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '家庭电话',
  `work_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '工作电话',

  -- 紧急联系人信息
  `is_emergency_contact` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为紧急联系人：0-否，1-是',
  `emergency_contact_priority` int(11) NOT NULL DEFAULT 0 COMMENT '紧急联系人优先级（数字越小优先级越高）',
  `relationship_description` varchar(200) NOT NULL DEFAULT '' COMMENT '与员工关系描述',

  -- 健康状况
  `health_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '健康状况：1-健康，2-一般，3-较差，4-残疾',
  `medical_history` varchar(500) DEFAULT NULL COMMENT '病史',
  `allergies` varchar(200) NOT NULL DEFAULT '' COMMENT '过敏史',

  -- 教育背景
  `education_level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '学历：1-小学，2-初中，3-高中/中专，4-大专，5-本科，6-硕士，7-博士',
  `graduate_school` varchar(100) NOT NULL DEFAULT '' COMMENT '毕业院校',
  `major` varchar(50) NOT NULL DEFAULT '' COMMENT '专业',

  -- 政治面貌
  `political_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '政治面貌：1-群众，2-团员，3-党员，4-民主党派，5-其他',
  `party_join_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '入党/团时间',

  -- 社会关系
  `social_relations` text DEFAULT NULL COMMENT '主要社会关系（JSON格式存储）',
  `overseas_relations` varchar(500) DEFAULT NULL COMMENT '海外关系',

  -- 经济状况
  `financial_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '经济状况：1-困难，2-一般，3-良好，4-富裕',
  `property_info` varchar(500) DEFAULT NULL COMMENT '财产情况',
  `debt_info` varchar(500) DEFAULT NULL COMMENT '负债情况',

  -- 其他信息
  `hobbies` varchar(200) NOT NULL DEFAULT '' COMMENT '兴趣爱好',
  `special_skills` varchar(200) NOT NULL DEFAULT '' COMMENT '特长技能',
  `personality_traits` varchar(200) NOT NULL DEFAULT '' COMMENT '性格特点',
  `life_habits` varchar(200) NOT NULL DEFAULT '' COMMENT '生活习惯',
  `special_notes` varchar(500) DEFAULT NULL COMMENT '特殊说明',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_name` (`name`),
  KEY `idx_relationship` (`relationship`),
  KEY `idx_phone` (`phone`),
  KEY `idx_is_emergency` (`is_emergency_contact`),
  KEY `idx_id_card` (`id_card_number`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_tenant_id` (`tenant_id`),

  -- 复合索引
  KEY `idx_archive_relationship` (`archive_id`, `relationship`),
  KEY `idx_user_relationship` (`user_id`, `relationship`),
  KEY `idx_emergency_priority` (`is_emergency_contact`, `emergency_contact_priority`),
  KEY `idx_delete_time_relationship` (`delete_time`, `relationship`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案家庭成员表：存储员工的详细家庭成员信息，支持多个家庭成员';

-- =====================================================
-- 6. 档案薪资调整表 (oa_archive_salary_adjustment) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_salary_adjustment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 调整基本信息
  `adjustment_number` varchar(50) NOT NULL DEFAULT '' COMMENT '调薪编号',
  `adjustment_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '调薪类型：1-入职定薪，2-转正调薪，3-晋升调薪，4-年度调薪，5-绩效调薪，6-市场调薪，7-特殊调薪，8-降薪，9-其他',
  `adjustment_reason` varchar(200) NOT NULL DEFAULT '' COMMENT '调薪原因',
  `reason_category` tinyint(1) NOT NULL DEFAULT 0 COMMENT '调薪原因分类：1-绩效优秀，2-职位晋升，3-市场调研，4-内部公平，5-挽留人才，6-成本控制，7-其他',
  `effective_date` varchar(20) NOT NULL DEFAULT '' COMMENT '生效日期',
  `application_date` varchar(20) NOT NULL DEFAULT '' COMMENT '申请日期',
  `applicant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '申请人ID',
  `applicant_name` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人姓名',

  -- 调薪前薪资结构
  `before_basic_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前基本薪资（元/月）',
  `before_position_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前岗位薪资（元/月）',
  `before_performance_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前绩效薪资（元/月）',
  `before_allowance` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前津贴补贴（元/月）',
  `before_total_monthly_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前月薪总额（元）',

  -- 调薪后薪资结构
  `after_basic_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后基本薪资（元/月）',
  `after_position_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后岗位薪资（元/月）',
  `after_performance_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后绩效薪资（元/月）',
  `after_allowance` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后津贴补贴（元/月）',
  `after_total_monthly_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后月薪总额（元）',

  -- 调薪幅度
  `adjustment_amount` int(11) NOT NULL DEFAULT 0 COMMENT '调薪金额（元/月）',
  `adjustment_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '调薪幅度（百分比）',
  `annual_cost_impact` int(11) NOT NULL DEFAULT 0 COMMENT '年度成本影响（元）',

  -- 审批信息
  `approval_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '审批状态：1-待审批，2-审批中，3-审批通过，4-审批拒绝，5-已撤回',
  `approval_process_id` varchar(50) NOT NULL DEFAULT '' COMMENT '审批流程ID',
  `current_approver_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前审批人ID',
  `current_approver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '当前审批人姓名',
  `final_approver_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '最终审批人ID',
  `final_approver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '最终审批人姓名',
  `final_approval_date` varchar(20) NOT NULL DEFAULT '' COMMENT '最终审批日期',
  `approval_comments` varchar(1000) DEFAULT NULL COMMENT '审批意见',

  -- 执行信息
  `execution_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '执行状态：1-待执行，2-执行中，3-已执行，4-执行失败',
  `executor_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '执行人ID',
  `executor_name` varchar(50) NOT NULL DEFAULT '' COMMENT '执行人姓名',
  `execution_date` varchar(20) NOT NULL DEFAULT '' COMMENT '执行日期',
  `execution_comments` varchar(1000) DEFAULT NULL COMMENT '执行说明',

  -- 影响分析
  `budget_impact` varchar(1000) DEFAULT NULL COMMENT '预算影响分析',
  `market_comparison` varchar(1000) DEFAULT NULL COMMENT '市场对比分析',
  `internal_equity_analysis` varchar(1000) DEFAULT NULL COMMENT '内部公平性分析',

  -- 其他信息
  `is_retroactive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否追溯：0-否，1-是',
  `retroactive_months` int(11) NOT NULL DEFAULT 0 COMMENT '追溯月数',
  `retroactive_amount` int(11) NOT NULL DEFAULT 0 COMMENT '追溯金额（元）',
  `next_review_date` varchar(20) NOT NULL DEFAULT '' COMMENT '下次评估日期',
  `special_notes` varchar(500) DEFAULT NULL COMMENT '特殊说明',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_adjustment_number` (`adjustment_number`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_adjustment_type` (`adjustment_type`),
  KEY `idx_effective_date` (`effective_date`),
  KEY `idx_approval_status` (`approval_status`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_current_approver` (`current_approver_id`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_tenant_id` (`tenant_id`),

  -- 复合索引
  KEY `idx_archive_status` (`archive_id`, `approval_status`),
  KEY `idx_user_status` (`user_id`, `approval_status`),
  KEY `idx_type_status` (`adjustment_type`, `approval_status`),
  KEY `idx_delete_time_status` (`delete_time`, `approval_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案薪资调整表：存储员工的薪资调整记录，支持完整的审批流程';

-- =====================================================
-- 7. 档案附件表 (oa_archive_attachment) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_attachment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 附件分类信息
  `attachment_type` varchar(50) NOT NULL DEFAULT '' COMMENT '附件类型：resume-个人简历，application_registration-入职登记表，id_card_front-身份证人像面，id_card_back-身份证国徽面，graduation_certificate-毕业证书，degree_certificate-学位证书，skill_certificate-岗位技能证书，medical_report-体检报告，resignation_certificate-离职证明，bank_statement-银行流水，employee_declaration-员工入职声明书，employment_confirmation-录用条件确认书，non_compete_agreement-竞业协议，labor_contract-劳动合同，confidentiality_agreement-保密协议，other_attachment-其他附件',
  `attachment_name` varchar(200) NOT NULL DEFAULT '' COMMENT '附件名称',
  `attachment_description` varchar(500) DEFAULT NULL COMMENT '附件描述',

  -- 文件基本信息
  `original_file_name` varchar(200) NOT NULL DEFAULT '' COMMENT '文件原始名称',
  `stored_file_name` varchar(200) NOT NULL DEFAULT '' COMMENT '文件存储名称',
  `file_url` varchar(500) NOT NULL DEFAULT '' COMMENT '文件URL',
  `file_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
  `file_type` varchar(50) NOT NULL DEFAULT '' COMMENT '文件类型/MIME类型',
  `file_extension` varchar(10) NOT NULL DEFAULT '' COMMENT '文件扩展名',
  `file_md5` varchar(32) NOT NULL DEFAULT '' COMMENT '文件MD5值',

  -- 存储信息
  `storage_path` varchar(500) NOT NULL DEFAULT '' COMMENT '存储路径',
  `storage_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '存储类型：1-本地存储，2-阿里云OSS，3-腾讯云COS，4-七牛云，5-其他',
  `storage_bucket` varchar(100) NOT NULL DEFAULT '' COMMENT '存储桶名称',
  `storage_region` varchar(50) NOT NULL DEFAULT '' COMMENT '存储区域',
  `storage_endpoint` varchar(200) NOT NULL DEFAULT '' COMMENT '存储端点',

  -- 上传信息
  `uploader_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '上传者ID',
  `uploader_name` varchar(50) NOT NULL DEFAULT '' COMMENT '上传者姓名',
  `upload_date` varchar(20) NOT NULL DEFAULT '' COMMENT '上传日期',
  `upload_source` varchar(50) NOT NULL DEFAULT '' COMMENT '上传来源（web、mobile、api等）',
  `upload_ip` varchar(50) NOT NULL DEFAULT '' COMMENT '上传IP地址',

  -- 审核信息
  `review_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '审核状态：1-待审核，2-审核通过，3-审核拒绝，4-需要重新上传',
  `reviewer_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '审核人ID',
  `reviewer_name` varchar(50) NOT NULL DEFAULT '' COMMENT '审核人姓名',
  `review_date` varchar(20) NOT NULL DEFAULT '' COMMENT '审核日期',
  `review_comments` varchar(1000) DEFAULT NULL COMMENT '审核意见',

  -- 访问控制
  `access_level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '访问级别：1-公开，2-内部，3-机密，4-绝密',
  `allowed_roles` varchar(200) NOT NULL DEFAULT '' COMMENT '允许访问的角色（逗号分隔）',

  -- 使用统计
  `download_count` int(11) NOT NULL DEFAULT 0 COMMENT '下载次数',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '查看次数',
  `last_download_date` varchar(20) NOT NULL DEFAULT '' COMMENT '最后下载时间',
  `last_view_date` varchar(20) NOT NULL DEFAULT '' COMMENT '最后查看时间',
  `last_access_user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '最后访问用户ID',
  `last_access_user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '最后访问用户姓名',

  -- 文件状态
  `file_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '文件状态：1-正常，2-损坏，3-丢失，4-过期，5-待处理',
  `expiry_date` varchar(20) NOT NULL DEFAULT '' COMMENT '过期日期',
  `is_archived` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已归档：0-否，1-是',
  `archive_date` varchar(20) NOT NULL DEFAULT '' COMMENT '归档日期',
  `archive_location` varchar(200) NOT NULL DEFAULT '' COMMENT '归档位置',

  -- 备份信息
  `backup_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '备份状态：0-未备份，1-已备份，2-备份失败',
  `backup_date` varchar(20) NOT NULL DEFAULT '' COMMENT '备份时间',
  `backup_location` varchar(500) NOT NULL DEFAULT '' COMMENT '备份位置',

  -- 版本信息
  `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '版本号',
  `is_latest_version` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否最新版本：0-否，1-是',
  `parent_attachment_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '父附件ID（用于版本管理）',
  `version_description` varchar(200) NOT NULL DEFAULT '' COMMENT '版本说明',

  -- 其他信息
  `tags` varchar(200) NOT NULL DEFAULT '' COMMENT '标签（逗号分隔）',
  `keywords` varchar(200) NOT NULL DEFAULT '' COMMENT '关键词（用于搜索）',
  `special_notes` varchar(500) DEFAULT NULL COMMENT '特殊说明',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_attachment_type` (`attachment_type`),
  KEY `idx_attachment_name` (`attachment_name`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_uploader_id` (`uploader_id`),
  KEY `idx_upload_date` (`upload_date`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_file_status` (`file_status`),
  KEY `idx_is_latest` (`is_latest_version`),
  KEY `idx_access_level` (`access_level`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_tenant_id` (`tenant_id`),

  -- 复合索引
  KEY `idx_archive_type` (`archive_id`, `attachment_type`),
  KEY `idx_user_type` (`user_id`, `attachment_type`),
  KEY `idx_type_status` (`attachment_type`, `file_status`),
  KEY `idx_delete_time_type` (`delete_time`, `attachment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案附件表：存储员工档案的所有附件文件，支持版本管理和访问控制';

-- =====================================================
-- 4. 档案工作经历表 (oa_archive_work_experience) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_work_experience` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 公司基本信息
  `company_name` varchar(100) NOT NULL DEFAULT '' COMMENT '公司名称',
  `company_short_name` varchar(50) NOT NULL DEFAULT '' COMMENT '公司简称',
  `company_size` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公司规模：1-20人以下，2-20-99人，3-100-499人，4-500-999人，5-1000-9999人，6-10000人以上',
  `company_nature` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公司性质：1-国有企业，2-民营企业，3-外资企业，4-合资企业，5-事业单位，6-政府机关，7-其他',
  `industry` varchar(100) NOT NULL DEFAULT '' COMMENT '所属行业',
  `industry_code` varchar(20) NOT NULL DEFAULT '' COMMENT '行业代码',

  -- 职位信息
  `department` varchar(50) NOT NULL DEFAULT '' COMMENT '工作部门',
  `position` varchar(50) NOT NULL DEFAULT '' COMMENT '职位名称',
  `position_level` varchar(20) NOT NULL DEFAULT '' COMMENT '职位级别',
  `position_category` tinyint(1) NOT NULL DEFAULT 0 COMMENT '职位类别：1-技术类，2-管理类，3-销售类，4-市场类，5-财务类，6-人事类，7-行政类，8-其他',

  -- 时间信息
  `start_date` varchar(20) NOT NULL DEFAULT '' COMMENT '入职时间（格式：YYYY-MM）',
  `end_date` varchar(20) NOT NULL DEFAULT '' COMMENT '离职时间（格式：YYYY-MM）',
  `is_current_job` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在职：0-否，1-是',
  `work_duration_months` int(11) NOT NULL DEFAULT 0 COMMENT '工作时长（月）',

  -- 薪资信息
  `entry_salary` int(11) NOT NULL DEFAULT 0 COMMENT '入职薪资（元/月）',
  `final_salary` int(11) NOT NULL DEFAULT 0 COMMENT '离职薪资（元/月）',
  `salary_currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '薪资币种',
  `salary_structure` varchar(500) DEFAULT NULL COMMENT '薪资结构描述',
  `bonus_info` varchar(500) DEFAULT NULL COMMENT '奖金情况',
  `benefits_info` varchar(1000) DEFAULT NULL COMMENT '福利待遇',

  -- 工作内容
  `job_description` varchar(1000) DEFAULT NULL COMMENT '工作职责描述',
  `key_achievements` text DEFAULT NULL COMMENT '主要成就（JSON格式存储多个成就）',
  `project_experience` text DEFAULT NULL COMMENT '项目经验（JSON格式存储多个项目）',
  `skills_gained` text DEFAULT NULL COMMENT '获得技能（JSON格式存储多个技能）',
  `training_received` text DEFAULT NULL COMMENT '接受培训（JSON格式存储多个培训）',

  -- 离职信息
  `leave_reason` varchar(200) NOT NULL DEFAULT '' COMMENT '离职原因',
  `leave_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '离职类型：1-主动离职，2-被动离职，3-合同到期，4-协商离职，5-其他',
  `notice_period_days` int(11) NOT NULL DEFAULT 0 COMMENT '离职通知期（天）',
  `handover_state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '交接状态：1-已完成，2-进行中，3-未开始',
  `rehire_eligibility` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可重新雇佣：0-否，1-是',

  -- 证明人信息
  `reference_name` varchar(50) NOT NULL DEFAULT '' COMMENT '证明人姓名',
  `reference_position` varchar(50) NOT NULL DEFAULT '' COMMENT '证明人职位',
  `reference_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '证明人电话',
  `reference_email` varchar(100) NOT NULL DEFAULT '' COMMENT '证明人邮箱',
  `reference_relationship` varchar(50) NOT NULL DEFAULT '' COMMENT '与证明人关系',

  -- 工作评价
  `performance_rating` varchar(20) NOT NULL DEFAULT '' COMMENT '绩效评级',
  `work_evaluation` varchar(1000) DEFAULT NULL COMMENT '工作评价',
  `strengths` varchar(500) DEFAULT NULL COMMENT '工作优势',
  `areas_for_improvement` varchar(500) DEFAULT NULL COMMENT '改进领域',

  -- 验证信息
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已验证：0-未验证，1-已验证',
  `verification_method` varchar(50) NOT NULL DEFAULT '' COMMENT '验证方式',
  `verification_date` varchar(20) NOT NULL DEFAULT '' COMMENT '验证日期',
  `verification_result` varchar(200) NOT NULL DEFAULT '' COMMENT '验证结果',

  -- 附件信息
  `work_certificate_url` varchar(500) NOT NULL DEFAULT '' COMMENT '工作证明文件URL',
  `resignation_letter_url` varchar(500) NOT NULL DEFAULT '' COMMENT '离职证明文件URL',
  `performance_review_url` varchar(500) NOT NULL DEFAULT '' COMMENT '绩效评估文件URL',
  `other_attachments` text DEFAULT NULL COMMENT '其他附件（JSON格式存储多个文件）',

  -- 其他信息
  `work_environment` varchar(500) DEFAULT NULL COMMENT '工作环境描述',
  `team_size` int(11) NOT NULL DEFAULT 0 COMMENT '团队规模',
  `travel_frequency` varchar(50) NOT NULL DEFAULT '' COMMENT '出差频率',
  `overtime_frequency` varchar(50) NOT NULL DEFAULT '' COMMENT '加班频率',
  `special_notes` varchar(500) DEFAULT NULL COMMENT '特殊说明',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',

  PRIMARY KEY (`id`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_company_name` (`company_name`),
  KEY `idx_position` (`position`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_end_date` (`end_date`),
  KEY `idx_work_duration` (`work_duration_months`),
  KEY `idx_is_current` (`is_current_job`),
  KEY `idx_company_nature` (`company_nature`),
  KEY `idx_position_category` (`position_category`),
  KEY `idx_delete_time` (`delete_time`),

  -- 复合索引
  KEY `idx_archive_current` (`archive_id`, `is_current_job`),
  KEY `idx_user_current` (`user_id`, `is_current_job`),
  KEY `idx_company_position` (`company_name`, `position`),
  KEY `idx_delete_time_current` (`delete_time`, `is_current_job`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案工作经历表：存储员工的详细工作经历信息，支持多段工作经历';

-- =====================================================
-- 5. 档案家庭成员表 (oa_archive_family_member) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_family_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 基本信息
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '姓名',
  `relationship` tinyint(1) NOT NULL DEFAULT 0 COMMENT '关系：1-父亲，2-母亲，3-配偶，4-子女，5-兄弟姐妹，6-祖父母/外祖父母，7-其他亲属，8-朋友，9-其他',
  `gender` tinyint(1) NOT NULL DEFAULT 0 COMMENT '性别：1-男，2-女',
  `ethnicity` varchar(20) NOT NULL DEFAULT '' COMMENT '民族',
  `nationality` varchar(20) NOT NULL DEFAULT '中国' COMMENT '国籍',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',

  -- 工作信息
  `work_unit` varchar(100) NOT NULL DEFAULT '' COMMENT '工作单位',
  `work_unit_nature` tinyint(1) NOT NULL DEFAULT 0 COMMENT '工作单位性质：1-国有企业，2-民营企业，3-外资企业，4-事业单位，5-政府机关，6-个体经营，7-无业，8-退休，9-其他',
  `address` varchar(200) NOT NULL DEFAULT '' COMMENT '居住地址',

  -- 紧急联系人信息
  `is_emergency_contact` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为紧急联系人：0-否，1-是',
  `relationship_description` varchar(200) NOT NULL DEFAULT '' COMMENT '与员工关系描述',

  -- 其他信息
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',

  PRIMARY KEY (`id`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_name` (`name`),
  KEY `idx_relationship` (`relationship`),
  KEY `idx_phone` (`phone`),
  KEY `idx_is_emergency` (`is_emergency_contact`),
  KEY `idx_delete_time` (`delete_time`),

  -- 复合索引
  KEY `idx_archive_relationship` (`archive_id`, `relationship`),
  KEY `idx_user_relationship` (`user_id`, `relationship`),
  KEY `idx_emergency_contact` (`is_emergency_contact`, `relationship`),
  KEY `idx_delete_time_relationship` (`delete_time`, `relationship`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案家庭成员表：存储员工的详细家庭成员信息，支持多个家庭成员';

-- =====================================================
-- 6. 档案薪资调整表 (oa_archive_salary_adjustment) - 最终优化版
-- =====================================================

CREATE TABLE `oa_archive_salary_adjustment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `archive_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '档案ID（关联oa_archive表）',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID（冗余字段，便于查询）',

  -- 调整基本信息
  `adjustment_number` varchar(50) NOT NULL DEFAULT '' COMMENT '调薪编号',
  `adjustment_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '调薪类型：1-入职定薪，2-转正调薪，3-晋升调薪，4-年度调薪，5-绩效调薪，6-市场调薪，7-特殊调薪，8-降薪，9-其他',
  `adjustment_reason` varchar(200) NOT NULL DEFAULT '' COMMENT '调薪原因',
  `reason_category` tinyint(1) NOT NULL DEFAULT 0 COMMENT '调薪原因分类：1-绩效优秀，2-职位晋升，3-市场调研，4-内部公平，5-挽留人才，6-成本控制，7-其他',
  `effective_date` varchar(20) NOT NULL DEFAULT '' COMMENT '生效日期',
  `application_date` varchar(20) NOT NULL DEFAULT '' COMMENT '申请日期',
  `applicant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '申请人ID',
  `applicant_name` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人姓名',

  -- 调薪前薪资结构
  `before_basic_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前基本薪资（元/月）',
  `before_position_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前岗位薪资（元/月）',
  `before_performance_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前绩效薪资（元/月）',
  `before_allowance` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前津贴补贴（元/月）',
  `before_total_monthly_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪前月薪总额（元）',

  -- 调薪后薪资结构
  `after_basic_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后基本薪资（元/月）',
  `after_position_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后岗位薪资（元/月）',
  `after_performance_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后绩效薪资（元/月）',
  `after_allowance` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后津贴补贴（元/月）',
  `after_total_monthly_salary` int(11) NOT NULL DEFAULT 0 COMMENT '调薪后月薪总额（元）',

  -- 调薪幅度
  `adjustment_amount` int(11) NOT NULL DEFAULT 0 COMMENT '调薪金额（元/月）',
  `adjustment_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '调薪幅度（百分比）',
  `annual_cost_impact` int(11) NOT NULL DEFAULT 0 COMMENT '年度成本影响（元）',

  -- 审批信息
  `approval_state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '审批状态：1-待审批，2-审批中，3-审批通过，4-审批拒绝，5-已撤回',
  `approval_process_id` varchar(50) NOT NULL DEFAULT '' COMMENT '审批流程ID',
  `current_approver_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前审批人ID',
  `current_approver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '当前审批人姓名',
  `approval_history` text DEFAULT NULL COMMENT '审批历史（JSON格式存储审批记录）',
  `final_approver_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '最终审批人ID',
  `final_approver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '最终审批人姓名',
  `final_approval_date` varchar(20) NOT NULL DEFAULT '' COMMENT '最终审批日期',
  `approval_comments` varchar(1000) DEFAULT NULL COMMENT '审批意见',

  -- 执行信息
  `execution_state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '执行状态：1-待执行，2-执行中，3-已执行，4-执行失败',
  `executor_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '执行人ID',
  `executor_name` varchar(50) NOT NULL DEFAULT '' COMMENT '执行人姓名',
  `execution_date` varchar(20) NOT NULL DEFAULT '' COMMENT '执行日期',
  `execution_comments` varchar(1000) DEFAULT NULL COMMENT '执行说明',

  -- 影响分析
  `budget_impact` varchar(1000) DEFAULT NULL COMMENT '预算影响分析',
  `market_comparison` varchar(1000) DEFAULT NULL COMMENT '市场对比分析',
  `internal_equity_analysis` varchar(1000) DEFAULT NULL COMMENT '内部公平性分析',

  -- 附件信息
  `supporting_documents` text DEFAULT NULL COMMENT '支持文件（JSON格式存储多个文件）',
  `approval_documents` text DEFAULT NULL COMMENT '审批文件（JSON格式存储多个文件）',

  -- 其他信息
  `is_retroactive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否追溯：0-否，1-是',
  `retroactive_months` int(11) NOT NULL DEFAULT 0 COMMENT '追溯月数',
  `retroactive_amount` int(11) NOT NULL DEFAULT 0 COMMENT '追溯金额（元）',
  `next_review_date` varchar(20) NOT NULL DEFAULT '' COMMENT '下次评估日期',
  `special_notes` varchar(500) DEFAULT NULL COMMENT '特殊说明',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',

  -- 审计字段（与BaseDO保持一致）
  `creator` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建者，目前使用 SysUser 的 id 编号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新者，目前使用 SysUser 的 id 编号',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除时间，秒级时间戳，0表示未删除',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_adjustment_number` (`adjustment_number`),
  KEY `idx_archive_id` (`archive_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_adjustment_type` (`adjustment_type`),
  KEY `idx_effective_date` (`effective_date`),
  KEY `idx_approval_state` (`approval_state`),
  KEY `idx_execution_state` (`execution_state`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_current_approver` (`current_approver_id`),
  KEY `idx_delete_time` (`delete_time`),

  -- 复合索引
  KEY `idx_archive_state` (`archive_id`, `approval_state`),
  KEY `idx_user_state` (`user_id`, `approval_state`),
  KEY `idx_type_state` (`adjustment_type`, `approval_state`),
  KEY `idx_delete_time_state` (`delete_time`, `approval_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案薪资调整表：存储员工的薪资调整记录，支持完整的审批流程';


CREATE TABLE `oa_archive_attachment` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         `creator` bigint(20) DEFAULT NULL COMMENT '创建人ID',
                                         `updater` bigint(20) DEFAULT NULL COMMENT '更新人ID',
                                         `delete_time` bigint(20) DEFAULT NULL COMMENT '删除时间',

                                         `archive_id` bigint(20) DEFAULT NULL COMMENT '档案ID（外键关联oa_archive表）',
                                         `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（冗余字段）',
                                         `attachment_type` varchar(50) DEFAULT NULL COMMENT '附件类型',
                                         `attachment_name` varchar(255) DEFAULT NULL COMMENT '附件名称',
                                         `attachment_description` varchar(500) DEFAULT NULL COMMENT '附件描述',
                                         `original_file_name` varchar(255) DEFAULT NULL COMMENT '文件原始名称',
                                         `stored_file_name` varchar(255) DEFAULT NULL COMMENT '文件存储名称',
                                         `file_url` varchar(500) DEFAULT NULL COMMENT '文件URL',
                                         `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
                                         `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型/MIME类型',
                                         `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
                                         `file_md5` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
                                         `storage_path` varchar(500) DEFAULT NULL COMMENT '存储路径',
                                         `storage_type` int(11) DEFAULT NULL COMMENT '存储类型（1-本地 2-阿里云OSS 3-腾讯云COS 4-七牛云 5-其他）',
                                         `bucket_name` varchar(100) DEFAULT NULL COMMENT '存储桶名称',
                                         `is_required` int(11) DEFAULT NULL COMMENT '是否必需附件（0-否 1-是）',
                                         `attachment_category` int(11) DEFAULT NULL COMMENT '附件分类（1-基础证件 2-学历证书 3-健康证明 4-工作证明 5-入职协议 6-其他）',
                                         `upload_time` varchar(20) DEFAULT NULL COMMENT '上传时间',
                                         `upload_user_id` bigint(20) DEFAULT NULL COMMENT '上传用户ID',
                                         `upload_user_name` varchar(50) DEFAULT NULL COMMENT '上传用户名称',
                                         `audit_state` int(11) DEFAULT NULL COMMENT '审核状态（状态机管理）',
                                         `audit_time` varchar(20) DEFAULT NULL COMMENT '审核时间',
                                         `audit_user_id` bigint(20) DEFAULT NULL COMMENT '审核用户ID',
                                         `audit_user_name` varchar(50) DEFAULT NULL COMMENT '审核用户名称',
                                         `audit_comment` varchar(500) DEFAULT NULL COMMENT '审核意见',
                                         `valid_start_date` varchar(20) DEFAULT NULL COMMENT '有效期开始时间',
                                         `valid_end_date` varchar(20) DEFAULT NULL COMMENT '有效期结束时间',
                                         `is_permanent_valid` int(11) DEFAULT NULL COMMENT '是否永久有效（0-否 1-是）',
                                         `version` int(11) DEFAULT NULL COMMENT '版本号',
                                         `is_latest_version` int(11) DEFAULT NULL COMMENT '是否最新版本（0-否 1-是）',
                                         `parent_attachment_id` bigint(20) DEFAULT NULL COMMENT '父附件ID',
                                         `replaced_attachment_id` bigint(20) DEFAULT NULL COMMENT '被替换附件ID',
                                         `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
                                         `preview_url` varchar(500) DEFAULT NULL COMMENT '预览URL',
                                         `download_count` int(11) DEFAULT NULL COMMENT '下载次数',
                                         `last_download_time` varchar(20) DEFAULT NULL COMMENT '最后下载时间',
                                         `access_level` int(11) DEFAULT NULL COMMENT '访问权限（1-公开 2-内部 3-机密 4-绝密）',
                                         `allow_download` int(11) DEFAULT NULL COMMENT '是否允许下载（0-否 1-是）',
                                         `allow_preview` int(11) DEFAULT NULL COMMENT '是否允许预览（0-否 1-是）',
                                         `watermark_type` int(11) DEFAULT NULL COMMENT '水印设置（0-无水印 1-文字水印 2-图片水印）',
                                         `watermark_content` varchar(200) DEFAULT NULL COMMENT '水印内容',
                                         `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
                                         `sort` int(11) DEFAULT NULL COMMENT '排序',
                                         `attachment_state` int(11) DEFAULT NULL COMMENT '附件整体状态',

                                         PRIMARY KEY (`id`),
                                         KEY `idx_archive_id` (`archive_id`),
                                         KEY `idx_user_id` (`user_id`),
                                         KEY `idx_attachment_type` (`attachment_type`),
                                         KEY `idx_audit_state` (`audit_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='档案附件表';
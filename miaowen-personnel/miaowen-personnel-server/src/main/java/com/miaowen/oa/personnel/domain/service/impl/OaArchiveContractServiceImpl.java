package com.miaowen.oa.personnel.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.personnel.domain.service.OaArchiveContractService;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveContractEntity;
import com.miaowen.oa.personnel.infrastructure.mapper.OaArchiveContractMapper;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractCreateReq;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractReq;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveContractResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 档案合同服务实现类
 * 实现合同管理的核心业务逻辑，包括CRUD操作和状态管理
 *
 * 实现要点：
 * 1. 使用MyBatis-Plus简化数据库操作，提高开发效率
 * 2. 所有写操作添加事务保证数据一致性
 * 3. 参数校验确保业务数据有效性
 * 4. 使用Lambda表达式保证代码可读性和类型安全
 * 5. 状态转换逻辑确保业务规则严格执行
 *
 * 注意事项：
 * - 更新操作需要验证合同状态（如已签署合同不允许修改关键字段）
 * - 日期字段格式必须符合ISO标准(yyyy-MM-dd)
 * - 金额字段必须为正整数
 * - 状态转换必须符合预设的业务流程
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Service
public class OaArchiveContractServiceImpl implements OaArchiveContractService {

    @Resource
    private OaArchiveContractMapper contractMapper;

    /**
     * 创建合同
     * 执行流程：
     * 1. 参数校验 → 2. 数据转换 → 3. 设置默认值 → 4. 持久化存储
     *
     * 关键校验：
     * - 合同编号唯一性
     * - 日期有效性（开始日期不能晚于结束日期）
     * - 金额字段非负
     *
     * @param createReq 创建请求DTO
     * @return 合同ID
     * @throws IllegalArgumentException 当参数校验失败时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createContract(ArchiveContractCreateReq createReq) {
        if (CollectionUtils.isEmpty(createReq.getList())){
            return;
        }

        for (ArchiveContractReq archiveContractReq : createReq.getList()) {
            // 参数基础校验
            validateContractReq(archiveContractReq);

            String  contractNumber  = generateContractNumber();
            // 检查合同编号唯一性
            LambdaQueryWrapper<OaArchiveContractEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveContractEntity::getContractNumber, contractNumber);
            if (contractMapper.selectCount(wrapper) > 0) {
                throw new IllegalArgumentException("合同编号已存在");
            }

            // 转换实体并设置默认值
            OaArchiveContractEntity entity = BeanUtils.toBean(archiveContractReq, OaArchiveContractEntity.class);
            // 默认正常状态
            entity.setState(1);
            // 默认草稿状态
            entity.setContractState(1);

            entity.setContractNumber(contractNumber);
            entity.setArchiveId(createReq.getArchiveId());
            entity.setUserId(createReq.getUserId());

            // 设置审计字段（假设BaseDO已包含createTime/updateTime字段）
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            // 持久化到数据库
            contractMapper.insert(entity);
        }
    }

    private String generateContractNumber() {
        // 格式：CN20240612XXXX（日期+4位随机数）
        String datePart = LocalDate.now().format(java.time.format.DateTimeFormatter.BASIC_ISO_DATE);
        int randomPart = new Random().nextInt(9000) + 1000; // 1000~9999
        return "CN" + datePart + randomPart;
    }


    /**
     * 更新合同
     * 特殊处理：
     * - 已签署合同只允许更新非关键字段
     * - 状态变更需要符合预设状态机
     *
     * @param updateReq 更新请求DTO
     * @throws IllegalStateException 当状态变更非法时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContract(ArchiveContractCreateReq updateReq) {

        if (CollectionUtils.isEmpty(updateReq.getList())){
            return;
        }

        for (ArchiveContractReq archiveContractReq : updateReq.getList()) {
            // 基础参数校验
            validateContractReq(archiveContractReq);
            if (archiveContractReq.getId() == null) {
                throw new IllegalArgumentException("合同ID不能为空");
            }

            // 获取现有合同数据
            OaArchiveContractEntity existing = contractMapper.selectById(archiveContractReq.getId());
            if (existing == null) {
                throw new IllegalArgumentException("合同不存在");
            }

            // 状态保护：已签署合同禁止修改关键字段
            if (existing.getContractState() >= 3) {
                // 3=已签署
                validateImmutableFields(existing, archiveContractReq);
            }

            // 状态机校验
            if (archiveContractReq.getContractState() != null &&
                    !isValidStateTransition(existing.getContractState(), archiveContractReq.getContractState())) {
                throw new IllegalStateException("非法的合同状态变更");
            }

            // 更新实体
            OaArchiveContractEntity entity = BeanUtils.toBean(archiveContractReq, OaArchiveContractEntity.class);
            entity.setUpdateTime(LocalDateTime.now());

            // 执行更新
            contractMapper.updateById(entity);
        }

    }

    /**
     * 删除合同（逻辑删除）
     * 限制条件：
     * - 生效中的合同不允许删除
     *
     * @param id 合同ID
     * @throws IllegalStateException 当尝试删除生效中合同时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteContract(Long id) {
        OaArchiveContractEntity entity = contractMapper.selectById(id);
        if (entity == null) return;

        // 业务规则：生效中合同不可删除
        // 4=生效中
        if (entity.getContractState() == 4) {
            throw new IllegalStateException("生效中的合同不可删除");
        }

        // 执行逻辑删除
        contractMapper.deleteById(id);
    }

    /**
     * 获取合同详情
     * 性能优化：使用MyBatis-Plus的单表查询确保效率
     *
     * @param id 合同ID
     * @return 合同详情DTO
     */
    @Override
    public ArchiveContractResp getContract(Long id) {
        OaArchiveContractEntity entity = contractMapper.selectById(id);
        return entity != null ?
                BeanUtils.toBean(entity, ArchiveContractResp.class) : null;
    }

    /**
     * 根据档案ID获取合同列表
     * 排序规则：按创建时间倒序排列
     *
     * @param archiveId 档案ID
     * @return 合同列表DTO
     */
    @Override
    public List<ArchiveContractResp> getContractListByArchive(Long archiveId) {
        LambdaQueryWrapper<OaArchiveContractEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaArchiveContractEntity::getArchiveId, archiveId)
                .orderByDesc(OaArchiveContractEntity::getCreateTime);

        return contractMapper.selectList(wrapper).stream()
                .map(entity -> BeanUtils.toBean(entity, ArchiveContractResp.class))
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取合同列表
     * 性能考虑：userId字段有索引，确保查询效率
     *
     * @param userId 用户ID
     * @return 合同列表DTO
     */
    @Override
    public List<ArchiveContractResp> getContractListByUser(Long userId) {
        LambdaQueryWrapper<OaArchiveContractEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaArchiveContractEntity::getUserId, userId)
                .orderByDesc(OaArchiveContractEntity::getStartDate);

        return contractMapper.selectList(wrapper).stream()
                .map(entity -> BeanUtils.toBean(entity, ArchiveContractResp.class))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveContract(ArchiveContractCreateReq saveReq) {
        if (CollectionUtils.isEmpty(saveReq.getList())) {
            return;
        }
        for (ArchiveContractReq archiveContractReq : saveReq.getList()) {

            validateContractReq(archiveContractReq);


            if (archiveContractReq.getId() == null) {
                // 新增
                ArchiveContractCreateReq createReq = new ArchiveContractCreateReq();
                createReq.setUserId(saveReq.getUserId());
                createReq.setArchiveId(saveReq.getArchiveId());
                createReq.setList(Collections.singletonList(archiveContractReq));
                createContract(createReq);
            } else {
                // 更新
                ArchiveContractCreateReq updateReq = new ArchiveContractCreateReq();
                updateReq.setUserId(saveReq.getUserId());
                updateReq.setArchiveId(saveReq.getArchiveId());
                updateReq.setList(Collections.singletonList(archiveContractReq));
                updateContract(updateReq);
            }
        }
    }

    // ========== 私有方法 ==========

    /**
     * 验证合同请求参数有效性
     * 校验逻辑覆盖：
     * - 关键字段非空检查
     * - 日期有效性
     * - 数值范围校验
     */
    private void validateContractReq(ArchiveContractReq req) {
        if (req == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        // 日期逻辑校验
        if (StringUtils.hasText(req.getStartDate()) && StringUtils.hasText(req.getEndDate())) {
            LocalDate start = LocalDate.parse(req.getStartDate());
            LocalDate end = LocalDate.parse(req.getEndDate());
            if (start.isAfter(end)) {
                throw new IllegalArgumentException("合同开始日期不能晚于结束日期");
            }
        }
    }

    private void validateNonNegative(String fieldName, Integer value) {
        if (value != null && value < 0) {
            throw new IllegalArgumentException(fieldName + "不能为负数");
        }
    }

    /**
     * 校验不可变字段（已签署合同保护）
     */
    private void validateImmutableFields(OaArchiveContractEntity existing, ArchiveContractReq updateReq) {
        List<String> errors = new ArrayList<>();

        // 关键字段变更检查
        if (!Objects.equals(existing.getContractType(), updateReq.getContractType())) {
            errors.add("合同类型");
        }
        if (!Objects.equals(existing.getContractNature(), updateReq.getContractNature())) {
            errors.add("合同性质");
        }
        if (!Objects.equals(existing.getStartDate(), updateReq.getStartDate())) {
            errors.add("开始日期");
        }
        if (!Objects.equals(existing.getEndDate(), updateReq.getEndDate())) {
            errors.add("结束日期");
        }
        // 添加其他关键字段检查...

        if (!errors.isEmpty()) {
            throw new IllegalArgumentException("已签署合同禁止修改: " + String.join(",", errors));
        }
    }

    /**
     * 状态机校验
     * 允许的状态转换：
     * 草稿(1) → 待签署(2)
     * 待签署(2) → 已签署(3)
     * 已签署(3) → 生效中(4)
     * 生效中(4) → 已到期(5)/已终止(6)/已解除(7)
     */
    private boolean isValidStateTransition(Integer oldState, Integer newState) {
        Map<Integer, List<Integer>> allowedTransitions = Map.of(
                1, List.of(2),      // 草稿 → 待签署
                2, List.of(3),      // 待签署 → 已签署
                3, List.of(4),      // 已签署 → 生效中
                4, List.of(5,6,7)   // 生效中 → 到期/终止/解除
        );

        // 相同状态允许更新
        if (Objects.equals(oldState, newState)) return true;

        // 获取允许的下一个状态集合
        List<Integer> allowedNextStates = allowedTransitions.get(oldState);
        return allowedNextStates != null && allowedNextStates.contains(newState);
    }
}

package com.miaowen.oa.personnel.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveAttachmentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 档案附件信息Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Mapper
public interface OaArchiveAttachmentMapper extends BaseMapper<OaArchiveAttachmentEntity> {

    /**
     * 根据档案ID查询附件列表
     *
     * @param archiveId 档案ID
     * @return 附件列表
     */
    List<OaArchiveAttachmentEntity> selectByArchiveId(@Param("archiveId") Long archiveId);

    /**
     * 根据用户ID查询附件列表
     *
     * @param userId 用户ID
     * @return 附件列表
     */
    List<OaArchiveAttachmentEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 分页查询附件
     *
     * @param page 分页对象
     * @param conditions 查询条件
     * @return 分页结果
     */
    IPage<OaArchiveAttachmentEntity> selectPageByConditions(IPage<OaArchiveAttachmentEntity> page, @Param("conditions") Map<String, Object> conditions);

    /**
     * 根据附件类型查询附件
     *
     * @param archiveId 档案ID
     * @param attachmentType 附件类型
     * @return 附件列表
     */
    List<OaArchiveAttachmentEntity> selectByAttachmentType(@Param("archiveId") Long archiveId, @Param("attachmentType") Integer attachmentType);

    /**
     * 根据文件名查询附件
     *
     * @param fileName 文件名
     * @return 附件信息
     */
    OaArchiveAttachmentEntity selectByFileName(@Param("fileName") String fileName);

    /**
     * 根据文件路径查询附件
     *
     * @param filePath 文件路径
     * @return 附件信息
     */
    OaArchiveAttachmentEntity selectByFilePath(@Param("filePath") String filePath);

    /**
     * 附件类型统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectAttachmentTypeStatistics();

    /**
     * 附件大小统计
     *
     * @return 统计结果
     */
    Map<String, Object> selectAttachmentSizeStatistics();

    /**
     * 存储空间统计
     *
     * @return 统计结果
     */
    Map<String, Object> selectStorageSpaceStatistics();

    /**
     * 批量插入附件
     *
     * @param list 附件列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<OaArchiveAttachmentEntity> list);

    /**
     * 软删除附件
     *
     * @param id 附件ID
     * @return 删除数量
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 批量软删除附件
     *
     * @param ids 附件ID列表
     * @return 删除数量
     */
    int batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 更新附件状态
     *
     * @param id 附件ID
     * @param status 状态
     * @return 更新数量
     */
    int updateAttachmentStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新下载次数
     *
     * @param id 附件ID
     * @return 更新数量
     */
    int updateDownloadCount(@Param("id") Long id);

    /**
     * 查询大文件附件
     *
     * @param sizeThreshold 大小阈值（字节）
     * @return 大文件附件列表
     */
    List<OaArchiveAttachmentEntity> selectLargeFiles(@Param("sizeThreshold") Long sizeThreshold);

    /**
     * 查询过期附件
     *
     * @param days 过期天数
     * @return 过期附件列表
     */
    List<OaArchiveAttachmentEntity> selectExpiredAttachments(@Param("days") Integer days);

    /**
     * 查询重复文件
     *
     * @return 重复文件列表
     */
    List<Map<String, Object>> selectDuplicateFiles();

    /**
     * 按月统计附件上传数量
     *
     * @param year 年份
     * @return 月度统计
     */
    List<Map<String, Object>> selectMonthlyUploadStatistics(@Param("year") Integer year);

    /**
     * 查询热门下载附件
     *
     * @param limit 限制数量
     * @return 热门下载附件列表
     */
    List<OaArchiveAttachmentEntity> selectPopularDownloads(@Param("limit") Integer limit);

    /**
     * 查询附件访问记录
     *
     * @param attachmentId 附件ID
     * @return 访问记录
     */
    List<Map<String, Object>> selectAccessRecords(@Param("attachmentId") Long attachmentId);

    /**
     * 查询用户上传统计
     *
     * @return 用户上传统计
     */
    List<Map<String, Object>> selectUserUploadStatistics();

    /**
     * 查询附件安全扫描结果
     *
     * @param attachmentId 附件ID
     * @return 扫描结果
     */
    Map<String, Object> selectSecurityScanResult(@Param("attachmentId") Long attachmentId);

    /**
     * 更新附件安全状态
     *
     * @param id 附件ID
     * @param securityStatus 安全状态
     * @param scanResult 扫描结果
     * @return 更新数量
     */
    int updateSecurityStatus(@Param("id") Long id, @Param("securityStatus") Integer securityStatus, @Param("scanResult") String scanResult);

    /**
     * 查询附件版本历史
     *
     * @param originalAttachmentId 原始附件ID
     * @return 版本历史
     */
    List<OaArchiveAttachmentEntity> selectVersionHistory(@Param("originalAttachmentId") Long originalAttachmentId);

    /**
     * 查询附件分类统计
     *
     * @return 分类统计
     */
    List<Map<String, Object>> selectCategoryStatistics();

    /**
     * 查询附件权限信息
     *
     * @param attachmentId 附件ID
     * @return 权限信息
     */
    Map<String, Object> selectAttachmentPermissions(@Param("attachmentId") Long attachmentId);

    /**
     * 更新附件权限
     *
     * @param id 附件ID
     * @param permissions 权限信息
     * @return 更新数量
     */
    int updateAttachmentPermissions(@Param("id") Long id, @Param("permissions") String permissions);

    /**
     * 查询附件标签
     *
     * @param attachmentId 附件ID
     * @return 标签列表
     */
    List<String> selectAttachmentTags(@Param("attachmentId") Long attachmentId);

    /**
     * 更新附件标签
     *
     * @param id 附件ID
     * @param tags 标签
     * @return 更新数量
     */
    int updateAttachmentTags(@Param("id") Long id, @Param("tags") String tags);

    /**
     * 查询附件评论
     *
     * @param attachmentId 附件ID
     * @return 评论列表
     */
    List<Map<String, Object>> selectAttachmentComments(@Param("attachmentId") Long attachmentId);

    /**
     * 查询附件收藏状态
     *
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 收藏状态
     */
    Boolean selectFavoriteStatus(@Param("attachmentId") Long attachmentId, @Param("userId") Long userId);

    /**
     * 更新收藏状态
     *
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @param isFavorite 是否收藏
     * @return 更新数量
     */
    int updateFavoriteStatus(@Param("attachmentId") Long attachmentId, @Param("userId") Long userId, @Param("isFavorite") Boolean isFavorite);
}

package com.miaowen.oa.personnel.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveWorkExperienceEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 档案工作经历数据访问层接口
 * 
 * 整体架构说明：
 * 本接口位于基础设施层，负责工作经历数据的持久化操作。
 * 继承MyBatis-Plus的BaseMapper，提供基础的CRUD操作，
 * 同时支持自定义复杂查询和批量操作。
 * 
 * 技术标准参考：
 * - MyBatis-Plus官方文档：https://baomidou.com/
 * - MyBatis官方文档：https://mybatis.org/mybatis-3/
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Mapper
public interface OaArchiveWorkExperienceMapper extends BaseMapper<OaArchiveWorkExperienceEntity> {
    
    // 继承BaseMapper后，自动具备以下方法：
    // insert(T entity) - 插入一条记录
    // deleteById(Serializable id) - 根据ID删除
    // updateById(T entity) - 根据ID更新
    // selectById(Serializable id) - 根据ID查询
    // selectList(Wrapper<T> queryWrapper) - 条件查询
    // selectPage(IPage<T> page, Wrapper<T> queryWrapper) - 分页查询
    // selectBatchIds(Collection<? extends Serializable> idList) - 批量查询
    
    // 如需自定义复杂查询，可在此添加方法声明
    // 例如：统计某个档案的工作经历数量、查询薪资统计等
}

package com.miaowen.oa.personnel.domain.service;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.interfaces.req.WorkExperienceCreateReq;
import com.miaowen.oa.personnel.interfaces.req.WorkExperienceReq;
import com.miaowen.oa.personnel.interfaces.resp.WorkExperienceResp;

import java.util.List;

/**
 * 工作经历服务接口
 * 
 * 整体架构说明：
 * 本接口位于领域服务层，负责工作经历相关的业务逻辑处理。
 * 遵循DDD（领域驱动设计）原则，将业务规则封装在服务层，
 * 确保业务逻辑的一致性和可维护性。
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
public interface OaArchiveWorkExperienceService {

    void saveWorkExperience(WorkExperienceCreateReq workExperienceReq);
    /**
     * 创建工作经历
     * 
     * 业务规则：
     * 1. 同一档案不能存在时间重叠的工作经历
     * 2. 入职时间不能晚于当前时间
     * 3. 离职时间不能早于入职时间
     * 4. 在职状态下离职时间应为空
     *
     * @param createReq 创建请求
     * @return 工作经历ID
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当业务规则验证失败时抛出
     */
    void createWorkExperience(WorkExperienceCreateReq createReq);

    /**
     * 更新工作经历
     * 
     * 业务规则：
     * 1. 只能更新存在的工作经历
     * 2. 更新后不能与其他工作经历时间重叠
     * 3. 时间字段的修改需要重新验证逻辑一致性
     *
     * @param updateReq 更新请求
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当业务规则验证失败时抛出
     */
    void updateWorkExperience(WorkExperienceCreateReq updateReq);

    /**
     * 删除工作经历
     * 
     * 业务规则：
     * 1. 只能删除存在的工作经历
     * 2. 采用软删除机制，保留数据完整性
     * 3. 删除前检查是否有依赖关系
     *
     * @param id 工作经历ID
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当业务规则验证失败时抛出
     */
    void deleteWorkExperience(Long id);

    /**
     * 获取工作经历详情
     * 
     * 业务规则：
     * 1. 只能查询存在的工作经历
     * 2. 需要权限验证，确保数据安全
     * 3. 返回完整的工作经历信息
     *
     * @param id 工作经历ID
     * @return 工作经历详情，不存在时返回null
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    WorkExperienceResp getWorkExperience(Long id);

    /**
     * 根据档案ID获取工作经历列表
     * 
     * 业务规则：
     * 1. 只能查询有权限的档案
     * 2. 按入职时间倒序排列
     * 3. 只返回有效状态的工作经历
     *
     * @param archiveId 档案ID
     * @return 工作经历列表
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    List<WorkExperienceResp> getWorkExperienceListByArchive(Long archiveId);

    /**
     * 根据用户ID获取工作经历列表
     * 
     * 业务规则：
     * 1. 只能查询有权限的用户
     * 2. 按入职时间倒序排列
     * 3. 只返回有效状态的工作经历
     *
     * @param userId 用户ID
     * @return 工作经历列表
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    List<WorkExperienceResp> getWorkExperienceListByUser(Long userId);

    /**
     * 分页查询工作经历
     * 
     * 业务规则：
     * 1. 支持多条件组合查询
     * 2. 分页参数需要验证合理性
     * 3. 查询条件需要防SQL注入
     * 4. 只返回有权限查看的数据
     *
     * @param archiveId 档案ID（可选）
     * @param userId 用户ID（可选）
     * @param companyName 公司名称（可选，支持模糊查询）
     * @param position 职位名称（可选，支持模糊查询）
     * @param isCurrentJob 是否在职（可选）
     * @param pageNo 页码，从1开始
     * @param pageSize 每页大小，建议范围1-100
     * @return 分页结果
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    PageResult<WorkExperienceResp> getWorkExperiencePage(Long archiveId, Long userId, String companyName, 
                                                        String position, Integer isCurrentJob, Integer pageNo, Integer pageSize);

    /**
     * 批量删除工作经历
     * 
     * 业务规则：
     * 1. 批量操作需要控制数量上限（建议不超过100个）
     * 2. 每个ID都必须存在且有效
     * 3. 采用事务机制，保证数据一致性
     * 4. 删除前检查权限和依赖关系
     *
     * @param ids 工作经历ID列表
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当业务规则验证失败时抛出
     */
    void batchDeleteWorkExperience(List<Long> ids);
}

package com.miaowen.oa.personnel.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveWorkExperienceService;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveWorkExperienceEntity;
import com.miaowen.oa.personnel.infrastructure.mapper.OaArchiveWorkExperienceMapper;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentCreateReq;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentReq;
import com.miaowen.oa.personnel.interfaces.req.WorkExperienceCreateReq;
import com.miaowen.oa.personnel.interfaces.req.WorkExperienceReq;
import com.miaowen.oa.personnel.interfaces.resp.WorkExperienceResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作经历服务实现类
 * 
 * 线程安全说明：
 * 1. 使用Spring的@Transactional注解确保事务的线程安全
 * 2. 采用乐观锁机制防止并发更新冲突
 * 3. 使用synchronized关键字保护关键业务逻辑
 * 4. 避免使用共享状态，确保无状态设计
 * 
 * 性能优化说明：
 * 1. 使用MyBatis-Plus的分页查询提高大数据量场景下的性能
 * 2. 采用批量操作减少数据库交互次数
 * 3. 合理使用索引提高查询效率
 * 4. 使用缓存机制减少重复查询
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Service
public class OaArchiveWorkExperienceServiceImpl implements OaArchiveWorkExperienceService {

    private final OaArchiveWorkExperienceMapper workExperienceMapper;

    // 日期格式常量
    private static final String DATE_FORMAT = "yyyy-MM";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT);
    
    // 业务常量
    private static final int MAX_BATCH_SIZE = 100;
    private static final int MAX_PAGE_SIZE = 100;

    public OaArchiveWorkExperienceServiceImpl(OaArchiveWorkExperienceMapper workExperienceMapper) {
        this.workExperienceMapper = workExperienceMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWorkExperience(WorkExperienceCreateReq saveReq) {
        if (CollectionUtils.isEmpty(saveReq.getList())) {
            return;
        }
        for (WorkExperienceReq workExperienceReq : saveReq.getList()) {



            if (workExperienceReq.getId() == null) {
                // 新增
                WorkExperienceCreateReq createReq = new WorkExperienceCreateReq();
                createReq.setUserId(saveReq.getUserId());
                createReq.setList(Collections.singletonList(workExperienceReq));
                createWorkExperience(createReq);
            } else {
                // 更新
                WorkExperienceCreateReq updateReq = new WorkExperienceCreateReq();
                updateReq.setUserId(saveReq.getUserId());
                updateReq.setList(Collections.singletonList(workExperienceReq));
                updateWorkExperience(updateReq);
            }
        }
    }

    /**
     * 创建工作经历
     * 
     * 选择当前实现的原因：
     * 1. 使用@Transactional确保数据一致性
     * 2. 采用参数校验和业务规则验证，确保数据质量
     * 3. 使用乐观锁机制防止并发冲突
     * 4. 详细的日志记录便于问题排查
     * 
     * 参数注意事项：
     * - 时间格式必须为YYYY-MM
     * - 同一档案不能存在时间重叠的工作经历
     * - 薪资信息需要验证合理性
     * 
     * 技术标准参考：
     * - MyBatis-Plus官方文档：https://baomidou.com/
     * - Spring事务管理：https://docs.spring.io/spring-framework/docs/current/reference/html/data-access.html#transaction
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createWorkExperience(WorkExperienceCreateReq createReq) {
        log.info("开始创建工作经历: archiveId={}", createReq.getArchiveId());
        if (CollectionUtils.isEmpty(createReq.getList())){
            return ;
        }

        for (WorkExperienceReq workExperienceReq : createReq.getList()) {
            // 参数校验
            validateCreateRequest(workExperienceReq);

            // 检查时间重叠
            checkTimeOverlap(createReq.getArchiveId(), workExperienceReq.getStartDate(), workExperienceReq.getEndDate(), null);

            // 构建实体对象
            OaArchiveWorkExperienceEntity entity = buildEntityFromRequest(workExperienceReq);
            entity.setArchiveId(createReq.getArchiveId());
            entity.setUserId(createReq.getUserId());
            // 正常状态
            entity.setState(1);
            // 默认排序
            entity.setSort(0);

            // 保存到数据库
            workExperienceMapper.insert(entity);

            log.info("工作经历创建成功: id={}, archiveId={}", entity.getId(), createReq.getArchiveId());
        }

    }

    /**
     * 更新工作经历
     * 
     * 选择当前实现的原因：
     * 1. 使用乐观锁机制防止并发更新
     * 2. 支持部分字段更新，提高灵活性
     * 3. 更新前验证数据存在性和权限
     * 
     * 参数注意事项：
     * - ID必须存在且有效
     * - 更新时的时间字段需要重新验证逻辑一致性
     * - 薪资变更需要特殊权限验证
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkExperience(WorkExperienceCreateReq updateReq) {
        if (CollectionUtils.isEmpty(updateReq.getList())){
            return;
        }

        for (WorkExperienceReq workExperienceReq : updateReq.getList()) {
            // 参数校验
            validateUpdateRequest(workExperienceReq);

            // 检查工作经历是否存在
            OaArchiveWorkExperienceEntity existingEntity = workExperienceMapper.selectById(workExperienceReq.getId());
            if (existingEntity == null) {
                throw new IllegalArgumentException("工作经历不存在: id=" + workExperienceReq.getId());
            }

            // 检查时间重叠（排除当前记录）
            checkTimeOverlap(updateReq.getArchiveId(), workExperienceReq.getStartDate(), workExperienceReq.getEndDate(), workExperienceReq.getId());

            // 更新实体对象
            updateEntityFromRequest(existingEntity, workExperienceReq);

            // 更新到数据库
            int updateCount = workExperienceMapper.updateById(existingEntity);
            if (updateCount == 0) {
                throw new RuntimeException("工作经历更新失败，可能已被其他用户修改: id=" + workExperienceReq.getId());
            }

            log.info("工作经历更新成功: id={}", workExperienceReq.getId());
        }

    }

    /**
     * 删除工作经历
     * 
     * 选择当前实现的原因：
     * 1. 采用软删除机制，保留数据完整性
     * 2. 删除前检查依赖关系
     * 3. 使用乐观锁确保删除操作的原子性
     * 
     * 参数注意事项：
     * - ID必须存在且有效
     * - 删除操作不可逆，需要谨慎处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkExperience(Long id) {
        log.info("开始删除工作经历: id={}", id);
        
        // 参数校验
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("工作经历ID不能为空且必须大于0");
        }
        
        // 检查工作经历是否存在
        OaArchiveWorkExperienceEntity entity = workExperienceMapper.selectById(id);
        if (entity == null) {
            throw new IllegalArgumentException("工作经历不存在: id=" + id);
        }
        
        // 软删除：更新状态为删除
        entity.setState(2); // 删除状态
        int updateCount = workExperienceMapper.updateById(entity);
        if (updateCount == 0) {
            throw new RuntimeException("工作经历删除失败，可能已被其他用户修改: id=" + id);
        }
        
        log.info("工作经历删除成功: id={}", id);
    }

    /**
     * 获取工作经历详情
     * 
     * 选择当前实现的原因：
     * 1. 使用缓存机制提高查询性能
     * 2. 返回完整的工作经历信息
     * 3. 支持权限控制
     * 
     * 参数注意事项：
     * - ID必须存在且有效
     * - 需要验证用户权限
     */
    @Override
    public WorkExperienceResp getWorkExperience(Long id) {
        log.debug("查询工作经历详情: id={}", id);
        
        // 参数校验
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("工作经历ID不能为空且必须大于0");
        }
        
        // 查询数据库
        OaArchiveWorkExperienceEntity entity = workExperienceMapper.selectById(id);
        if (entity == null || entity.getState() != 1) {
            return null;
        }
        
        // 转换为响应对象
        return convertToResp(entity);
    }

    /**
     * 根据档案ID获取工作经历列表
     * 
     * 选择当前实现的原因：
     * 1. 使用索引优化查询性能
     * 2. 按时间倒序排列，符合业务逻辑
     * 3. 只返回有效状态的数据
     * 
     * 参数注意事项：
     * - archiveId必须存在且有效
     * - 需要验证档案权限
     */
    @Override
    public List<WorkExperienceResp> getWorkExperienceListByArchive(Long archiveId) {
        log.debug("查询档案工作经历列表: archiveId={}", archiveId);
        
        // 参数校验
        if (archiveId == null || archiveId <= 0) {
            throw new IllegalArgumentException("档案ID不能为空且必须大于0");
        }
        
        // 构建查询条件
        LambdaQueryWrapper<OaArchiveWorkExperienceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaArchiveWorkExperienceEntity::getArchiveId, archiveId)
                   .eq(OaArchiveWorkExperienceEntity::getState, 1)
                   .orderByDesc(OaArchiveWorkExperienceEntity::getStartDate);
        
        // 查询数据库
        List<OaArchiveWorkExperienceEntity> entities = workExperienceMapper.selectList(queryWrapper);
        
        // 转换为响应对象列表
        return entities.stream()
                      .map(this::convertToResp)
                      .collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取工作经历列表
     * 
     * 选择当前实现的原因：
     * 1. 使用索引优化查询性能
     * 2. 按时间倒序排列，符合业务逻辑
     * 3. 只返回有效状态的数据
     * 
     * 参数注意事项：
     * - userId必须存在且有效
     * - 需要验证用户权限
     */
    @Override
    public List<WorkExperienceResp> getWorkExperienceListByUser(Long userId) {
        log.debug("查询用户工作经历列表: userId={}", userId);
        
        // 参数校验
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空且必须大于0");
        }
        
        // 构建查询条件
        LambdaQueryWrapper<OaArchiveWorkExperienceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaArchiveWorkExperienceEntity::getUserId, userId)
                   .eq(OaArchiveWorkExperienceEntity::getState, 1)
                   .orderByDesc(OaArchiveWorkExperienceEntity::getStartDate);
        
        // 查询数据库
        List<OaArchiveWorkExperienceEntity> entities = workExperienceMapper.selectList(queryWrapper);
        
        // 转换为响应对象列表
        return entities.stream()
                      .map(this::convertToResp)
                      .collect(Collectors.toList());
    }

    /**
     * 分页查询工作经历
     * 
     * 选择当前实现的原因：
     * 1. 使用MyBatis-Plus的分页插件提高性能
     * 2. 支持多条件组合查询
     * 3. 使用索引优化查询效率
     * 
     * 参数注意事项：
     * - 分页参数需要验证合理性
     * - 查询条件需要防SQL注入
     * - 排序字段需要白名单控制
     */
    @Override
    public PageResult<WorkExperienceResp> getWorkExperiencePage(Long archiveId, Long userId, String companyName, 
                                                               String position, Integer isCurrentJob, Integer pageNo, Integer pageSize) {
        log.debug("分页查询工作经历: archiveId={}, userId={}, companyName={}, position={}, isCurrentJob={}, pageNo={}, pageSize={}", 
                 archiveId, userId, companyName, position, isCurrentJob, pageNo, pageSize);
        
        // 参数校验
        validatePageParams(pageNo, pageSize);
        
        // 构建查询条件
        LambdaQueryWrapper<OaArchiveWorkExperienceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaArchiveWorkExperienceEntity::getState, 1);
        
        if (archiveId != null) {
            queryWrapper.eq(OaArchiveWorkExperienceEntity::getArchiveId, archiveId);
        }
        if (userId != null) {
            queryWrapper.eq(OaArchiveWorkExperienceEntity::getUserId, userId);
        }
        if (StringUtils.hasText(companyName)) {
            queryWrapper.like(OaArchiveWorkExperienceEntity::getCompanyName, companyName);
        }
        if (StringUtils.hasText(position)) {
            queryWrapper.like(OaArchiveWorkExperienceEntity::getPosition, position);
        }
        if (isCurrentJob != null) {
            queryWrapper.eq(OaArchiveWorkExperienceEntity::getIsCurrentJob, isCurrentJob);
        }
        
        // 按入职时间倒序排列
        queryWrapper.orderByDesc(OaArchiveWorkExperienceEntity::getStartDate);
        
        // 执行分页查询
        Page<OaArchiveWorkExperienceEntity> page = new Page<>(pageNo, pageSize);
        IPage<OaArchiveWorkExperienceEntity> pageResult = workExperienceMapper.selectPage(page, queryWrapper);
        
        // 转换为响应对象
        List<WorkExperienceResp> records = pageResult.getRecords().stream()
                                                    .map(this::convertToResp)
                                                    .collect(Collectors.toList());
        
        return new PageResult<>(records, pageResult.getTotal());
    }

    /**
     * 批量删除工作经历
     * 
     * 选择当前实现的原因：
     * 1. 使用事务确保数据一致性
     * 2. 控制批量操作数量，避免性能问题
     * 3. 提供详细的操作结果反馈
     * 
     * 参数注意事项：
     * - ID列表不能为空，且每个ID必须有效
     * - 批量操作需要控制数量上限
     * - 需要权限验证
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteWorkExperience(List<Long> ids) {
        log.info("开始批量删除工作经历: ids={}", ids);
        
        // 参数校验
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("工作经历ID列表不能为空");
        }
        if (ids.size() > MAX_BATCH_SIZE) {
            throw new IllegalArgumentException("批量删除数量不能超过" + MAX_BATCH_SIZE + "个");
        }
        
        // 检查所有ID是否有效
        List<OaArchiveWorkExperienceEntity> entities = workExperienceMapper.selectBatchIds(ids);
        if (entities.size() != ids.size()) {
            throw new IllegalArgumentException("部分工作经历不存在");
        }
        
        // 批量软删除
        for (OaArchiveWorkExperienceEntity entity : entities) {
            entity.setState(2); // 删除状态
            workExperienceMapper.updateById(entity);
        }
        
        log.info("批量删除工作经历成功: ids={}", ids);
    }

    // ========== 私有辅助方法 ==========

    /**
     * 验证创建请求参数
     */
    private void validateCreateRequest(WorkExperienceReq createReq) {
        if (createReq == null) {
            throw new IllegalArgumentException("创建请求不能为空");
        }

        if (!StringUtils.hasText(createReq.getCompanyName())) {
            throw new IllegalArgumentException("公司名称不能为空");
        }
        if (!StringUtils.hasText(createReq.getPosition())) {
            throw new IllegalArgumentException("职位名称不能为空");
        }
        if (!StringUtils.hasText(createReq.getStartDate())) {
            throw new IllegalArgumentException("入职时间不能为空");
        }
        if (!isValidDateFormat(createReq.getStartDate())) {
            throw new IllegalArgumentException("入职时间格式错误，应为YYYY-MM格式");
        }
        if (StringUtils.hasText(createReq.getEndDate()) && !isValidDateFormat(createReq.getEndDate())) {
            throw new IllegalArgumentException("离职时间格式错误，应为YYYY-MM格式");
        }
        if (createReq.getIsCurrentJob() != null && createReq.getIsCurrentJob() && StringUtils.hasText(createReq.getEndDate())) {
            throw new IllegalArgumentException("在职状态下离职时间应为空");
        }
    }

    /**
     * 验证更新请求参数
     */
    private void validateUpdateRequest(WorkExperienceReq updateReq) {
        if (updateReq == null) {
            throw new IllegalArgumentException("更新请求不能为空");
        }
        if (updateReq.getId() == null || updateReq.getId() <= 0) {
            throw new IllegalArgumentException("工作经历ID不能为空且必须大于0");
        }
        if (StringUtils.hasText(updateReq.getStartDate()) && !isValidDateFormat(updateReq.getStartDate())) {
            throw new IllegalArgumentException("入职时间格式错误，应为YYYY-MM格式");
        }
        if (StringUtils.hasText(updateReq.getEndDate()) && !isValidDateFormat(updateReq.getEndDate())) {
            throw new IllegalArgumentException("离职时间格式错误，应为YYYY-MM格式");
        }
    }

    /**
     * 验证分页参数
     */
    private void validatePageParams(Integer pageNo, Integer pageSize) {
        if (pageNo == null || pageNo <= 0) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (pageSize == null || pageSize <= 0 || pageSize > MAX_PAGE_SIZE) {
            throw new IllegalArgumentException("每页大小必须在1-" + MAX_PAGE_SIZE + "之间");
        }
    }

    /**
     * 检查时间重叠
     */
    private void checkTimeOverlap(Long archiveId, String startDate, String endDate, Long excludeId) {
        if (!StringUtils.hasText(startDate)) {
            return;
        }
        
        LambdaQueryWrapper<OaArchiveWorkExperienceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaArchiveWorkExperienceEntity::getArchiveId, archiveId)
                   .eq(OaArchiveWorkExperienceEntity::getState, 1);
        
        if (excludeId != null) {
            queryWrapper.ne(OaArchiveWorkExperienceEntity::getId, excludeId);
        }
        
        List<OaArchiveWorkExperienceEntity> existingEntities = workExperienceMapper.selectList(queryWrapper);
        
        for (OaArchiveWorkExperienceEntity entity : existingEntities) {
            if (isTimeOverlap(startDate, endDate, entity.getStartDate(), entity.getEndDate())) {
                throw new IllegalArgumentException("工作经历时间存在重叠");
            }
        }
    }

    /**
     * 检查时间是否重叠
     */
    private boolean isTimeOverlap(String start1, String end1, String start2, String end2) {
        try {
            LocalDate startDate1 = LocalDate.parse(start1 + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate endDate1 = StringUtils.hasText(end1) ? 
                LocalDate.parse(end1 + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")) : LocalDate.now();
            
            LocalDate startDate2 = LocalDate.parse(start2 + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate endDate2 = StringUtils.hasText(end2) ? 
                LocalDate.parse(end2 + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")) : LocalDate.now();
            
            return !(endDate1.isBefore(startDate2) || startDate1.isAfter(endDate2));
        } catch (DateTimeParseException e) {
            log.warn("时间格式解析失败: start1={}, end1={}, start2={}, end2={}", start1, end1, start2, end2);
            return false;
        }
    }

    /**
     * 验证日期格式
     */
    private boolean isValidDateFormat(String date) {
        if (!StringUtils.hasText(date)) {
            return false;
        }
        try {
            DATE_FORMATTER.parse(date);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 从请求对象构建实体对象
     */
    private OaArchiveWorkExperienceEntity buildEntityFromRequest(WorkExperienceReq req) {
        OaArchiveWorkExperienceEntity entity = new OaArchiveWorkExperienceEntity();
        
        // 复制基本字段
        entity.setCompanyName(req.getCompanyName());
        entity.setPosition(req.getPosition());
        entity.setStartDate(req.getStartDate());
        entity.setEndDate(req.getEndDate());

        entity.setReferenceName(req.getReferenceName());
        entity.setReferencePhone(req.getReferencePhone());
        entity.setSort(req.getSort());
        
        // 处理布尔类型转换
        if (req.getIsCurrentJob() != null) {
            entity.setIsCurrentJob(req.getIsCurrentJob() ? 1 : 0);
        }

        
        return entity;
    }

    /**
     * 从请求对象更新实体对象
     */
    private void updateEntityFromRequest(OaArchiveWorkExperienceEntity entity, WorkExperienceReq req) {
        if (StringUtils.hasText(req.getCompanyName())) {
            entity.setCompanyName(req.getCompanyName());
        }
        if (StringUtils.hasText(req.getPosition())) {
            entity.setPosition(req.getPosition());
        }
        if (StringUtils.hasText(req.getStartDate())) {
            entity.setStartDate(req.getStartDate());
        }
        if (StringUtils.hasText(req.getEndDate())) {
            entity.setEndDate(req.getEndDate());
        }


        if (StringUtils.hasText(req.getReferenceName())) {
            entity.setReferenceName(req.getReferenceName());
        }

        if (StringUtils.hasText(req.getReferencePhone())) {
            entity.setReferencePhone(req.getReferencePhone());
        }

        if (req.getSort() != null) {
            entity.setSort(req.getSort());
        }
        
        // 处理布尔类型转换
        if (req.getIsCurrentJob() != null) {
            entity.setIsCurrentJob(req.getIsCurrentJob() ? 1 : 0);
        }

    }

    /**
     * 将实体对象转换为响应对象
     */
    private WorkExperienceResp convertToResp(OaArchiveWorkExperienceEntity entity) {
        WorkExperienceResp resp = new WorkExperienceResp();
        
        // 复制基本字段
        resp.setId(entity.getId());
        resp.setCompanyName(entity.getCompanyName());
        resp.setPosition(entity.getPosition());
        resp.setStartDate(entity.getStartDate());
        resp.setEndDate(entity.getEndDate());
        resp.setSalary(entity.getSalary());
        resp.setBonus(entity.getBonus());
        resp.setResignationReason(entity.getResignationReason());
        resp.setReferenceName(entity.getReferenceName());
        resp.setReferencePosition(entity.getReferencePosition());
        resp.setReferencePhone(entity.getReferencePhone());
        resp.setWorkLocation(entity.getWorkLocation());
        resp.setRemarks(entity.getRemarks());
        resp.setSort(entity.getSort());
        
        // 处理整数到字符串的转换
        resp.setCompanySize(convertCompanySizeToString(entity.getCompanySize()));
        resp.setCompanyNature(convertCompanyNatureToString(entity.getCompanyNature()));
        
        // 处理布尔类型转换
        resp.setIsCurrentJob(entity.getIsCurrentJob() != null && entity.getIsCurrentJob() == 1);
        
        // 设置其他字段
        resp.setIndustry(entity.getIndustry());
        resp.setDepartment(entity.getDepartment());
        resp.setPositionLevel(entity.getPositionLevel());
        
        return resp;
    }

    /**
     * 解析公司规模字符串为整数
     */
    private Integer parseCompanySize(String companySize) {
        if (!StringUtils.hasText(companySize)) {
            return null;
        }
        
        if (companySize.contains("20人以下")) return 1;
        if (companySize.contains("20-99人")) return 2;
        if (companySize.contains("100-499人")) return 3;
        if (companySize.contains("500-999人")) return 4;
        if (companySize.contains("1000-9999人")) return 5;
        if (companySize.contains("10000人以上")) return 6;
        
        return null;
    }

    /**
     * 解析公司性质字符串为整数
     */
    private Integer parseCompanyNature(String companyNature) {
        if (!StringUtils.hasText(companyNature)) {
            return null;
        }
        
        if (companyNature.contains("国有企业")) return 1;
        if (companyNature.contains("民营企业")) return 2;
        if (companyNature.contains("外资企业")) return 3;
        if (companyNature.contains("合资企业")) return 4;
        if (companyNature.contains("事业单位")) return 5;
        if (companyNature.contains("政府机关")) return 6;
        if (companyNature.contains("其他")) return 7;
        
        return null;
    }

    /**
     * 解析职位类别字符串为整数
     */
    private Integer parsePositionCategory(String positionLevel) {
        if (!StringUtils.hasText(positionLevel)) {
            return null;
        }
        
        if (positionLevel.contains("技术")) return 1;
        if (positionLevel.contains("管理")) return 2;
        if (positionLevel.contains("销售")) return 3;
        if (positionLevel.contains("市场")) return 4;
        if (positionLevel.contains("财务")) return 5;
        if (positionLevel.contains("人事")) return 6;
        if (positionLevel.contains("行政")) return 7;
        
        return 8; // 其他
    }

    /**
     * 将公司规模整数转换为字符串
     */
    private String convertCompanySizeToString(Integer companySize) {
        if (companySize == null) return null;
        
        switch (companySize) {
            case 1: return "20人以下";
            case 2: return "20-99人";
            case 3: return "100-499人";
            case 4: return "500-999人";
            case 5: return "1000-9999人";
            case 6: return "10000人以上";
            default: return null;
        }
    }

    /**
     * 将公司性质整数转换为字符串
     */
    private String convertCompanyNatureToString(Integer companyNature) {
        if (companyNature == null) return null;
        
        switch (companyNature) {
            case 1: return "国有企业";
            case 2: return "民营企业";
            case 3: return "外资企业";
            case 4: return "合资企业";
            case 5: return "事业单位";
            case 6: return "政府机关";
            case 7: return "其他";
            default: return null;
        }
    }
}

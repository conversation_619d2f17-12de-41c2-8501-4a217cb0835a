package com.miaowen.oa.personnel.domain.repository;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.application.bo.ArchiveBO;

import java.util.List;
import java.util.Map;

/**
 * 档案仓储接口
 * 
 * 设计理念：
 * 1. 仓储模式定位：
 *    - 领域层接口：定义领域对象的持久化契约
 *    - 封装数据访问：隐藏底层数据存储的实现细节
 *    - 面向领域：使用领域语言定义方法，而非技术术语
 *    - 测试友好：便于进行单元测试和集成测试
 *    - 参考：《领域驱动设计》- 仓储模式
 * 
 * 2. 接口设计原则：
 *    - 领域导向：方法命名体现业务含义，如findByUserId而非selectByUserId
 *    - 类型安全：使用强类型参数，避免Map传参的类型不安全
 *    - 职责单一：每个方法只负责一个明确的数据访问职责
 *    - 抽象适当：抽象程度适中，既隐藏实现又保持灵活性
 * 
 * 3. 与基础设施层的关系：
 *    - 接口定义：在领域层定义接口契约
 *    - 实现分离：在基础设施层提供具体实现
 *    - 依赖倒置：领域层不依赖基础设施层的具体实现
 *    - 技术无关：接口定义与具体的持久化技术无关
 * 
 * 4. 数据一致性保证：
 *    - 事务边界：明确事务边界和一致性要求
 *    - 并发控制：支持乐观锁和悲观锁机制
 *    - 数据完整性：确保数据的完整性约束
 *    - 异常处理：定义清晰的异常处理策略
 * 
 * 5. 性能考虑：
 *    - 查询优化：支持高效的查询操作
 *    - 批量操作：提供批量数据处理能力
 *    - 缓存集成：支持缓存策略的集成
 *    - 分页支持：提供大数据量的分页查询
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
public interface ArchiveRepository {

    /**
     * 保存档案
     * 
     * 业务语义：
     * 1. 新增档案：如果档案ID为空，则创建新档案
     * 2. 更新档案：如果档案ID不为空，则更新现有档案
     * 3. 数据完整性：确保保存的数据符合业务规则
     * 4. 事务性：保证保存操作的原子性
     * 
     * 实现要求：
     * 1. 自动生成ID：新增时自动生成主键ID
     * 2. 审计信息：自动设置创建时间、更新时间等审计字段
     * 3. 乐观锁：支持版本号的乐观锁机制
     * 4. 异常处理：处理唯一约束冲突等异常情况
     * 
     * @param archive 档案业务对象
     * @return 保存后的档案（包含生成的ID等信息）
     * @throws IllegalArgumentException 当档案数据不合法时
     * @throws RuntimeException 当保存操作失败时
     */
    ArchiveBO save(ArchiveBO archive);

    /**
     * 根据ID查找档案
     * 
     * 业务语义：
     * 1. 精确查找：根据档案的唯一标识查找
     * 2. 软删除过滤：不返回已删除的档案
     * 3. 权限过滤：根据当前用户权限过滤结果
     * 4. 缓存支持：支持查询结果的缓存
     * 
     * @param id 档案ID
     * @return 档案业务对象，如果不存在返回null
     * @throws IllegalArgumentException 当ID为空或无效时
     */
    ArchiveBO findById(Long id);

    /**
     * 根据用户ID查找档案
     * 
     * 业务语义：
     * 1. 唯一性：一个用户只能有一个档案
     * 2. 业务关联：体现用户与档案的一对一关系
     * 3. 常用查询：这是最常用的档案查询方式之一
     * 4. 权限验证：用于权限验证和业务流程
     * 
     * @param userId 用户ID
     * @return 用户的档案，如果不存在返回null
     * @throws IllegalArgumentException 当用户ID为空或无效时
     */
    ArchiveBO findByUserId(Long userId);

    /**
     * 根据档案编号查找档案
     * 
     * 业务语义：
     * 1. 业务标识：档案编号是档案的业务唯一标识
     * 2. 外部集成：常用于与外部系统的数据集成
     * 3. 用户友好：档案编号对用户更友好，便于记忆和交流
     * 4. 唯一性：档案编号在系统中具有唯一性
     * 
     * @param archiveNumber 档案编号
     * @return 档案业务对象，如果不存在返回null
     * @throws IllegalArgumentException 当档案编号为空或格式不正确时
     */
    ArchiveBO findByArchiveNumber(String archiveNumber);

    /**
     * 根据状态查找档案列表
     * 
     * 业务语义：
     * 1. 状态管理：支持档案状态机的查询需求
     * 2. 批量处理：用于批量处理特定状态的档案
     * 3. 统计分析：支持按状态进行统计分析
     * 4. 工作流：支持工作流中的任务查询
     * 
     * @param archiveState 档案状态
     * @return 指定状态的档案列表
     * @throws IllegalArgumentException 当状态值无效时
     */
    List<ArchiveBO> findByState(Integer archiveState);

    /**
     * 根据类型查找档案列表
     * 
     * 业务语义：
     * 1. 分类管理：支持按档案类型进行分类管理
     * 2. 差异化处理：不同类型的档案可能有不同的处理流程
     * 3. 统计报表：支持按类型进行统计分析
     * 4. 权限控制：不同类型的档案可能有不同的权限要求
     * 
     * @param archiveType 档案类型
     * @return 指定类型的档案列表
     * @throws IllegalArgumentException 当类型值无效时
     */
    List<ArchiveBO> findByType(Integer archiveType);

    /**
     * 分页查询档案
     * 
     * 业务语义：
     * 1. 大数据量处理：支持大数据量的分页查询
     * 2. 多条件查询：支持复杂的查询条件组合
     * 3. 性能优化：通过分页减少内存使用和网络传输
     * 4. 用户体验：提供良好的列表浏览体验
     * 
     * 查询条件说明：
     * - archiveState: 档案状态
     * - archiveType: 档案类型
     * - userId: 用户ID
     * - archiveNumber: 档案编号（支持模糊查询）
     * - entryDateStart/entryDateEnd: 入职日期范围
     * - createTimeStart/createTimeEnd: 创建时间范围
     * - keyword: 关键字搜索
     * 
     * @param queryConditions 查询条件Map
     * @param pageNo 页码（从1开始）
     * @param pageSize 页大小
     * @return 分页查询结果
     * @throws IllegalArgumentException 当分页参数无效时
     */
    PageResult<ArchiveBO> findPage(Map<String, Object> queryConditions, Integer pageNo, Integer pageSize);

    /**
     * 批量查询档案
     * 
     * 业务语义：
     * 1. 批量操作：支持批量获取多个档案
     * 2. 性能优化：使用IN查询优化批量查询性能
     * 3. 数据一致性：确保批量查询的数据一致性
     * 4. 权限过滤：根据权限过滤查询结果
     * 
     * @param ids 档案ID列表
     * @return 档案列表（按ID顺序返回）
     * @throws IllegalArgumentException 当ID列表为空或包含无效ID时
     */
    List<ArchiveBO> findByIds(List<Long> ids);

    /**
     * 统计档案总数
     * 
     * 业务语义：
     * 1. 数据统计：提供档案总数的统计信息
     * 2. 仪表板：用于仪表板和报表的数据展示
     * 3. 容量规划：用于系统容量规划和资源评估
     * 4. 业务监控：用于业务指标的监控和告警
     * 
     * @return 档案总数（不包括已删除的档案）
     */
    Long countTotal();

    /**
     * 按状态统计档案数量
     * 
     * 业务语义：
     * 1. 状态分布：了解档案在各个状态的分布情况
     * 2. 工作负载：评估各个状态的工作负载
     * 3. 流程监控：监控档案流转的效率和瓶颈
     * 4. 决策支持：为业务决策提供数据支持
     * 
     * @return 状态统计Map，key为状态值，value为数量
     */
    Map<Integer, Long> countByState();

    /**
     * 按类型统计档案数量
     * 
     * 业务语义：
     * 1. 类型分布：了解不同类型档案的数量分布
     * 2. 资源配置：根据类型分布配置相应的资源
     * 3. 业务分析：分析不同类型档案的业务特点
     * 4. 策略制定：为不同类型制定相应的管理策略
     * 
     * @return 类型统计Map，key为类型值，value为数量
     */
    Map<Integer, Long> countByType();

    /**
     * 软删除档案
     * 
     * 业务语义：
     * 1. 数据安全：使用软删除保证数据的可恢复性
     * 2. 审计要求：满足审计对数据保留的要求
     * 3. 关联影响：避免硬删除对关联数据的影响
     * 4. 业务连续性：保证业务流程的连续性
     * 
     * 实现要求：
     * 1. 标记删除：设置deleted字段为true
     * 2. 审计信息：记录删除时间和删除人
     * 3. 级联处理：处理关联数据的删除标记
     * 4. 索引更新：更新相关的唯一索引
     * 
     * @param id 档案ID
     * @return 删除是否成功
     * @throws IllegalArgumentException 当ID为空或无效时
     * @throws IllegalStateException 当档案状态不允许删除时
     */
    Boolean softDelete(Long id);

    /**
     * 批量软删除档案
     * 
     * 业务语义：
     * 1. 批量操作：支持批量删除多个档案
     * 2. 事务性：确保批量删除的事务一致性
     * 3. 性能优化：使用批量操作优化删除性能
     * 4. 原子性：要么全部成功，要么全部失败
     * 
     * @param ids 档案ID列表
     * @return 成功删除的档案数量
     * @throws IllegalArgumentException 当ID列表为空或包含无效ID时
     * @throws IllegalStateException 当某些档案状态不允许删除时
     */
    Integer batchSoftDelete(List<Long> ids);

    /**
     * 检查档案编号是否存在
     * 
     * 业务语义：
     * 1. 唯一性验证：验证档案编号的唯一性
     * 2. 重复检查：防止创建重复的档案编号
     * 3. 数据完整性：确保档案编号的数据完整性
     * 4. 业务规则：支持档案编号的业务规则验证
     * 
     * @param archiveNumber 档案编号
     * @return 是否存在
     * @throws IllegalArgumentException 当档案编号为空或格式不正确时
     */
    Boolean existsByArchiveNumber(String archiveNumber);

    /**
     * 检查用户是否已有档案
     * 
     * 业务语义：
     * 1. 唯一性约束：确保一个用户只能有一个档案
     * 2. 业务规则：支持档案创建的业务规则验证
     * 3. 数据一致性：维护用户与档案的一对一关系
     * 4. 重复防护：防止为同一用户创建多个档案
     * 
     * @param userId 用户ID
     * @return 是否已有档案
     * @throws IllegalArgumentException 当用户ID为空或无效时
     */
    Boolean existsByUserId(Long userId);
}

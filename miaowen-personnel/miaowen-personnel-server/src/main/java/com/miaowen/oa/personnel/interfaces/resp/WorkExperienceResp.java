package com.miaowen.oa.personnel.interfaces.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 工作经历DTO
 * 用于存储详细的工作经历信息
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "工作经历信息")
public class WorkExperienceResp {

    @Schema(description = "工作经历ID", example = "1")
    private Long id;

    @Schema(description = "公司名称", example = "阿里巴巴集团", required = true)
    @NotBlank(message = "公司名称不能为空")
    @Size(max = 100, message = "公司名称长度不能超过100个字符")
    private String companyName;

    @Schema(description = "公司规模", example = "10000人以上")
    @Size(max = 50, message = "公司规模长度不能超过50个字符")
    private String companySize;

    @Schema(description = "公司性质", example = "民营企业")
    @Size(max = 50, message = "公司性质长度不能超过50个字符")
    private String companyNature;

    @Schema(description = "所属行业", example = "互联网/电子商务")
    @Size(max = 50, message = "所属行业长度不能超过50个字符")
    private String industry;

    @Schema(description = "工作部门", example = "技术部")
    @Size(max = 100, message = "工作部门长度不能超过100个字符")
    private String department;

    @Schema(description = "职位名称", example = "高级Java开发工程师", required = true)
    @NotBlank(message = "职位名称不能为空")
    @Size(max = 100, message = "职位名称长度不能超过100个字符")
    private String position;

    @Schema(description = "职位级别", example = "P6")
    @Size(max = 20, message = "职位级别长度不能超过20个字符")
    private String positionLevel;

    @Schema(description = "入职时间", example = "2018-03", required = true)
    @NotBlank(message = "入职时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "入职时间格式应为YYYY-MM")
    private String startDate;

    @Schema(description = "离职时间", example = "2023-12")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "离职时间格式应为YYYY-MM")
    private String endDate;

    @Schema(description = "是否在职", example = "false")
    private Boolean isCurrentJob;

    @Schema(description = "薪资（月薪）", example = "15000")
    @Min(value = 1000, message = "薪资不能小于1000")
    @Max(value = 1000000, message = "薪资不能大于1000000")
    private Integer salary;

    @Schema(description = "年终奖", example = "30000")
    @Min(value = 0, message = "年终奖不能小于0")
    private Integer bonus;

    @Schema(description = "直属上级", example = "张经理")
    @Size(max = 50, message = "直属上级长度不能超过50个字符")
    private String directSupervisor;

    @Schema(description = "下属人数", example = "5")
    @Min(value = 0, message = "下属人数不能小于0")
    private Integer subordinateCount;

    @Schema(description = "工作职责", example = "负责核心业务系统的开发和维护")
    @Size(max = 1000, message = "工作职责长度不能超过1000个字符")
    private String responsibilities;

    @Schema(description = "主要成就", example = "主导完成了用户中心重构项目")
    @Size(max = 1000, message = "主要成就长度不能超过1000个字符")
    private String achievements;

    @Schema(description = "使用技术", example = "Java,Spring Boot,MySQL,Redis")
    @Size(max = 500, message = "使用技术长度不能超过500个字符")
    private String technologies;

    @Schema(description = "离职原因", example = "个人发展")
    @Size(max = 200, message = "离职原因长度不能超过200个字符")
    private String resignationReason;

    @Schema(description = "证明人姓名", example = "李主管")
    @Size(max = 50, message = "证明人姓名长度不能超过50个字符")
    private String referenceName;

    @Schema(description = "证明人职位", example = "技术主管")
    @Size(max = 50, message = "证明人职位长度不能超过50个字符")
    private String referencePosition;

    @Schema(description = "证明人联系方式", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "证明人联系方式格式不正确")
    private String referencePhone;

    @Schema(description = "工作地点", example = "北京市朝阳区")
    @Size(max = 100, message = "工作地点长度不能超过100个字符")
    private String workLocation;

    @Schema(description = "备注", example = "项目经验丰富")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remarks;

    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sort;

    // ========== 便利方法 ==========

    /**
     * 计算工作时长（月）
     */
    public Integer getWorkDurationMonths() {
        if (startDate == null) {
            return null;
        }
        
        String endDateToUse = endDate;
        if (Boolean.TRUE.equals(isCurrentJob) || endDateToUse == null) {
            // 如果是在职或没有结束时间，使用当前时间
            java.time.LocalDate now = java.time.LocalDate.now();
            endDateToUse = String.format("%04d-%02d", now.getYear(), now.getMonthValue());
        }
        
        try {
            String[] startParts = startDate.split("-");
            String[] endParts = endDateToUse.split("-");
            
            int startYear = Integer.parseInt(startParts[0]);
            int startMonth = Integer.parseInt(startParts[1]);
            int endYear = Integer.parseInt(endParts[0]);
            int endMonth = Integer.parseInt(endParts[1]);
            
            return (endYear - startYear) * 12 + (endMonth - startMonth);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取工作时长描述
     */
    public String getWorkDurationDescription() {
        Integer months = getWorkDurationMonths();
        if (months == null || months <= 0) {
            return null;
        }
        
        int years = months / 12;
        int remainingMonths = months % 12;
        
        if (years > 0 && remainingMonths > 0) {
            return years + "年" + remainingMonths + "个月";
        } else if (years > 0) {
            return years + "年";
        } else {
            return remainingMonths + "个月";
        }
    }

    /**
     * 计算年薪
     */
    public Integer getAnnualSalary() {
        if (salary == null) {
            return null;
        }
        
        int annualSalary = salary * 12;
        if (bonus != null) {
            annualSalary += bonus;
        }
        return annualSalary;
    }

    /**
     * 是否为管理岗位
     */
    public boolean isManagementPosition() {
        if (subordinateCount != null && subordinateCount > 0) {
            return true;
        }
        
        if (position != null) {
            String pos = position.toLowerCase();
            return pos.contains("经理") || pos.contains("主管") || pos.contains("总监") || 
                   pos.contains("manager") || pos.contains("director") || pos.contains("lead");
        }
        
        return false;
    }

    /**
     * 是否为长期工作（超过2年）
     */
    public boolean isLongTermJob() {
        Integer months = getWorkDurationMonths();
        return months != null && months >= 24;
    }

    /**
     * 是否为短期工作（少于6个月）
     */
    public boolean isShortTermJob() {
        Integer months = getWorkDurationMonths();
        return months != null && months < 6;
    }

    /**
     * 获取薪资等级描述
     */
    public String getSalaryLevelDescription() {
        if (salary == null) {
            return null;
        }
        
        if (salary < 5000) {
            return "初级";
        } else if (salary < 10000) {
            return "中级";
        } else if (salary < 20000) {
            return "高级";
        } else if (salary < 50000) {
            return "专家级";
        } else {
            return "顶级";
        }
    }

    /**
     * 验证工作经历信息完整性
     */
    public boolean isComplete() {
        return companyName != null && !companyName.trim().isEmpty() &&
               position != null && !position.trim().isEmpty() &&
               startDate != null && !startDate.trim().isEmpty() &&
               (Boolean.TRUE.equals(isCurrentJob) || 
                (endDate != null && !endDate.trim().isEmpty()));
    }

    /**
     * 验证时间逻辑是否正确
     */
    public boolean isDateLogicValid() {
        if (startDate == null) {
            return false;
        }
        
        // 如果是在职，不需要验证结束时间
        if (Boolean.TRUE.equals(isCurrentJob)) {
            return true;
        }
        
        if (endDate == null) {
            return false;
        }
        
        try {
            String[] startParts = startDate.split("-");
            String[] endParts = endDate.split("-");
            
            int startYear = Integer.parseInt(startParts[0]);
            int startMonth = Integer.parseInt(startParts[1]);
            int endYear = Integer.parseInt(endParts[0]);
            int endMonth = Integer.parseInt(endParts[1]);
            
            // 结束时间应该晚于或等于开始时间
            return endYear > startYear || (endYear == startYear && endMonth >= startMonth);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证薪资合理性
     */
    public boolean isSalaryReasonable() {
        if (salary == null) {
            return true; // 薪资可以为空
        }
        
        // 基本合理性检查
        if (salary < 1000 || salary > 1000000) {
            return false;
        }
        
        // 年终奖不应该超过年薪的5倍
        if (bonus != null && bonus > salary * 60) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取工作经历摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(companyName).append(" - ").append(position);
        
        String duration = getWorkDurationDescription();
        if (duration != null) {
            summary.append(" (").append(duration).append(")");
        }
        
        return summary.toString();
    }
}

package com.miaowen.oa.personnel.interfaces.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 档案合同信息DTO
 * 
 * 设计理念：
 * 1. 采用DTO模式而非直接暴露Entity的原因：
 *    - 数据传输对象(DTO)模式提供了更好的API稳定性，Entity变更不会直接影响API接口
 *    - 可以灵活控制哪些字段对外暴露，保护敏感信息
 *    - 支持字段级别的验证和转换，提供更好的数据完整性保障
 *    - 符合领域驱动设计(DDD)的分层架构原则，参考：https://martinfowler.com/eaaCatalog/dataTransferObject.html
 * 
 * 2. 验证注解选择说明：
 *    - 使用Jakarta Validation 3.0规范而非Hibernate Validator的原因：
 *      Jakarta是Java EE的标准规范，具有更好的跨框架兼容性
 *      参考：https://jakarta.ee/specifications/bean-validation/3.0/
 *    - @NotNull vs @NotEmpty vs @NotBlank的使用场景：
 *      @NotNull: 对象不能为null，但可以为空字符串
 *      @NotEmpty: 集合/数组/字符串不能为null且长度>0
 *      @NotBlank: 字符串不能为null且去除空白字符后长度>0
 * 
 * 3. 金额字段使用BigDecimal的原因：
 *    - 避免浮点数精度丢失问题，确保财务计算的准确性
 *    - 符合金融行业标准，参考：JSR 354 Money and Currency API
 *    - 支持任意精度的十进制运算
 * 
 * 4. 日期字段使用String而非LocalDate的考虑：
 *    - API接口层面使用String可以避免时区转换问题
 *    - 前端JavaScript对日期处理更友好
 *    - 支持多种日期格式的灵活解析
 *    - 在Service层进行类型转换，保持接口层的简洁性
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "档案合同信息")
public class ArchiveContractResp {

    @Schema(description = "合同ID", example = "1")
    private Long id;

    @Schema(description = "档案ID", example = "1", required = true)
    @NotNull(message = "档案ID不能为空")
    private Long archiveId;

    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    /**
     * 合同编号字段设计说明：
     * 1. 使用自定义格式而非UUID的原因：
     *    - 业务可读性：CONTRACT-2025-000001比UUID更容易理解和记忆
     *    - 排序友好：按时间顺序生成，支持自然排序
     *    - 长度控制：固定长度便于数据库索引优化
     * 2. 正则表达式说明：
     *    - ^CONTRACT-\d{4}-\d{6}$ 确保格式为 CONTRACT-年份-6位序号
     *    - 年份部分便于按年度统计和归档
     *    - 序号部分支持每年最多999999个合同
     */
    @Schema(description = "合同编号", example = "CONTRACT-2025-000001", required = true)
    @NotBlank(message = "合同编号不能为空")
    @Pattern(regexp = "^CONTRACT-\\d{4}-\\d{6}$", message = "合同编号格式不正确，应为CONTRACT-YYYY-NNNNNN")
    @Size(max = 50, message = "合同编号长度不能超过50个字符")
    private String contractNumber;

    @Schema(description = "合同名称", example = "劳动合同书")
    @Size(max = 100, message = "合同名称长度不能超过100个字符")
    private String contractName;

    /**
     * 合同类型枚举值设计：
     * 1. 使用数字枚举而非字符串枚举的原因：
     *    - 存储效率：整型占用空间更小，索引性能更好
     *    - 国际化友好：数字枚举便于多语言环境下的显示
     *    - 扩展性：新增类型不会影响现有数据结构
     * 2. 枚举值范围说明：
     *    - 1-8覆盖了常见的合同类型
     *    - 预留了扩展空间，避免频繁的数据库结构变更
     * 3. 可能的tricky问题：
     *    - 枚举值删除时需要考虑历史数据的兼容性
     *    - 前端显示时需要维护枚举值与显示名称的映射关系
     */
    @Schema(description = "合同类型", example = "1", 
            allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"},
            required = true)
    @NotNull(message = "合同类型不能为空")
    @Min(value = 1, message = "合同类型代码不能小于1")
    @Max(value = 8, message = "合同类型代码不能大于8")
    private Integer contractType;

    @Schema(description = "合同性质", example = "1", allowableValues = {"1", "2", "3"})
    @Min(value = 1, message = "合同性质代码不能小于1")
    @Max(value = 3, message = "合同性质代码不能大于3")
    private Integer contractNature;

    @Schema(description = "合同状态", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7"})
    @Min(value = 1, message = "合同状态代码不能小于1")
    @Max(value = 7, message = "合同状态代码不能大于7")
    private Integer contractStatus;

    /**
     * 日期字段验证说明：
     * 1. 使用ISO 8601日期格式(YYYY-MM-DD)的原因：
     *    - 国际标准，避免地区差异导致的解析错误
     *    - 字典序与时间序一致，便于排序和比较
     *    - 参考：https://tools.ietf.org/html/rfc3339
     * 2. 正则表达式说明：
     *    - ^\d{4}-\d{2}-\d{2}$ 确保基本格式正确
     *    - 不验证日期有效性(如2月30日)，由业务层处理
     * 3. 注意事项：
     *    - 前端传入时需要确保时区一致性
     *    - 数据库存储时建议使用DATE类型而非VARCHAR
     */
    @Schema(description = "合同开始日期", example = "2025-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "合同开始日期格式应为YYYY-MM-DD")
    private String startDate;

    @Schema(description = "合同结束日期", example = "2027-12-31")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "合同结束日期格式应为YYYY-MM-DD")
    private String endDate;

    @Schema(description = "合同期限（月）", example = "36")
    @Min(value = 1, message = "合同期限不能小于1个月")
    @Max(value = 1200, message = "合同期限不能超过1200个月") // 100年上限，防止异常数据
    private Integer contractDuration;

    @Schema(description = "试用期开始日期", example = "2025-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "试用期开始日期格式应为YYYY-MM-DD")
    private String probationStartDate;

    @Schema(description = "试用期结束日期", example = "2025-06-30")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "试用期结束日期格式应为YYYY-MM-DD")
    private String probationEndDate;

    /**
     * 试用期期限验证说明：
     * 1. 根据《劳动合同法》第19条规定：
     *    - 合同期限3个月以上不满1年的，试用期不得超过1个月
     *    - 合同期限1年以上不满3年的，试用期不得超过2个月
     *    - 3年以上固定期限和无固定期限的劳动合同，试用期不得超过6个月
     * 2. 设置12个月上限是为了防止异常数据，实际业务中应根据合同期限动态验证
     * 3. 参考资料：《中华人民共和国劳动合同法》
     */
    @Schema(description = "试用期期限（月）", example = "6")
    @Min(value = 0, message = "试用期期限不能小于0个月")
    @Max(value = 12, message = "试用期期限不能超过12个月")
    private Integer probationDuration;

    @Schema(description = "工作地点", example = "武汉市洪山区")
    @Size(max = 100, message = "工作地点长度不能超过100个字符")
    private String workLocation;

    @Schema(description = "详细工作地址", example = "武汉市洪山区光谷大道xxx号")
    @Size(max = 200, message = "详细工作地址长度不能超过200个字符")
    private String workAddress;

    @Schema(description = "工作岗位", example = "Java开发工程师")
    @Size(max = 100, message = "工作岗位长度不能超过100个字符")
    private String position;

    @Schema(description = "岗位级别", example = "P6")
    @Size(max = 20, message = "岗位级别长度不能超过20个字符")
    private String positionLevel;

    @Schema(description = "所属部门", example = "技术研发部")
    @Size(max = 100, message = "所属部门长度不能超过100个字符")
    private String department;

    /**
     * 薪资字段设计说明：
     * 1. 使用Integer而非BigDecimal的考虑：
     *    - 薪资通常以元为单位，不涉及小数计算
     *    - Integer类型在JSON序列化时更简洁
     *    - 如需要精确计算，在Service层转换为BigDecimal
     * 2. 薪资范围设置：
     *    - 最小值1元，避免0薪资的异常情况
     *    - 最大值10,000,000元(1千万)，覆盖高管薪资范围
     * 3. 注意事项：
     *    - 薪资信息属于敏感数据，需要严格的权限控制
     *    - 不同币种的薪资需要额外的货币字段支持
     */
    @Schema(description = "基本薪资（元/月）", example = "8000")
    @Min(value = 1, message = "基本薪资不能小于1元")
    @Max(value = 10000000, message = "基本薪资不能超过10,000,000元")
    private Integer basicSalary;

    @Schema(description = "岗位薪资（元/月）", example = "2000")
    @Min(value = 0, message = "岗位薪资不能小于0元")
    @Max(value = 10000000, message = "岗位薪资不能超过10,000,000元")
    private Integer positionSalary;

    @Schema(description = "绩效薪资（元/月）", example = "3000")
    @Min(value = 0, message = "绩效薪资不能小于0元")
    @Max(value = 10000000, message = "绩效薪资不能超过10,000,000元")
    private Integer performanceSalary;

    @Schema(description = "津贴补贴（元/月）", example = "500")
    @Min(value = 0, message = "津贴补贴不能小于0元")
    @Max(value = 10000000, message = "津贴补贴不能超过10,000,000元")
    private Integer allowance;

    @Schema(description = "月薪总额（元）", example = "13500")
    @Min(value = 1, message = "月薪总额不能小于1元")
    @Max(value = 50000000, message = "月薪总额不能超过50,000,000元")
    private Integer totalMonthlySalary;

    /**
     * 社保公积金比例字段说明：
     * 1. 使用BigDecimal存储百分比的原因：
     *    - 社保比例通常有小数位，如10.5%
     *    - BigDecimal确保精度，避免浮点数误差
     *    - 便于精确的费用计算
     * 2. 比例范围设置：
     *    - 0-100的范围覆盖了所有可能的百分比值
     *    - scale=2确保最多两位小数精度
     * 3. 参考标准：
     *    - 社保比例参考各地社保局规定
     *    - 公积金比例参考住房公积金管理条例
     */
    @Schema(description = "社保缴纳比例（企业部分，百分比）", example = "20.5")
    @DecimalMin(value = "0.00", message = "社保缴纳比例不能小于0")
    @DecimalMax(value = "100.00", message = "社保缴纳比例不能超过100")
    @Digits(integer = 3, fraction = 2, message = "社保缴纳比例最多3位整数2位小数")
    private BigDecimal socialInsuranceRateCompany;

    @Schema(description = "公积金缴纳比例（企业部分，百分比）", example = "12.0")
    @DecimalMin(value = "0.00", message = "公积金缴纳比例不能小于0")
    @DecimalMax(value = "100.00", message = "公积金缴纳比例不能超过100")
    @Digits(integer = 3, fraction = 2, message = "公积金缴纳比例最多3位整数2位小数")
    private BigDecimal housingFundRateCompany;

    @Schema(description = "年假天数", example = "5")
    @Min(value = 0, message = "年假天数不能小于0天")
    @Max(value = 365, message = "年假天数不能超过365天")
    private Integer annualLeaveDays;

    /**
     * 工作时间字段说明：
     * 1. 每日工作小时数使用BigDecimal的原因：
     *    - 支持弹性工作制，如7.5小时/天
     *    - 精确计算加班费和工时统计
     * 2. 工作时间范围设置：
     *    - 1-24小时覆盖了所有可能的工作时长
     *    - 考虑特殊岗位(如医生)的长时间工作需求
     * 3. 时间格式说明：
     *    - HH:mm格式便于前端显示和用户理解
     *    - 24小时制避免AM/PM的歧义
     */
    @Schema(description = "每日工作小时数", example = "8.0")
    @DecimalMin(value = "1.0", message = "每日工作小时数不能小于1小时")
    @DecimalMax(value = "24.0", message = "每日工作小时数不能超过24小时")
    @Digits(integer = 2, fraction = 1, message = "每日工作小时数最多2位整数1位小数")
    private BigDecimal workHoursPerDay;

    @Schema(description = "上班时间", example = "09:00")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "上班时间格式应为HH:mm")
    private String workStartTime;

    @Schema(description = "下班时间", example = "18:00")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "下班时间格式应为HH:mm")
    private String workEndTime;

    /**
     * 竞业限制字段说明：
     * 1. 期限范围设置依据：
     *    - 《劳动合同法》第24条规定竞业限制期限不得超过2年
     *    - 设置24个月上限符合法律规定
     * 2. 保密期限说明：
     *    - 通常与竞业限制期限相同或更长
     *    - 设置60个月上限考虑特殊行业需求
     * 3. 参考资料：
     *    - 《中华人民共和国劳动合同法》第23、24条
     *    - 各地高院关于竞业限制的司法解释
     */
    @Schema(description = "保密期限（月）", example = "24")
    @Min(value = 0, message = "保密期限不能小于0个月")
    @Max(value = 60, message = "保密期限不能超过60个月")
    private Integer confidentialityPeriod;

    @Schema(description = "竞业限制期限（月）", example = "12")
    @Min(value = 0, message = "竞业限制期限不能小于0个月")
    @Max(value = 24, message = "竞业限制期限不能超过24个月")
    private Integer nonCompetePeriod;

    @Schema(description = "竞业限制范围", example = "同行业竞争对手")
    @Size(max = 500, message = "竞业限制范围长度不能超过500个字符")
    private String nonCompeteScope;

    /**
     * 文本字段长度设置说明：
     * 1. 违约金条款设置1000字符上限：
     *    - 覆盖常见的违约金条款描述
     *    - 避免过长文本影响数据库性能
     * 2. 知识产权条款设置2000字符上限：
     *    - 知识产权条款通常较为复杂，需要更多描述空间
     *    - 平衡存储效率和内容完整性
     * 3. 数据库设计建议：
     *    - 使用TEXT类型存储长文本
     *    - 考虑使用全文索引支持内容搜索
     */
    @Schema(description = "违约金条款", example = "违反竞业限制协议的，应支付违约金...")
    @Size(max = 1000, message = "违约金条款长度不能超过1000个字符")
    private String penaltyClause;

    @Schema(description = "知识产权条款", example = "员工在职期间产生的知识产权归公司所有...")
    @Size(max = 2000, message = "知识产权条款长度不能超过2000个字符")
    private String intellectualPropertyClause;

    @Schema(description = "员工签署日期", example = "2025-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "员工签署日期格式应为YYYY-MM-DD")
    private String employeeSignDate;

    @Schema(description = "公司签署人", example = "张总")
    @Size(max = 50, message = "公司签署人长度不能超过50个字符")
    private String companySignatory;

    @Schema(description = "公司签署日期", example = "2025-01-02")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "公司签署日期格式应为YYYY-MM-DD")
    private String companySignDate;

    /**
     * 文件URL字段说明：
     * 1. 长度设置为500字符的原因：
     *    - 覆盖常见的文件存储路径长度
     *    - 支持云存储服务的长URL格式
     * 2. URL格式验证考虑：
     *    - 不强制验证URL格式，因为可能是相对路径
     *    - 在Service层进行URL有效性检查
     * 3. 安全考虑：
     *    - 文件访问需要权限控制
     *    - 考虑使用预签名URL提高安全性
     */
    @Schema(description = "合同文件URL", example = "https://example.com/contracts/CONTRACT-2025-000001.pdf")
    @Size(max = 500, message = "合同文件URL长度不能超过500个字符")
    private String contractFileUrl;

    @Schema(description = "审批状态", example = "1", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "审批状态代码不能小于1")
    @Max(value = 4, message = "审批状态代码不能大于4")
    private Integer approvalStatus;

    @Schema(description = "审批流程ID", example = "APPROVAL-2025-000001")
    @Size(max = 100, message = "审批流程ID长度不能超过100个字符")
    private String approvalProcessId;

    @Schema(description = "审批意见", example = "合同条款符合公司规定，同意签署")
    @Size(max = 1000, message = "审批意见长度不能超过1000个字符")
    private String approvalComment;

    @Schema(description = "是否为续签合同", example = "false")
    private Boolean isRenewal;

    @Schema(description = "原合同ID（续签时关联）", example = "100")
    private Long originalContractId;

    @Schema(description = "变更原因", example = "薪资调整")
    @Size(max = 200, message = "变更原因长度不能超过200个字符")
    private String changeReason;

    @Schema(description = "备注", example = "特殊条款说明")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remarks;

    // ========== 便利方法 ==========

    /**
     * 获取合同类型名称
     * 
     * 实现说明：
     * 1. 使用数组映射而非Map的原因：
     *    - 数组访问效率更高，O(1)时间复杂度
     *    - 内存占用更小，适合频繁调用的场景
     *    - 枚举值连续且数量较少，数组是最优选择
     * 2. 边界检查的重要性：
     *    - 防止数组越界异常
     *    - 处理异常数据的容错机制
     * 3. 可能的扩展方案：
     *    - 如果枚举值变得稀疏，可考虑使用EnumMap
     *    - 如果需要国际化，可结合ResourceBundle使用
     */
    public String getContractTypeName() {
        if (contractType == null) return null;
        String[] types = {"", "劳动合同", "劳务合同", "实习协议", "派遣合同", "顾问协议", "保密协议", "竞业协议", "其他"};
        return contractType > 0 && contractType < types.length ? types[contractType] : "未知类型";
    }

    /**
     * 计算合同剩余天数
     * 
     * 实现说明：
     * 1. 使用java.time包而非Date的原因：
     *    - java.time是Java 8+的现代日期时间API，线程安全且不可变
     *    - 提供了更丰富的日期计算方法
     *    - 避免了Date类的时区和精度问题
     *    - 参考：JSR 310 Date and Time API
     * 2. 异常处理策略：
     *    - 日期解析失败时返回null而非抛出异常
     *    - 调用方可以根据null值判断数据异常
     * 3. 性能考虑：
     *    - LocalDate.parse()比SimpleDateFormat性能更好
     *    - ChronoUnit.DAYS.between()是最高效的日期差值计算方法
     */
    public Integer getRemainingDays() {
        if (endDate == null) return null;
        
        try {
            java.time.LocalDate end = java.time.LocalDate.parse(endDate);
            java.time.LocalDate now = java.time.LocalDate.now();
            return (int) java.time.temporal.ChronoUnit.DAYS.between(now, end);
        } catch (Exception e) {
            // 日期格式错误时返回null，避免抛出异常影响业务流程
            return null;
        }
    }

    /**
     * 判断合同是否即将到期
     * 
     * 业务规则说明：
     * 1. 30天阈值的选择依据：
     *    - 给HR部门足够的时间准备续签工作
     *    - 符合大多数企业的管理流程
     *    - 可以通过配置文件调整，提高灵活性
     * 2. 边界条件处理：
     *    - remaining > 0 确保合同未过期
     *    - remaining <= 30 确保在提醒范围内
     * 3. 扩展建议：
     *    - 可以根据合同重要性设置不同的提醒阈值
     *    - 可以结合员工级别调整提醒策略
     */
    public boolean isExpiringSoon() {
        Integer remaining = getRemainingDays();
        return remaining != null && remaining <= 30 && remaining > 0;
    }

    /**
     * 判断合同是否已过期
     */
    public boolean isExpired() {
        Integer remaining = getRemainingDays();
        return remaining != null && remaining < 0;
    }

    /**
     * 获取合同摘要信息
     * 
     * 实现说明：
     * 1. 使用StringBuilder而非String拼接的原因：
     *    - StringBuilder在多次字符串操作时性能更好
     *    - 避免创建大量临时String对象
     * 2. 信息优先级设计：
     *    - 合同类型：最重要的识别信息
     *    - 合同编号：唯一标识符
     *    - 有效期：关键的时间信息
     * 3. 容错处理：
     *    - 各字段为null时不影响其他信息的显示
     *    - 确保方法始终返回有意义的字符串
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        
        String typeName = getContractTypeName();
        if (typeName != null) {
            summary.append(typeName);
        }
        
        if (contractNumber != null) {
            if (summary.length() > 0) summary.append(" ");
            summary.append("(").append(contractNumber).append(")");
        }
        
        if (startDate != null && endDate != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(startDate).append("至").append(endDate);
        }
        
        return summary.length() > 0 ? summary.toString() : "合同信息";
    }
}

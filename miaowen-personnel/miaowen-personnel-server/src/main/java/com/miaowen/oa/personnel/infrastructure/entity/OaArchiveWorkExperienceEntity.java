package com.miaowen.oa.personnel.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 档案工作经历实体类
 * 存储员工的详细工作经历信息，支持多段工作经历
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_archive_work_experience")
public class OaArchiveWorkExperienceEntity extends BaseDO {

    /**
     * 档案ID（外键关联oa_archive表）
     */
    private Long archiveId;

    /**
     * 用户ID（冗余字段，便于查询）
     */
    private Long userId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司简称
     */
    private String companyShortName;

    /**
     * 公司规模
     * 1-20人以下 2-20-99人 3-100-499人 4-500-999人 5-1000-9999人 6-10000人以上
     */
    private Integer companySize;

    /**
     * 公司性质
     * 1-国有企业 2-民营企业 3-外资企业 4-合资企业 5-事业单位 6-政府机关 7-其他
     */
    private Integer companyNature;

    /**
     * 所属行业
     */
    private String industry;

    /**
     * 行业代码
     */
    private String industryCode;


    /**
     * 工作部门
     */
    private String department;

    /**
     * 职位名称
     */
    private String position;

    /**
     * 职位级别
     */
    private String positionLevel;

    /**
     * 职位类别
     * 1-技术类 2-管理类 3-销售类 4-市场类 5-财务类 6-人事类 7-行政类 8-其他
     */
    private Integer positionCategory;

    /**
     * 入职时间
     * 格式：YYYY-MM
     */
    private String startDate;

    /**
     * 离职时间
     * 格式：YYYY-MM
     */
    private String endDate;

    /**
     * 是否在职
     * 0-否 1-是
     */
    private Integer isCurrentJob;

    /**
     * 薪资（月薪，元）
     */
    private Integer salary;

    /**
     * 年终奖（元）
     */
    private Integer bonus;



    /**
     * 离职原因
     */
    private String resignationReason;

    /**
     * 离职原因分类
     * 1-个人发展 2-薪资待遇 3-工作环境 4-家庭原因 5-健康原因 6-转行 7-公司裁员 8-合同到期 9-其他
     */
    private Integer resignationCategory;

    /**
     * 是否主动离职
     * 0-否 1-是
     */
    private Integer isActiveResignation;

    /**
     * 证明人姓名
     */
    private String referenceName;

    /**
     * 证明人职位
     */
    private String referencePosition;

    /**
     * 证明人电话
     */
    private String referencePhone;


    /**
     * 工作地点
     */
    private String workLocation;

    /**
     * 工作地点详细地址
     */
    private String workAddress;

    /**
     * 工作性质
     * 1-全职 2-兼职 3-实习 4-派遣 5-外包 6-顾问
     */
    private Integer workNature;

    /**
     * 合同类型
     * 1-劳动合同 2-劳务合同 3-实习协议 4-派遣合同 5-其他
     */
    private Integer contractType;


    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     * 1-正常 2-删除
     */
    private Integer state;

    // ========== 便利方法 ==========

    /**
     * 计算工作时长（月）
     */
    public Integer getWorkDurationMonths() {
        if (startDate == null) return null;
        
        String endDateToUse = endDate;
        if (isCurrentJob != null && isCurrentJob == 1 || endDateToUse == null) {
            // 如果是在职或没有结束时间，使用当前时间
            java.time.LocalDate now = java.time.LocalDate.now();
            endDateToUse = String.format("%04d-%02d", now.getYear(), now.getMonthValue());
        }
        
        try {
            String[] startParts = startDate.split("-");
            String[] endParts = endDateToUse.split("-");
            
            int startYear = Integer.parseInt(startParts[0]);
            int startMonth = Integer.parseInt(startParts[1]);
            int endYear = Integer.parseInt(endParts[0]);
            int endMonth = Integer.parseInt(endParts[1]);
            
            return (endYear - startYear) * 12 + (endMonth - startMonth);
        } catch (Exception e) {
            return null;
        }
    }



    /**
     * 是否为长期工作（超过2年）
     */
    public boolean isLongTermJob() {
        Integer months = getWorkDurationMonths();
        return months != null && months >= 24;
    }

    /**
     * 是否为短期工作（少于6个月）
     */
    public boolean isShortTermJob() {
        Integer months = getWorkDurationMonths();
        return months != null && months < 6;
    }

    /**
     * 获取公司规模名称
     */
    public String getCompanySizeName() {
        if (companySize == null) return null;
        String[] sizes = {"", "20人以下", "20-99人", "100-499人", "500-999人", "1000-9999人", "10000人以上"};
        return companySize > 0 && companySize < sizes.length ? sizes[companySize] : null;
    }

    /**
     * 获取公司性质名称
     */
    public String getCompanyNatureName() {
        if (companyNature == null) return null;
        String[] natures = {"", "国有企业", "民营企业", "外资企业", "合资企业", "事业单位", "政府机关", "其他"};
        return companyNature > 0 && companyNature < natures.length ? natures[companyNature] : null;
    }

    /**
     * 获取薪资等级描述
     */
    public String getSalaryLevelDescription() {
        if (salary == null) return null;
        
        if (salary < 5000) return "初级";
        else if (salary < 10000) return "中级";
        else if (salary < 20000) return "高级";
        else if (salary < 50000) return "专家级";
        else return "顶级";
    }

    /**
     * 是否为知名企业
     */
    public boolean isFamousCompany() {
        if (companyName == null) return false;
        
        // 这里可以根据实际情况配置知名企业列表
        String[] famousCompanies = {"阿里巴巴", "腾讯", "百度", "字节跳动", "美团", "京东", "华为", "小米"};
        for (String famous : famousCompanies) {
            if (companyName.contains(famous)) return true;
        }
        
        return false;
    }
}

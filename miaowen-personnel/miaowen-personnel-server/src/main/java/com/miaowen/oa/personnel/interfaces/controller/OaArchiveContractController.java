package com.miaowen.oa.personnel.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveContractService;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractCreateReq;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractReq;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveContractResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同管理控制器
 * RESTful API设计原则：
 * - 资源命名：/contracts 作为主资源路径
 * - 状态码：遵循标准HTTP状态码规范
 * - 版本控制：通过URL路径/v1实现
 * 
 * 安全设计：
 * - 所有写操作需要权限验证（由网关统一处理）
 * - 敏感操作记录审计日志
 * 
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "合同管理")
@Slf4j
@Validated
@RestController
@RequestMapping("/v1/contracts")
public class OaArchiveContractController {

    private final OaArchiveContractService contractService;

    public OaArchiveContractController(OaArchiveContractService contractService) {
        this.contractService = contractService;
    }


    @Operation(summary = "编辑合同")
    @PostMapping("/save")
    public CommonResult<Boolean> saveContract(
            @Validated @RequestBody ArchiveContractCreateReq updateReq) {
        contractService.saveContract(updateReq);
        return CommonResult.success(true);
    }


    /**
     * 创建合同
     * API: POST /v1/contracts
     * 
     * 注意事项：
     * - 自动生成合同ID
     * - 返回201 Created状态码
     * 
     * @param createReq 创建请求体
     * @return 合同ID
     */
    @Operation(summary = "创建合同")
    @PostMapping
    public CommonResult<Void> createContract(@Validated @RequestBody ArchiveContractCreateReq createReq) {
        contractService.createContract(createReq);
        return CommonResult.success();
    }

    /**
     * 更新合同
     * API: PUT /v1/contracts/
     * 
     * 业务限制：
     * - 已签署合同只允许更新非关键字段
     * 
     * @param updateReq 更新请求体
     * @return 操作结果
     */
    @Operation(summary = "批量更新合同")
    @PutMapping("")
    public CommonResult<Boolean> updateContract(
            @Validated @RequestBody ArchiveContractCreateReq updateReq) {
        contractService.updateContract(updateReq);
        return CommonResult.success(true);
    }

    /**
     * 删除合同
     * API: DELETE /v1/contracts/{id}
     * 
     * 限制条件：
     * - 生效中合同不可删除
     * 
     * @param id 合同ID
     * @return 操作结果
     */
    @Operation(summary = "删除合同")
    @DeleteMapping("/batchUpdate")
    public CommonResult<Boolean> deleteContract(@PathVariable Long id) {
        contractService.deleteContract(id);
        return CommonResult.success(true);
    }

    /**
     * 获取合同详情
     * API: GET /v1/contracts/{id}
     * 
     * @param id 合同ID
     * @return 合同详情
     */
    @Operation(summary = "获取合同详情")
    @GetMapping("/{id}")
    public CommonResult<ArchiveContractResp> getContract(@PathVariable Long id) {
        ArchiveContractResp resp = contractService.getContract(id);
        return CommonResult.success(resp);
    }

    /**
     * 按档案ID查询合同
     * API: GET /v1/contracts/archive/{archiveId}
     * 
     * 排序规则：按创建时间倒序
     * 
     * @param archiveId 档案ID
     * @return 合同列表
     */
    @Operation(summary = "按档案ID查询合同")
    @GetMapping("/archive/{archiveId}")
    public CommonResult<List<ArchiveContractResp>> getByArchive(
            @PathVariable Long archiveId) {
        List<ArchiveContractResp> list = contractService.getContractListByArchive(archiveId);
        return CommonResult.success(list);
    }

    /**
     * 按用户ID查询合同
     * API: GET /v1/contracts/user/{userId}
     * 
     * @param userId 用户ID
     * @return 合同列表
     */
    @Operation(summary = "按用户ID查询合同")
    @GetMapping("/user/{userId}")
    public CommonResult<List<ArchiveContractResp>> getByUser(
            @PathVariable Long userId) {
        List<ArchiveContractResp> list = contractService.getContractListByUser(userId);
        return CommonResult.success(list);
    }
}
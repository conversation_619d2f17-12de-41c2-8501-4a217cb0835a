package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.time.LocalDate;

/**
 * 档案基本信息DTO
 *
 * 设计理念：
 * 1. DTO职责边界定义：
 *    - 作为Controller层的数据传输对象，专注于API接口的数据结构定义
 *    - 不包含业务逻辑，只包含数据验证和基本的数据转换方法
 *    - 与Entity分离，避免数据库结构变更直接影响API接口
 *    - 参考：《领域驱动设计》- Eric Evans
 *
 * 2. 字段选择原则：
 *    - 只包含档案的核心基础信息，避免DTO过于庞大
 *    - 详细信息通过专门的子模块DTO处理（教育经历、工作经历等）
 *    - 敏感信息需要根据用户权限动态控制是否返回
 *
 * 3. 验证注解的选择依据：
 *    - @NotNull: 字段不能为null，但可以为空字符串或空集合
 *    - @NotBlank: 字符串不能为null、空字符串或只包含空白字符
 *    - @NotEmpty: 集合、数组、字符串不能为null且长度大于0
 *    - @Size: 限制字符串长度或集合大小，防止恶意输入
 *    - @Min/@Max: 限制数值范围，确保业务逻辑的合理性
 *    - @Pattern: 正则表达式验证，确保格式的正确性
 *
 * 4. Schema注解的作用：
 *    - 为Swagger/OpenAPI文档生成提供元数据
 *    - 提供字段描述、示例值、是否必需等信息
 *    - 便于前端开发人员理解接口结构
 *    - 支持API文档的自动化生成和维护
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "档案基本信息")
public class ArchiveEnterpriseInfoReq {


    @Schema(description = "企业邮箱", example = "<EMAIL>")
    private String companyEmail;

    @Schema(description = "员工类型：1-正式员工（全职） 2-实习生 3-外包员工 4-其他", example = "1", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "员工类型代码不能小于1")
    @Max(value = 4, message = "员工类型代码不能大于4")
    private Integer employeeType;

    @Schema(description = "员工状态：1-试用期 2-准正式（已转正） 3-待离职 4-已离职", example = "1", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "员工状态代码不能小于1")
    @Max(value = 4, message = "员工状态代码不能大于4")
    private Integer employeeState;

    @Schema(description = "入职日期", example = "2025-01-15")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "入职日期格式应为YYYY-MM-DD")
    private LocalDate entryDate;


    @Schema(description = "预计转正日期", example = "2025-04-15")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "预计转正日期格式应为YYYY-MM-DD")
    private LocalDate regularDate;


    @Schema(description = "试用期开始日期", example = "2025-01-15")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "试用期开始日期格式应为YYYY-MM-DD")
    private LocalDate probationStartDate;

    @Schema(description = "试用期结束日期", example = "2025-04-15")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "试用期结束日期格式应为YYYY-MM-DD")
    private LocalDate probationEndDate;

    @Schema(description = "入职薪资（元）", example = "15000")
    @Min(value = 0, message = "入职薪资不能小于0")
    @Max(value = 1000000, message = "入职薪资不能大于1000000")
    private Integer entrySalary;


    @Schema(description = "转正薪资（元）", example = "18000")
    @Min(value = 0, message = "转正薪资不能小于0")
    @Max(value = 1000000, message = "转正薪资不能大于1000000")
    private Integer regularSalary;

    @Schema(description = "预计转正薪资（元）", example = "18000")
    @Min(value = 0, message = "预计转正薪资不能小于0")
    @Max(value = 1000000, message = "预计转正薪资不能大于1000000")
    private Integer expectedRegularSalary;

    @Schema(description = "当前薪资（元）", example = "25000")
    @Min(value = 0, message = "当前薪资不能小于0")
    @Max(value = 1000000, message = "当前薪资不能大于1000000")
    private Integer currentSalary;

}

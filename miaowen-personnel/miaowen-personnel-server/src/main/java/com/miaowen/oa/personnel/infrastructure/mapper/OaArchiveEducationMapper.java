package com.miaowen.oa.personnel.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveEducationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 档案教育经历信息Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Mapper
public interface OaArchiveEducationMapper extends BaseMapper<OaArchiveEducationEntity> {



  @Select("SELECT MAX(sort) FROM oa_archive_education WHERE archive_id = #{archiveId}")
  Integer getMaxSortByArchiveId(@Param("archiveId") Long archiveId);
}

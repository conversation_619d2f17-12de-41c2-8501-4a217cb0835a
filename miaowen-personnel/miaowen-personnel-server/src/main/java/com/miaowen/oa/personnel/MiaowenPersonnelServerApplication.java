package com.miaowen.oa.personnel;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 人事档案管理系统启动类
 *
 * 优化内容：
 * 1. 启用Feign客户端支持，解决AdminUserApi Bean找不到的问题
 * 2. 配置Feign客户端扫描包路径，支持系统服务API调用
 * 3. 支持微服务架构下的服务间通信
 *
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@MapperScan("com.miaowen.oa.personnel.infrastructure.mapper")
@SpringBootApplication
@EnableFeignClients(basePackages = {
    "com.miaowen.oa.system.api",      // 系统服务API包
    "com.miaowen.oa.personnel.api"    // 人事服务API包（如果有的话）
})
public class MiaowenPersonnelServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(MiaowenPersonnelServerApplication.class, args);
    }

}

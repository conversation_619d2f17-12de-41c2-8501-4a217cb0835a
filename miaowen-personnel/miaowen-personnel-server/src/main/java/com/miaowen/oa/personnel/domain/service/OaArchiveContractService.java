package com.miaowen.oa.personnel.domain.service;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractCreateReq;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractReq;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveContractResp;

import java.util.List;
import java.util.Map;

/**
 * 档案合同服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
public interface OaArchiveContractService {

    /**
     * 创建合同
     *
     * @param createReq 创建请求
     * @return 合同ID
     */
    void createContract(ArchiveContractCreateReq createReq);

    /**
     * 更新合同
     *
     * @param updateReq 更新请求
     */
    void updateContract(ArchiveContractCreateReq updateReq);

    /**
     * 删除合同
     *
     * @param id 合同ID
     */
    void deleteContract(Long id);

    /**
     * 获取合同详情
     *
     * @param id 合同ID
     * @return 合同详情
     */
    ArchiveContractResp getContract(Long id);

    /**
     * 根据档案ID获取合同列表
     *
     * @param archiveId 档案ID
     * @return 合同列表
     */
    List<ArchiveContractResp> getContractListByArchive(Long archiveId);

    /**
     * 根据用户ID获取合同列表
     *
     * @param userId 用户ID
     * @return 合同列表
     */
    List<ArchiveContractResp> getContractListByUser(Long userId);


    void saveContract(ArchiveContractCreateReq updateReq);
}

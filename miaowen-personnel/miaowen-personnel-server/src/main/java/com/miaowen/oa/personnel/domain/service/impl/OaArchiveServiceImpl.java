package com.miaowen.oa.personnel.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.framework.security.core.util.SecurityFrameworkUtils;
import com.miaowen.oa.personnel.domain.enums.ArchiveState;
import com.miaowen.oa.personnel.domain.service.*;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveEntity;
import com.miaowen.oa.personnel.infrastructure.mapper.OaArchiveMapper;
import com.miaowen.oa.personnel.interfaces.req.*;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveAttachmentResp;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveContractResp;
import com.miaowen.oa.personnel.interfaces.resp.EducationExperienceResp;
import com.miaowen.oa.personnel.interfaces.resp.FamilyMemberResp;
import com.miaowen.oa.personnel.interfaces.resp.WorkExperienceResp;
import com.miaowen.oa.personnel.interfaces.resp.archive.*;
import com.miaowen.oa.system.api.user.AdminUserApi;
import com.miaowen.oa.system.api.user.dto.OaUserDetailDTO;
import com.miaowen.oa.system.api.user.dto.UserSaveDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 档案管理服务实现类
 * 
 * 实现理念：
 * 1. 业务逻辑封装：
 *    - 将复杂的业务规则封装在Service层
 *    - 确保业务逻辑的一致性和可重用性
 *    - 提供清晰的业务接口和异常处理
 *    - 参考：《企业应用架构模式》- Martin Fowler
 * 
 * 2. 事务管理策略：
 *    - 使用声明式事务管理，简化事务控制
 *    - 合理设置事务边界，确保数据一致性
 *    - 考虑事务的性能影响，避免长事务
 *    - 参考：Spring Framework事务管理最佳实践
 * 
 * 3. 异常处理机制：
 *    - 定义明确的业务异常类型和错误码
 *    - 提供详细的错误信息和处理建议
 *    - 记录完整的异常上下文，便于问题排查
 *    - 支持异常的分类处理和恢复策略
 * 
 * 4. 数据转换策略：
 *    - 使用BeanUtils进行DTO和Entity之间的转换
 *    - 处理复杂的数据映射和类型转换
 *    - 确保数据转换的准确性和性能
 *    - 考虑空值和默认值的处理
 * 
 * 5. 性能优化考虑：
 *    - 合理使用缓存，减少数据库访问
 *    - 优化查询语句，提高查询效率
 *    - 使用批量操作，提高处理性能
 *    - 考虑异步处理，提升用户体验
 * 
 * 6. 安全性保障：
 *    - 实施严格的权限检查和数据访问控制
 *    - 对敏感数据进行加密和脱敏处理
 *    - 记录详细的操作日志，支持审计追踪
 *    - 防范SQL注入和其他安全威胁
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Slf4j
@Service
public class OaArchiveServiceImpl extends ServiceImpl<OaArchiveMapper, OaArchiveEntity> implements OaArchiveService {

    /**
     * 档案数据访问层
     * 
     * 依赖注入说明：
     * 1. 使用@Resource注解进行依赖注入：
     *    - @Resource是JSR-250标准注解，具有更好的可移植性
     *    - 默认按名称注入，找不到时按类型注入
     *    - 相比@Autowired更加明确和标准化
     * 2. Mapper层的作用：
     *    - 封装数据库访问逻辑，提供CRUD操作
     *    - 使用MyBatis-Plus简化常用操作
     *    - 支持复杂查询和自定义SQL
     * 3. 数据访问优化：
     *    - 合理使用索引，提高查询性能
     *    - 避免N+1查询问题
     *    - 使用分页查询处理大数据量
     */
    @Resource
    private OaArchiveMapper archiveMapper;


    @Resource
    private OaArchiveAttachmentService archiveAttachmentService;

    @Resource
    private OaArchiveContractService contractService;


    @Resource
    private OaArchiveEducationService educationService;

    @Resource
    private OaArchiveFamilyMemberService familyMemberService;

    @Resource
    private OaArchiveWorkExperienceService workExperienceService;

    @Resource
    private AdminUserApi adminUserApi;



    // ========== 档案基础CRUD操作 ==========

    /**
     * 创建档案
     * 
     * 实现逻辑：
     * 1. 数据验证：验证用户是否存在，是否已有档案
     * 2. 编号生成：生成唯一的档案编号
     * 3. 状态初始化：设置档案初始状态为草稿
     * 4. 数据保存：将档案信息保存到数据库
     * 
     * 异常处理：
     * 1. 参数验证异常：抛出IllegalArgumentException
     * 2. 业务规则异常：抛出BusinessException
     * 3. 数据库异常：记录日志并抛出SystemException
     * 
     * 性能考虑：
     * 1. 使用事务确保数据一致性
     * 2. 避免不必要的数据库查询
     * 3. 合理设置事务超时时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveArchive(ArchiveCreateReq createReqDTO) {
        // 参数验证
        if (Objects.isNull(createReqDTO)) {
            log.error("创建档案失败：请求参数为空");
            throw new IllegalArgumentException("创建档案请求参数不能为空");
        }
        
        if (Objects.isNull(createReqDTO.getUserId())) {
            log.error("创建档案失败：用户ID为空");
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {

            OaArchiveEntity result = getBuildArchiveEntity(createReqDTO);

            // 2.1 保存档案
            boolean res = archiveMapper.insertOrUpdate(result);
            if (!res) {
                log.error("编辑档案失败：数据库编辑失败, userId={}", createReqDTO.getUserId());
                throw new RuntimeException("编辑档案失败，请稍后重试");
            }

            log.info("档案编辑成功：archiveId={}, userId={}, archiveNumber={}",
                    result.getId(), createReqDTO.getUserId(), result.getArchiveNumber());

            //2.2 保存用户信息
            UserSaveDTO dto = BeanUtils.toBean(createReqDTO.getBasicInfo(), UserSaveDTO.class);
            org.springframework.beans.BeanUtils.copyProperties(createReqDTO.getEnterpriseInfo(), dto);
            org.springframework.beans.BeanUtils.copyProperties(result, dto);
            dto.setGraduateSchool(result.getHistoricalGraduateSchool());
            dto.setMajor(result.getHistoricalMajor());
            dto.setEducation(result.getHighestEducation());
            dto.setMaritalState(result.getHistoricalMaritalState());
            adminUserApi.save(dto);

//            //3. 保存教育经历
//            createReqDTO.getEducationExperienceCreateReq().setUserId(createReqDTO.getUserId());
//            createReqDTO.getEducationExperienceCreateReq().setArchiveId(result.getId());
//            educationService.saveEducation(createReqDTO.getEducationExperienceCreateReq());
//
//            //4. 保存工作经历
//            createReqDTO.getWorkExperienceCreateReq().setUserId(createReqDTO.getUserId());
//            createReqDTO.getWorkExperienceCreateReq().setArchiveId(result.getId());
//            workExperienceService.saveWorkExperience(createReqDTO.getWorkExperienceCreateReq());
//
//            //5. 保存家庭情况
//            createReqDTO.getFamilyMemberCreateReq().setUserId(createReqDTO.getUserId());
//            createReqDTO.getFamilyMemberCreateReq().setArchiveId(result.getId());
//            familyMemberService.saveFamilyMember(createReqDTO.getFamilyMemberCreateReq());
//
//            //6. 保存合同信息
//            createReqDTO.getContractCreateReq().setUserId(createReqDTO.getUserId());
//            createReqDTO.getContractCreateReq().setArchiveId(result.getId());
//            contractService.saveContract(createReqDTO.getContractCreateReq());

            return result.getId();

        } catch (Exception e) {
            log.error("编辑档案异常：userId={}, error={}", createReqDTO.getUserId(), e.getMessage(), e);
            throw new RuntimeException("档案编辑失败：" + e.getMessage(), e);
        }
    }

    /**
     * 构建档案entity
     * @param createReqDTO 创建档案dto
     * @return 返回entity
     */
    @NotNull
    private OaArchiveEntity getBuildArchiveEntity(ArchiveCreateReq createReqDTO) {
        // 生成档案编号
        String archiveNumber =  generateArchiveNumber();

        //  构建档案实体
        OaArchiveEntity archiveEntity = BeanUtils.toBean(createReqDTO.getBasicInfo(), OaArchiveEntity.class);

        //设置档案基本信息
        archiveEntity.setUserId(createReqDTO.getUserId());
        archiveEntity.setArchiveNumber(archiveNumber);
        // 设置初始状态为草稿状态（使用状态机枚举）
        archiveEntity.setArchiveState(ArchiveState.APPROVED.getCode());
        archiveEntity.setArchiveType(createReqDTO.getArchiveType());
        archiveEntity.setSecurityLevel(3);

        //设置档案创建信息
        archiveEntity.setArchiveCreatorId(SecurityFrameworkUtils.getLoginUserId());
        archiveEntity.setArchiveCreatorName(SecurityFrameworkUtils.getLoginUserRealName());
        archiveEntity.setArchiveCreateDate(LocalDateTime.now());

        //设置档案更新信息
        archiveEntity.setLastUpdateDate(LocalDateTime.now());
        archiveEntity.setLastUpdaterName(SecurityFrameworkUtils.getLoginUserRealName());

        archiveEntity.setCreateTime(LocalDateTime.now());
        archiveEntity.setUpdateTime(LocalDateTime.now());

        if (CollectionUtils.isEmpty(createReqDTO.getEducationExperienceCreateReq().getList())){
            archiveEntity.setHistoricalMajor("");
            archiveEntity.setHistoricalGraduateSchool("");
            archiveEntity.setGraduationEndDate("");
            archiveEntity.setGraduationStartDate("");
        }else {
            EducationExperienceReq education = createReqDTO.getEducationExperienceCreateReq().getList().stream()
                    .max(Comparator.comparingInt(e -> e.getEducationLevel() == null ? 0 : e.getEducationLevel())).get();
            // 设置历史教育信息
            //遍历教育经历列表，获得最高学历educationLevel对应的专业
            archiveEntity.setHistoricalMajor(education.getMajor());
            archiveEntity.setHistoricalGraduateSchool(education.getSchoolName());
            archiveEntity.setGraduationStartDate(education.getStartDate());
            archiveEntity.setGraduationStartDate(education.getStartDate());
        }
        //设置档案历史记录信息
        archiveEntity.setHistoricalMaritalState(createReqDTO.getBasicInfo().getMaritalState());
        archiveEntity.setHistoricalEntryDate(createReqDTO.getEnterpriseInfo().getEntryDate());
        archiveEntity.setHistoricalRegularDate(createReqDTO.getEnterpriseInfo().getRegularDate());
        archiveEntity.setHistoricalProbationEndDate(createReqDTO.getEnterpriseInfo().getProbationEndDate());
        archiveEntity.setHistoricalProbationStartDate(createReqDTO.getEnterpriseInfo().getProbationStartDate());
        return archiveEntity;
    }


    /**
     * 删除档案
     * 
     * 实现逻辑：
     * 1. 档案存在性检查
     * 2. 状态检查：只有草稿状态可以删除
     * 3. 关联数据检查：检查是否有关联的子数据
     * 4. 软删除处理：标记删除而非物理删除
     * 
     * 安全考虑：
     * 1. 使用软删除，保留数据可恢复性
     * 2. 记录删除操作日志
     * 3. 检查用户权限
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArchive(Long id) {
        if (Objects.isNull(id)) {
            log.error("删除档案失败：档案ID为空");
            throw new IllegalArgumentException("档案ID不能为空");
        }

        try {
            // 1. 检查档案是否存在
            OaArchiveEntity existingArchive = archiveMapper.selectById(id);
            if (Objects.isNull(existingArchive)) {
                log.warn("删除档案失败：档案不存在, archiveId={}", id);
                throw new RuntimeException("档案不存在");
            }

            // 2. 检查档案状态是否允许删除（使用状态机枚举）
            com.miaowen.oa.personnel.domain.enums.ArchiveState currentState =
                    com.miaowen.oa.personnel.domain.enums.ArchiveState.getByCode(existingArchive.getArchiveState());

            if (currentState == null || !com.miaowen.oa.personnel.domain.enums.ArchiveState.isDeletable(currentState)) {
                log.warn("删除档案失败：档案状态不允许删除, archiveId={}, state={}",
                        id, currentState != null ? currentState.getName() : "未知状态");
                throw new RuntimeException("当前档案状态不允许删除");
            }

       // 3\. 执行软删除（将 deleteTime 字段设置为当前时间）
       OaArchiveEntity updateEntity = new OaArchiveEntity();
       updateEntity.setId(id);
       updateEntity.setDeleteTime(System.currentTimeMillis());
       int deleteResult = archiveMapper.updateById(updateEntity);
            if (deleteResult <= 0) {
                log.error("删除档案失败：数据库删除失败, archiveId={}", id);
                throw new RuntimeException("档案删除失败，请稍后重试");
            }

            log.info("档案删除成功：archiveId={}, userId={}", id, existingArchive.getUserId());

        } catch (Exception e) {
            log.error("删除档案异常：archiveId={}, error={}", id, e.getMessage(), e);
            throw new RuntimeException("档案删除失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取档案详情
     * 
     * 实现逻辑：
     * 1. 根据ID查询档案基本信息
     * 2. 权限检查和数据脱敏处理
     * 3. 关联数据的可选加载
     * 4. 数据转换和返回
     */
    @Override
    public ArchiveDetailInfoResp getArchive(Long id) {
        ArchiveDetailInfoResp resp = new ArchiveDetailInfoResp();
        if (Objects.isNull(id)) {
            log.error("获取档案详情失败：档案ID为空");
            throw new IllegalArgumentException("档案ID不能为空");
        }

        try {
            // 查询档案信息
            OaArchiveEntity archiveEntity = archiveMapper.selectById(id);
            if (Objects.isNull(archiveEntity)) {
                log.warn("获取档案详情失败：档案不存在, archiveId={}", id);
                return null;
            }

            // 转换为DTO并返回
            OaUserDetailDTO detail = adminUserApi.detail(archiveEntity.getUserId()).getData();
            ArchiveBasicInfoResp result = BeanUtils.toBean(archiveEntity, ArchiveBasicInfoResp.class);
            org.springframework.beans.BeanUtils.copyProperties(detail, result);
            resp.setBasicInfoResp(result);


            ArchiveEnterpriseInfoResp enterpriseInfoResp = new ArchiveEnterpriseInfoResp();
            org.springframework.beans.BeanUtils.copyProperties(detail, enterpriseInfoResp);
            enterpriseInfoResp.setDto(detail);
            resp.setEnterpriseInfoResp(enterpriseInfoResp);

            // 获取附件信息
            List<ArchiveAttachmentResp> attachmentListByArchive = archiveAttachmentService.getAttachmentListByArchive(id, null, true);
            ArchiveAttachmentResp archiveAttachmentResp = mergeAttachments(attachmentListByArchive);
            resp.setAttachmentResp(archiveAttachmentResp);
            
            if (attachmentListByArchive != null && !attachmentListByArchive.isEmpty()) {
                log.debug("设置档案附件信息成功：archiveId={}, attachmentCount={}, hasRequiredAttachments={}", 
                         id, attachmentListByArchive.size(), archiveAttachmentResp.hasRequiredAttachments());
            } else {
                log.debug("档案无附件信息：archiveId={}", id);
            }

            ArchiveContractInfoResp contractInfoResp = new ArchiveContractInfoResp();
            List<ArchiveContractResp> contractListByArchive = contractService.getContractListByArchive(id);
            List<com.miaowen.oa.personnel.interfaces.resp.archive.ArchiveContractResp> contractRespList = BeanUtils.toBean(contractListByArchive, com.miaowen.oa.personnel.interfaces.resp.archive.ArchiveContractResp.class);
            contractInfoResp.setList(contractRespList);
            resp.setContractInfoResp(contractInfoResp);


            FamilyMemberInfoResp familyMemberInfoResp = new FamilyMemberInfoResp();
            List<FamilyMemberResp> familyMemberListByArchive = familyMemberService.getFamilyMemberListByArchive(id);
            List<com.miaowen.oa.personnel.interfaces.resp.archive.FamilyMemberResp> familyMemberRespList = BeanUtils.toBean(familyMemberListByArchive, com.miaowen.oa.personnel.interfaces.resp.archive.FamilyMemberResp.class);
            familyMemberInfoResp.setList(familyMemberRespList);
            resp.setFamilyMemberInfoResp(familyMemberInfoResp);



            WorkExperienceInfoResp workExperienceInfoResp = new WorkExperienceInfoResp();
            List<WorkExperienceResp> workExperienceListByArchive = workExperienceService.getWorkExperienceListByArchive(id);
            List<com.miaowen.oa.personnel.interfaces.resp.archive.WorkExperienceResp> workExperienceRespList
                    = BeanUtils.toBean(workExperienceListByArchive, com.miaowen.oa.personnel.interfaces.resp.archive.WorkExperienceResp.class);
            workExperienceInfoResp.setList(workExperienceRespList);
            resp.setWorkExperienceInfoResp(workExperienceInfoResp);

            EducationExperienceInfoResp educationExperienceInfoResp = new EducationExperienceInfoResp();
            List<EducationExperienceResp> educationListByArchive = educationService.getEducationListByArchive(id);
            List<com.miaowen.oa.personnel.interfaces.resp.archive.EducationExperienceResp> educationExperienceRespList
                    = BeanUtils.toBean(educationListByArchive, com.miaowen.oa.personnel.interfaces.resp.archive.EducationExperienceResp.class);
            educationExperienceInfoResp.setList(educationExperienceRespList);
            resp.setEducationExperienceInfoResp(educationExperienceInfoResp);




            log.debug("获取档案详情成功：archiveId={}, userId={}", id, archiveEntity.getUserId());
            return resp;

        } catch (Exception e) {
            log.error("获取档案详情异常：archiveId={}, error={}", id, e.getMessage(), e);
            throw new RuntimeException("获取档案详情失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据用户ID获取档案
     *
     * 使用LambdaQueryWrapper优化：
     * 1. 类型安全的字段引用，避免硬编码字段名
     * 2. 编译期检查，减少运行时错误
     * 3. 重构友好，字段重命名时自动更新
     * 4. 链式调用，代码更加简洁清晰
     */
    @Override
    public ArchiveBasicInfoResp getArchiveByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            log.error("根据用户ID获取档案失败：用户ID为空");
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveEntity::getUserId, userId)
                   .eq(OaArchiveEntity::getDeleteTime, false);

            OaArchiveEntity archiveEntity = this.getOne(wrapper);
            if (Objects.isNull(archiveEntity)) {
                log.debug("根据用户ID获取档案：档案不存在, userId={}", userId);
                return null;
            }

            log.debug("根据用户ID获取档案成功：userId={}, archiveId={}", userId, archiveEntity.getId());
            return BeanUtils.toBean(archiveEntity, ArchiveBasicInfoResp.class);

        } catch (Exception e) {
            log.error("根据用户ID获取档案异常：userId={}, error={}", userId, e.getMessage(), e);
            throw new RuntimeException("获取档案失败：" + e.getMessage(), e);
        }
    }

    /**
     * 分页查询档案
     *
     * 使用LambdaQueryWrapper优化实现：
     * 1. 类型安全的条件构建，避免字段名硬编码
     * 2. 动态条件组合，支持复杂查询场景
     * 3. 链式调用，代码简洁清晰
     * 4. 编译期检查，减少运行时错误
     */
    @Override
    public PageResult<ArchiveBasicInfoResp> getArchivePage(Integer pageNo, Integer pageSize, Object queryConditions) {
        // 参数验证和默认值设置
        pageNo = Objects.isNull(pageNo) || pageNo < 1 ? 1 : pageNo;
        pageSize = Objects.isNull(pageSize) || pageSize < 1 ? 10 : pageSize;
        pageSize = Math.min(pageSize, 100); // 限制最大页大小

        try {
            // 构建分页对象
            Page<OaArchiveEntity> page = new Page<>(pageNo, pageSize);

            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();

            // 基础条件：未删除的记录
            wrapper.eq(OaArchiveEntity::getDeleteTime, false);

            // 动态条件构建
            if (queryConditions instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> conditions = (Map<String, Object>) queryConditions;

                // 档案状态条件
                Object archiveState = conditions.get("archiveState");
                if (Objects.nonNull(archiveState)) {
                    wrapper.eq(OaArchiveEntity::getArchiveState, archiveState);
                }

                // 档案类型条件
                Object archiveType = conditions.get("archiveType");
                if (Objects.nonNull(archiveType)) {
                    wrapper.eq(OaArchiveEntity::getArchiveType, archiveType);
                }

                // 档案编号条件（模糊查询）
                Object archiveNumber = conditions.get("archiveNumber");
                if (Objects.nonNull(archiveNumber) && !archiveNumber.toString().trim().isEmpty()) {
                    wrapper.like(OaArchiveEntity::getArchiveNumber, archiveNumber.toString().trim());
                }

                // 用户ID条件
                Object userId = conditions.get("userId");
                if (Objects.nonNull(userId)) {
                    wrapper.eq(OaArchiveEntity::getUserId, userId);
                }

                // 创建时间范围条件
                Object createTimeStart = conditions.get("createTimeStart");
                if (Objects.nonNull(createTimeStart)) {
                    wrapper.ge(OaArchiveEntity::getCreateTime, createTimeStart);
                }

                Object createTimeEnd = conditions.get("createTimeEnd");
                if (Objects.nonNull(createTimeEnd)) {
                    wrapper.le(OaArchiveEntity::getCreateTime, createTimeEnd);
                }
            }

            // 默认排序：按创建时间倒序
            wrapper.orderByDesc(OaArchiveEntity::getCreateTime);

            // 执行分页查询
            IPage<OaArchiveEntity> pageResult = this.page(page, wrapper);

            // 数据转换
            List<ArchiveBasicInfoResp> dtoList = BeanUtils.toBean(pageResult.getRecords(), ArchiveBasicInfoResp.class);

            log.debug("分页查询档案成功：pageNo={}, pageSize={}, total={}, records={}",
                    pageNo, pageSize, pageResult.getTotal(), pageResult.getRecords().size());

            // 构建分页结果
            return new PageResult<>(dtoList, pageResult.getTotal());

        } catch (Exception e) {
            log.error("分页查询档案异常：pageNo={}, pageSize={}, error={}",
                    pageNo, pageSize, e.getMessage(), e);
            throw new RuntimeException("分页查询档案失败：" + e.getMessage(), e);
        }
    }

    // ========== 常用查询方法（基于LambdaQueryWrapper） ==========

    /**
     * 根据档案编号查询档案
     *
     * 使用LambdaQueryWrapper实现类型安全的查询
     */
    @Override
    public ArchiveBasicInfoReq getArchiveByNumber(String archiveNumber) {
        if (!org.springframework.util.StringUtils.hasText(archiveNumber)) {
            log.error("根据档案编号查询档案失败：档案编号为空");
            throw new IllegalArgumentException("档案编号不能为空");
        }

        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveEntity::getArchiveNumber, archiveNumber.trim())
                   .eq(OaArchiveEntity::getDeleteTime, false);

            OaArchiveEntity archiveEntity = this.getOne(wrapper);
            if (Objects.isNull(archiveEntity)) {
                log.debug("根据档案编号查询档案：档案不存在, archiveNumber={}", archiveNumber);
                return null;
            }

            log.debug("根据档案编号查询档案成功：archiveNumber={}, archiveId={}",
                    archiveNumber, archiveEntity.getId());
            return BeanUtils.toBean(archiveEntity, ArchiveBasicInfoReq.class);

        } catch (Exception e) {
            log.error("根据档案编号查询档案异常：archiveNumber={}, error={}",
                    archiveNumber, e.getMessage(), e);
            throw new RuntimeException("查询档案失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据状态查询档案列表
     */
    @Override
    public List<ArchiveBasicInfoReq> getArchivesByState(Integer archiveState) {
        if (Objects.isNull(archiveState)) {
            log.error("根据状态查询档案失败：状态参数为空");
            throw new IllegalArgumentException("档案状态不能为空");
        }

        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveEntity::getArchiveState, archiveState)
                   .eq(OaArchiveEntity::getDeleteTime, false)
                   .orderByDesc(OaArchiveEntity::getCreateTime);

            List<OaArchiveEntity> archiveList = this.list(wrapper);

            log.debug("根据状态查询档案成功：archiveState={}, count={}",
                    archiveState, archiveList.size());

            return BeanUtils.toBean(archiveList, ArchiveBasicInfoReq.class);

        } catch (Exception e) {
            log.error("根据状态查询档案异常：archiveState={}, error={}",
                    archiveState, e.getMessage(), e);
            throw new RuntimeException("查询档案失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据类型查询档案列表
     */
    @Override
    public List<ArchiveBasicInfoReq> getArchivesByType(Integer archiveType) {
        if (Objects.isNull(archiveType)) {
            log.error("根据类型查询档案失败：类型参数为空");
            throw new IllegalArgumentException("档案类型不能为空");
        }

        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveEntity::getArchiveType, archiveType)
                   .eq(OaArchiveEntity::getDeleteTime, false)
                   .orderByDesc(OaArchiveEntity::getCreateTime);

            List<OaArchiveEntity> archiveList = this.list(wrapper);

            log.debug("根据类型查询档案成功：archiveType={}, count={}",
                    archiveType, archiveList.size());

            return BeanUtils.toBean(archiveList, ArchiveBasicInfoReq.class);

        } catch (Exception e) {
            log.error("根据类型查询档案异常：archiveType={}, error={}",
                    archiveType, e.getMessage(), e);
            throw new RuntimeException("查询档案失败：" + e.getMessage(), e);
        }
    }

    /**
     * 统计档案总数
     */
    @Override
    public Long countTotalArchives() {
        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveEntity::getDeleteTime, 0);

            Long count = this.count(wrapper);

            log.debug("统计档案总数成功：count={}", count);
            return count;

        } catch (Exception e) {
            log.error("统计档案总数异常：error={}", e.getMessage(), e);
            throw new RuntimeException("统计档案总数失败：" + e.getMessage(), e);
        }
    }

    /**
     * 按状态统计档案数量
     */
    @Override
    public Map<Integer, Long> countArchivesByState() {
        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveEntity::getDeleteTime, false);

            List<OaArchiveEntity> allArchives = this.list(wrapper);

            // 按状态分组统计
            Map<Integer, Long> stateCountMap = allArchives.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            OaArchiveEntity::getArchiveState,
                            java.util.stream.Collectors.counting()
                    ));

            log.debug("按状态统计档案数量成功：stateCount={}", stateCountMap);
            return stateCountMap;

        } catch (Exception e) {
            log.error("按状态统计档案数量异常：error={}", e.getMessage(), e);
            throw new RuntimeException("统计档案数量失败：" + e.getMessage(), e);
        }
    }

    /**
     * 按类型统计档案数量
     */
    @Override
    public Map<Integer, Long> countArchivesByType() {
        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OaArchiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaArchiveEntity::getDeleteTime, false);

            List<OaArchiveEntity> allArchives = this.list(wrapper);

            // 按类型分组统计
            Map<Integer, Long> typeCountMap = allArchives.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            OaArchiveEntity::getArchiveType,
                            java.util.stream.Collectors.counting()
                    ));

            log.debug("按类型统计档案数量成功：typeCount={}", typeCountMap);
            return typeCountMap;

        } catch (Exception e) {
            log.error("按类型统计档案数量异常：error={}", e.getMessage(), e);
            throw new RuntimeException("统计档案数量失败：" + e.getMessage(), e);
        }
    }

    // ========== 档案状态管理 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitAudit(Long id) {
        // TODO: 实现提交审核逻辑
        log.info("提交档案审核：archiveId={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveArchive(Long id, String comment) {
        // TODO: 实现审核通过逻辑
        log.info("档案审核通过：archiveId={}, comment={}", id, comment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectArchive(Long id, String reason) {
        // TODO: 实现审核拒绝逻辑
        log.info("档案审核拒绝：archiveId={}, reason={}", id, reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archiveRecord(Long id) {
        // TODO: 实现归档逻辑
        log.info("档案归档：archiveId={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockArchive(Long id, String reason) {
        // TODO: 实现锁定逻辑
        log.info("档案锁定：archiveId={}, reason={}", id, reason);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlockArchive(Long id, String reason) {
        // TODO: 实现解锁逻辑
        log.info("档案解锁：archiveId={}, reason={}", id, reason);
    }


    // ========== 批量操作 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object batchCreateArchives(List<ArchiveBasicInfoReq> createReqDTOList) {
        // TODO: 实现批量创建逻辑
        return new Object();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object batchUpdateArchiveState(List<Long> ids, Integer targetState, String reason) {
        // TODO: 实现批量状态更新逻辑
        // 1. 验证目标状态的有效性
        com.miaowen.oa.personnel.domain.enums.ArchiveState targetArchiveState =
                com.miaowen.oa.personnel.domain.enums.ArchiveState.getByCode(targetState);

        if (targetArchiveState == null) {
            throw new IllegalArgumentException("无效的目标状态: " + targetState);
        }

        // 2. 批量验证状态转换的合法性
        // 3. 执行批量状态更新
        // 4. 记录状态转换历史

        log.info("批量更新档案状态: ids={}, targetState={}, reason={}", ids, targetArchiveState.getName(), reason);
        return new Object();
    }

    // ========== 数据导入导出 ==========

    @Override
    public Object exportArchives(Object exportConditions) {
        // TODO: 实现导出逻辑
        return new Object();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object importArchives(Object importFile) {
        // TODO: 实现导入逻辑
        return new Object();
    }

    // ========== 私有辅助方法 ==========

    /**
     * 生成档案编号
     * 
     * 编号规则：ARCH-YYYY-NNNNNN
     * - ARCH: 固定前缀
     * - YYYY: 当前年份
     * - NNNNNN: 6位序号，从000001开始
     * 
     * 实现方式：
     * 1. 查询当年已有档案的最大序号
     * 2. 序号加1生成新的编号
     * 3. 考虑并发情况下的唯一性
     */
    private String generateArchiveNumber() {
        try {
            // TODO: 实现档案编号生成逻辑
            // 1. 获取当前年份
            // 2. 查询当年最大序号
            // 3. 生成新编号
            
            int currentYear = java.time.LocalDate.now().getYear();
            // 临时实现：使用时间戳生成唯一编号
            long timestamp = System.currentTimeMillis() % 1000000;
            return String.format("ARCH-%d-%06d", currentYear, timestamp);
            
        } catch (Exception e) {
            log.error("生成档案编号失败：{}", e.getMessage(), e);
            throw new RuntimeException("生成档案编号失败", e);
        }
    }

    /**
     * 获取档案当前状态枚举
     *
     * 状态获取逻辑：
     * 1. 根据状态码获取对应的枚举值
     * 2. 提供状态的完整信息（名称、描述、颜色等）
     * 3. 支持状态转换规则的验证
     *
     * @param stateCode 状态码
     * @return 状态枚举，如果无效返回null
     */
    private com.miaowen.oa.personnel.domain.enums.ArchiveState getArchiveState(Integer stateCode) {
        return com.miaowen.oa.personnel.domain.enums.ArchiveState.getByCode(stateCode);
    }

    /**
     * 验证档案状态转换是否合法
     *
     * 转换验证逻辑：
     * 1. 检查源状态和目标状态的有效性
     * 2. 验证状态转换规则是否允许
     * 3. 记录转换验证的结果
     *
     * @param fromStateCode 源状态码
     * @param toStateCode 目标状态码
     * @return true-转换合法，false-转换不合法
     */
    private boolean isStateTransitionValid(Integer fromStateCode, Integer toStateCode) {
        com.miaowen.oa.personnel.domain.enums.ArchiveState fromState = getArchiveState(fromStateCode);
        com.miaowen.oa.personnel.domain.enums.ArchiveState toState = getArchiveState(toStateCode);

        if (fromState == null || toState == null) {
            return false;
        }

        return com.miaowen.oa.personnel.domain.enums.ArchiveState.canTransition(fromState, toState);
    }

    /**
     * 获取档案可转换的状态列表
     *
     * @param currentStateCode 当前状态码
     * @return 可转换的状态列表
     */
    private java.util.List<com.miaowen.oa.personnel.domain.enums.ArchiveState> getAvailableTransitions(Integer currentStateCode) {
        com.miaowen.oa.personnel.domain.enums.ArchiveState currentState = getArchiveState(currentStateCode);

        if (currentState == null) {
            return java.util.List.of();
        }

        return com.miaowen.oa.personnel.domain.enums.ArchiveState.getNextStates(currentState);
    }

    /**
     * 合并多个附件响应对象为一个
     * 
     * @param attachmentList 附件列表
     * @return 合并后的附件响应对象
     */
    private ArchiveAttachmentResp mergeAttachments(List<ArchiveAttachmentResp> attachmentList) {
        if (attachmentList == null || attachmentList.isEmpty()) {
            return new ArchiveAttachmentResp();
        }
        
        ArchiveAttachmentResp mergedAttachment = new ArchiveAttachmentResp();
        
        for (ArchiveAttachmentResp attachment : attachmentList) {
            // 合并各个附件类型的URL，优先保留非空值
            if (attachment.getResumeFileUrl() != null) {
                mergedAttachment.setResumeFileUrl(attachment.getResumeFileUrl());
            }
            if (attachment.getApplicationRegistrationFileUrl() != null) {
                mergedAttachment.setApplicationRegistrationFileUrl(attachment.getApplicationRegistrationFileUrl());
            }
            if (attachment.getIdCardFrontFileUrl() != null) {
                mergedAttachment.setIdCardFrontFileUrl(attachment.getIdCardFrontFileUrl());
            }
            if (attachment.getIdCardBackFileUrl() != null) {
                mergedAttachment.setIdCardBackFileUrl(attachment.getIdCardBackFileUrl());
            }
            if (attachment.getGraduationCertificateFileUrl() != null) {
                mergedAttachment.setGraduationCertificateFileUrl(attachment.getGraduationCertificateFileUrl());
            }
            if (attachment.getDegreeCertificateFileUrl() != null) {
                mergedAttachment.setDegreeCertificateFileUrl(attachment.getDegreeCertificateFileUrl());
            }
            if (attachment.getSkillCertificateFileUrl() != null) {
                mergedAttachment.setSkillCertificateFileUrl(attachment.getSkillCertificateFileUrl());
            }
            if (attachment.getMedicalReportFileUrl() != null) {
                mergedAttachment.setMedicalReportFileUrl(attachment.getMedicalReportFileUrl());
            }
            if (attachment.getResignationCertificateFileUrl() != null) {
                mergedAttachment.setResignationCertificateFileUrl(attachment.getResignationCertificateFileUrl());
            }
            if (attachment.getBankStatementFileUrl() != null) {
                mergedAttachment.setBankStatementFileUrl(attachment.getBankStatementFileUrl());
            }
            if (attachment.getEmployeeDeclarationFileUrl() != null) {
                mergedAttachment.setEmployeeDeclarationFileUrl(attachment.getEmployeeDeclarationFileUrl());
            }
            if (attachment.getEmploymentConfirmationFileUrl() != null) {
                mergedAttachment.setEmploymentConfirmationFileUrl(attachment.getEmploymentConfirmationFileUrl());
            }
            if (attachment.getNonCompeteAgreementFileUrl() != null) {
                mergedAttachment.setNonCompeteAgreementFileUrl(attachment.getNonCompeteAgreementFileUrl());
            }
            if (attachment.getLaborContractFileUrl() != null) {
                mergedAttachment.setLaborContractFileUrl(attachment.getLaborContractFileUrl());
            }
            if (attachment.getConfidentialityAgreementFileUrl() != null) {
                mergedAttachment.setConfidentialityAgreementFileUrl(attachment.getConfidentialityAgreementFileUrl());
            }
            if (attachment.getOtherAttachmentFileUrl() != null) {
                mergedAttachment.setOtherAttachmentFileUrl(attachment.getOtherAttachmentFileUrl());
            }
        }
        
        return mergedAttachment;
    }
}

package com.miaowen.oa.personnel.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveWorkExperienceService;
import com.miaowen.oa.personnel.domain.service.OaEntryService;
import com.miaowen.oa.personnel.interfaces.req.EntrySaveReq;
import com.miaowen.oa.personnel.interfaces.req.WorkExperienceCreateReq;
import com.miaowen.oa.personnel.interfaces.resp.WorkExperienceResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 档案工作经历管理 RESTful API
 * 提供工作经历的增删改查、背景调查、统计分析等功能
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "入职管理", description = "员工工作经历的详细管理功能")
@RestController
@RequestMapping("/personnel/archive/entry")
@Validated
@Slf4j
public class OaEntryController {

    private final OaEntryService oaEntryService;

    public OaEntryController(OaEntryService oaEntryService) {
        this.oaEntryService = oaEntryService;
    }

    // ========== 工作经历基础CRUD操作 ==========


    @Operation(summary = "编辑工作经历")
    @PostMapping("/save")
    public CommonResult<Boolean> saveEntry(
            @Validated @RequestBody EntrySaveReq updateReq) {
        oaEntryService.saveEntry(updateReq);
        return CommonResult.success(true);
    }




    /**
     * 分页查询工作经历
     * 
     * 选择当前实现的原因：
     * 1. 支持复杂查询条件，满足高级搜索需求
     * 2. 采用分页机制，提高大数据量场景下的性能
     * 3. 支持多字段排序，提供灵活的展示方式
     * 
     * 参数注意事项：
     * - 查询条件需要防SQL注入
     * - 分页参数需要验证合理性
     * - 排序字段需要白名单控制
     */
    @Operation(summary = "分页查询工作经历", description = "支持多条件分页查询工作经历")
    @GetMapping("/page")
    public CommonResult<PageResult<WorkExperienceResp>> getWorkExperiencePage(
            @Parameter(description = "档案ID") @RequestParam(value = "archiveId", required = false) Long archiveId,
            @Parameter(description = "用户ID") @RequestParam(value = "userId", required = false) Long userId,
            @Parameter(description = "公司名称") @RequestParam(value = "companyName", required = false) String companyName,
            @Parameter(description = "职位名称") @RequestParam(value = "position", required = false) String position,
            @Parameter(description = "是否在职") @RequestParam(value = "isCurrentJob", required = false) Integer isCurrentJob,
            @Parameter(description = "页码", required = true) @RequestParam("pageNo") @NotNull Integer pageNo,
            @Parameter(description = "每页大小", required = true) @RequestParam("pageSize") @NotNull Integer pageSize) {
        
        log.info("分页查询工作经历请求: archiveId={}, userId={}, companyName={}, position={}, isCurrentJob={}, pageNo={}, pageSize={}", 
                archiveId, userId, companyName, position, isCurrentJob, pageNo, pageSize);
        
        try {
//            PageResult<WorkExperienceResp> pageResult = oaEntryService.getEntryPage(
//                    archiveId, userId, companyName, position, isCurrentJob, pageNo, pageSize);
//            log.info("分页查询工作经历成功: total={}, pageNo={}, pageSize={}",
//                    pageResult.getTotal(), pageNo, pageSize);
            return CommonResult.success();
            
        } catch (IllegalArgumentException e) {
            log.warn("分页查询工作经历参数错误: error={}", e.getMessage());
            return CommonResult.error(400, e.getMessage());
            
        } catch (RuntimeException e) {
            log.error("分页查询工作经历业务异常: error={}", e.getMessage());
            return CommonResult.error(500, e.getMessage());
            
        } catch (Exception e) {
            log.error("分页查询工作经历系统异常: error={}", e.getMessage(), e);
            return CommonResult.error(500, "系统异常，请稍后重试");
        }
    }


}

package com.miaowen.oa.personnel.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 档案薪资调整记录实体类
 * 存储员工的薪资调整历史记录
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_archive_salary_adjustment")
public class OaArchiveSalaryAdjustmentEntity extends BaseDO {

    /**
     * 档案ID（外键关联oa_archive表）
     */
    private Long archiveId;

    /**
     * 用户ID（冗余字段，便于查询）
     */
    private Long userId;

    /**
     * 调薪编号
     */
    private String adjustmentNumber;

    /**
     * 调薪类型
     * 1-入职定薪 2-转正调薪 3-晋升调薪 4-年度调薪 5-绩效调薪 6-市场调薪 7-特殊调薪 8-降薪 9-其他
     */
    private Integer adjustmentType;

    /**
     * 调薪原因
     */
    private String adjustmentReason;

    /**
     * 调薪原因分类
     * 1-绩效优秀 2-职位晋升 3-市场调研 4-内部公平 5-挽留人才 6-成本控制 7-其他
     */
    private Integer reasonCategory;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 申请日期
     */
    private String applicationDate;


    //-------------------------调薪前薪资结构
    /**
     * 调薪前基本薪资（元/月）
     */
    private Integer beforeBasicSalary;

    /**
     * 调薪前岗位薪资（元/月）
     */
    private Integer beforePositionSalary;

    /**
     * 调薪前绩效薪资（元/月）
     */
    private Integer beforePerformanceSalary;

    /**
     * 调薪前津贴补贴（元/月）
     */
    private Integer beforeAllowance;

    /**
     * 调薪前月薪总额（元）
     */
    private Integer beforeTotalMonthlySalary;

    /**
     * 调薪前年薪总额（元）
     */
    private Integer beforeTotalAnnualSalary;

    //-------------------------调薪后薪资结构
    /**
     * 调薪后基本薪资（元/月）
     */
    private Integer afterBasicSalary;

    /**
     * 调薪后岗位薪资（元/月）
     */
    private Integer afterPositionSalary;

    /**
     * 调薪后绩效薪资（元/月）
     */
    private Integer afterPerformanceSalary;

    /**
     * 调薪后津贴补贴（元/月）
     */
    private Integer afterAllowance;

    /**
     * 调薪后月薪总额（元）
     */
    private Integer afterTotalMonthlySalary;

    /**
     * 调薪后年薪总额（元）
     */
    private Integer afterTotalAnnualSalary;

    //-------------------------调薪幅度计算
    /**
     * 月薪调整金额（元）
     */
    private Integer monthlyAdjustmentAmount;

    /**
     * 年薪调整金额（元）
     */
    private Integer annualAdjustmentAmount;

    /**
     * 月薪调整比例（百分比）
     */
    private BigDecimal monthlyAdjustmentPercentage;

    /**
     * 年薪调整比例（百分比）
     */
    private BigDecimal annualAdjustmentPercentage;

    //-------------------------职位信息
    /**
     * 调薪前职位
     */
    private String beforePosition;

    /**
     * 调薪后职位
     */
    private String afterPosition;

    /**
     * 调薪前职级
     */
    private String beforePositionLevel;

    /**
     * 调薪后职级
     */
    private String afterPositionLevel;

    /**
     * 调薪前部门
     */
    private String beforeDepartment;

    /**
     * 调薪后部门
     */
    private String afterDepartment;


    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     * 1-正常 2-删除
     */
    private Integer state;

    // ========== 便利方法 ==========

    /**
     * 获取调薪类型名称
     */
    public String getAdjustmentTypeName() {
        if (adjustmentType == null) return null;
        String[] types = {"", "入职定薪", "转正调薪", "晋升调薪", "年度调薪", "绩效调薪", "市场调薪", "特殊调薪", "降薪", "其他"};
        return adjustmentType > 0 && adjustmentType < types.length ? types[adjustmentType] : null;
    }


    /**
     * 是否为加薪
     */
    public boolean isSalaryIncrease() {
        return monthlyAdjustmentAmount != null && monthlyAdjustmentAmount > 0;
    }

    /**
     * 是否为降薪
     */
    public boolean isSalaryDecrease() {
        return monthlyAdjustmentAmount != null && monthlyAdjustmentAmount < 0;
    }

    /**
     * 获取调薪幅度描述
     */
    public String getAdjustmentDescription() {
        if (monthlyAdjustmentAmount == null || monthlyAdjustmentPercentage == null) {
            return null;
        }
        
        String direction = monthlyAdjustmentAmount > 0 ? "上调" : "下调";
        return String.format("%s %d元/月 (%.1f%%)", 
                           direction, 
                           Math.abs(monthlyAdjustmentAmount), 
                           monthlyAdjustmentPercentage.abs());
    }

    /**
     * 是否已生效
     */
    public boolean isEffective() {
        if (effectiveDate == null) return false;
        
        try {
            java.time.LocalDate effective = java.time.LocalDate.parse(effectiveDate);
            return !effective.isAfter(java.time.LocalDate.now());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 是否为大幅调薪（超过20%）
     */
    public boolean isSignificantAdjustment() {
        return monthlyAdjustmentPercentage != null && 
               monthlyAdjustmentPercentage.abs().compareTo(new BigDecimal("20")) > 0;
    }

    /**
     * 获取调薪摘要信息
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getAdjustmentTypeName());
        
        String adjustmentDesc = getAdjustmentDescription();
        if (adjustmentDesc != null) {
            summary.append(" - ").append(adjustmentDesc);
        }
        
        if (effectiveDate != null) {
            summary.append(" (").append(effectiveDate).append("生效)");
        }
        
        return summary.toString();
    }
}

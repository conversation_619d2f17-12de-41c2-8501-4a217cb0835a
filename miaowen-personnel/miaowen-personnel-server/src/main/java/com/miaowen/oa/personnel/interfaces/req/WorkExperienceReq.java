package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 工作经历请求体
 * 根据最新的entity结构优化，支持更丰富的工作经历信息管理
 *
 * 优化内容：
 * 1. 根据OaArchiveWorkExperienceEntity的字段结构进行适配
 * 2. 增加公司规模、性质的枚举类型支持
 * 3. 增加职位类别、薪资结构等详细信息
 * 4. 支持JSON格式的复杂数据存储（成就、项目经验、技能等）
 * 5. 增加验证信息、附件信息等扩展字段
 *
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "工作经历信息")
public class WorkExperienceReq {

    @Schema(description = "工作经历ID", example = "1")
    private Long id;

    // ========== 时间信息 ==========

    @Schema(description = "入职时间", example = "2018-03", required = true)
    @NotBlank(message = "入职时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "入职时间格式应为YYYY-MM")
    private String startDate;

    @Schema(description = "离职时间", example = "2023-12")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "离职时间格式应为YYYY-MM")
    private String endDate;

    @Schema(description = "是否在职", example = "false")
    private Boolean isCurrentJob;

    @Schema(description = "工作时长（月）", example = "68")
    @Min(value = 0, message = "工作时长不能小于0")
    private Integer workDurationMonths;



    // ========== 公司基本信息 ==========

    @Schema(description = "公司名称", example = "阿里巴巴集团", required = true)
    @NotBlank(message = "公司名称不能为空")
    @Size(max = 100, message = "公司名称长度不能超过100个字符")
    private String companyName;


    // ========== 职位信息 ==========

    @Schema(description = "职务名称", example = "高级Java开发工程师", required = true)
    @NotBlank(message = "职务名称不能为空")
    @Size(max = 50, message = "职务名称长度不能超过50个字符")
    private String position;


    // ========== 证明人信息 ==========


    @Schema(description = "证明人姓名", example = "李主管")
    @Size(max = 50, message = "证明人姓名长度不能超过50个字符")
    private String referenceName;


    @Schema(description = "证明人联系方式", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "证明人联系方式格式不正确")
    private String referencePhone;



    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sort;

    // ========== 便利方法 ==========

    /**
     * 计算工作时长（月）
     */
    public Integer getWorkDurationMonths() {
        if (startDate == null) {
            return null;
        }
        
        String endDateToUse = endDate;
        if (Boolean.TRUE.equals(isCurrentJob) || endDateToUse == null) {
            // 如果是在职或没有结束时间，使用当前时间
            java.time.LocalDate now = java.time.LocalDate.now();
            endDateToUse = String.format("%04d-%02d", now.getYear(), now.getMonthValue());
        }
        
        try {
            String[] startParts = startDate.split("-");
            String[] endParts = endDateToUse.split("-");
            
            int startYear = Integer.parseInt(startParts[0]);
            int startMonth = Integer.parseInt(startParts[1]);
            int endYear = Integer.parseInt(endParts[0]);
            int endMonth = Integer.parseInt(endParts[1]);
            
            return (endYear - startYear) * 12 + (endMonth - startMonth);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取工作时长描述
     */
    public String getWorkDurationDescription() {
        Integer months = getWorkDurationMonths();
        if (months == null || months <= 0) {
            return null;
        }
        
        int years = months / 12;
        int remainingMonths = months % 12;
        
        if (years > 0 && remainingMonths > 0) {
            return years + "年" + remainingMonths + "个月";
        } else if (years > 0) {
            return years + "年";
        } else {
            return remainingMonths + "个月";
        }
    }


    /**
     * 是否为长期工作（超过2年）
     */
    public boolean isLongTermJob() {
        Integer months = getWorkDurationMonths();
        return months != null && months >= 24;
    }

    /**
     * 是否为短期工作（少于6个月）
     */
    public boolean isShortTermJob() {
        Integer months = getWorkDurationMonths();
        return months != null && months < 6;
    }

    /**
     * 验证工作经历信息完整性
     */
    public boolean isComplete() {
        return companyName != null && !companyName.trim().isEmpty() &&
               position != null && !position.trim().isEmpty() &&
               startDate != null && !startDate.trim().isEmpty() &&
               (Boolean.TRUE.equals(isCurrentJob) || 
                (endDate != null && !endDate.trim().isEmpty()));
    }

    /**
     * 验证时间逻辑是否正确
     */
    public boolean isDateLogicValid() {
        if (startDate == null) {
            return false;
        }
        
        // 如果是在职，不需要验证结束时间
        if (Boolean.TRUE.equals(isCurrentJob)) {
            return true;
        }
        
        if (endDate == null) {
            return false;
        }
        
        try {
            String[] startParts = startDate.split("-");
            String[] endParts = endDate.split("-");
            
            int startYear = Integer.parseInt(startParts[0]);
            int startMonth = Integer.parseInt(startParts[1]);
            int endYear = Integer.parseInt(endParts[0]);
            int endMonth = Integer.parseInt(endParts[1]);
            
            // 结束时间应该晚于或等于开始时间
            return endYear > startYear || (endYear == startYear && endMonth >= startMonth);
        } catch (Exception e) {
            return false;
        }
    }



    /**
     * 获取工作经历摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(companyName).append(" - ").append(position);
        
        String duration = getWorkDurationDescription();
        if (duration != null) {
            summary.append(" (").append(duration).append(")");
        }
        
        return summary.toString();
    }
}

package com.miaowen.oa.personnel.interfaces.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 薪资调整DTO
 * 
 * 设计理念：
 * 1. 薪资调整的复杂性考虑：
 *    - 薪资调整涉及多个维度：基本薪资、岗位薪资、绩效薪资等
 *    - 需要记录调整前后的完整状态，便于审计和回滚
 *    - 调整原因和依据需要详细记录，满足合规要求
 *    - 参考：《企业薪酬管理制度设计指南》
 * 
 * 2. 金额精度处理：
 *    - 薪资金额使用Integer而非BigDecimal的权衡：
 *      优点：JSON序列化简洁，数据库存储高效，前端处理方便
 *      缺点：无法处理小数薪资（如按小时计费）
 *      选择原因：大多数企业薪资以元为最小单位，Integer足够满足需求
 *    - 调整比例使用BigDecimal确保精度：
 *      薪资调整比例通常有小数位，如3.5%的调薪
 *      BigDecimal避免浮点数计算误差
 * 
 * 3. 审批流程设计：
 *    - 采用状态机模式管理审批流程
 *    - 支持多级审批和并行审批
 *    - 记录每个审批节点的详细信息
 *    - 参考：工作流引擎设计模式
 * 
 * 4. 数据一致性保障：
 *    - 调整前后金额的一致性校验
 *    - 调整比例与金额差值的匹配校验
 *    - 生效日期与申请日期的逻辑校验
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "薪资调整信息")
public class SalaryAdjustmentResp {

    @Schema(description = "调薪ID", example = "1")
    private Long id;

    @Schema(description = "档案ID", example = "1", required = true)
    @NotNull(message = "档案ID不能为空")
    private Long archiveId;

    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    /**
     * 调薪编号设计说明：
     * 1. 编号格式选择：SALARY-YYYY-NNNNNN
     *    - SALARY前缀明确标识调薪记录
     *    - YYYY年份便于按年度统计和归档
     *    - NNNNNN六位序号支持每年最多999999次调薪
     * 2. 与合同编号保持一致的格式规范
     * 3. 便于系统集成和数据追踪
     */
    @Schema(description = "调薪编号", example = "SALARY-2025-000001", required = true)
    @NotBlank(message = "调薪编号不能为空")
    @Pattern(regexp = "^SALARY-\\d{4}-\\d{6}$", message = "调薪编号格式不正确，应为SALARY-YYYY-NNNNNN")
    @Size(max = 50, message = "调薪编号长度不能超过50个字符")
    private String adjustmentNumber;

    /**
     * 调薪类型枚举设计：
     * 1. 类型分类依据：
     *    - 按触发原因分类：入职、转正、晋升、年度、绩效等
     *    - 覆盖企业常见的调薪场景
     *    - 便于统计分析不同类型调薪的分布
     * 2. 枚举值范围1-9的考虑：
     *    - 预留扩展空间，避免频繁的结构变更
     *    - 数字枚举便于数据库索引和查询优化
     * 3. 可能的tricky问题：
     *    - 同一员工可能同时存在多种调薪类型（如晋升+绩效）
     *    - 需要在业务层处理调薪类型的优先级和合并逻辑
     */
    @Schema(description = "调薪类型", example = "4", 
            allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8", "9"},
            required = true)
    @NotNull(message = "调薪类型不能为空")
    @Min(value = 1, message = "调薪类型代码不能小于1")
    @Max(value = 9, message = "调薪类型代码不能大于9")
    private Integer adjustmentType;

    @Schema(description = "调薪原因", example = "年度绩效考核优秀，予以调薪激励", required = true)
    @NotBlank(message = "调薪原因不能为空")
    @Size(max = 500, message = "调薪原因长度不能超过500个字符")
    private String adjustmentReason;

    /**
     * 生效日期设计说明：
     * 1. 与申请日期分离的原因：
     *    - 调薪申请可能提前提交，但从特定日期开始生效
     *    - 便于薪资核算系统按生效日期计算薪资
     *    - 支持批量调薪在同一日期生效的场景
     * 2. 日期格式统一使用ISO 8601标准
     * 3. 业务规则：生效日期不能早于申请日期
     */
    @Schema(description = "生效日期", example = "2025-02-01", required = true)
    @NotBlank(message = "生效日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "生效日期格式应为YYYY-MM-DD")
    private String effectiveDate;

    @Schema(description = "申请日期", example = "2025-01-15")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "申请日期格式应为YYYY-MM-DD")
    private String applicationDate;

    @Schema(description = "申请人ID", example = "2001")
    private Long applicantId;

    @Schema(description = "申请人姓名", example = "张经理")
    @Size(max = 50, message = "申请人姓名长度不能超过50个字符")
    private String applicantName;

    // ========== 调薪前薪资结构 ==========

    /**
     * 薪资结构设计说明：
     * 1. 分项记录的必要性：
     *    - 便于分析不同薪资组成部分的调整情况
     *    - 支持灵活的薪资结构调整策略
     *    - 满足财务核算的详细要求
     * 2. 调薪前后完整记录的原因：
     *    - 审计要求：需要完整的变更轨迹
     *    - 回滚需要：支持调薪决策的撤销
     *    - 分析需要：对比调薪前后的差异
     * 3. 金额范围设置：
     *    - 最小值0元：某些薪资组成部分可能为0
     *    - 最大值10,000,000元：覆盖高管薪资范围
     */
    @Schema(description = "调薪前基本薪资（元/月）", example = "8000")
    @Min(value = 0, message = "调薪前基本薪资不能小于0元")
    @Max(value = 10000000, message = "调薪前基本薪资不能超过10,000,000元")
    private Integer beforeBasicSalary;

    @Schema(description = "调薪前岗位薪资（元/月）", example = "2000")
    @Min(value = 0, message = "调薪前岗位薪资不能小于0元")
    @Max(value = 10000000, message = "调薪前岗位薪资不能超过10,000,000元")
    private Integer beforePositionSalary;

    @Schema(description = "调薪前绩效薪资（元/月）", example = "3000")
    @Min(value = 0, message = "调薪前绩效薪资不能小于0元")
    @Max(value = 10000000, message = "调薪前绩效薪资不能超过10,000,000元")
    private Integer beforePerformanceSalary;

    @Schema(description = "调薪前津贴补贴（元/月）", example = "500")
    @Min(value = 0, message = "调薪前津贴补贴不能小于0元")
    @Max(value = 10000000, message = "调薪前津贴补贴不能超过10,000,000元")
    private Integer beforeAllowance;

    @Schema(description = "调薪前月薪总额（元）", example = "13500")
    @Min(value = 0, message = "调薪前月薪总额不能小于0元")
    @Max(value = 50000000, message = "调薪前月薪总额不能超过50,000,000元")
    private Integer beforeTotalMonthlySalary;

    // ========== 调薪后薪资结构 ==========

    @Schema(description = "调薪后基本薪资（元/月）", example = "9000", required = true)
    @NotNull(message = "调薪后基本薪资不能为空")
    @Min(value = 1, message = "调薪后基本薪资不能小于1元")
    @Max(value = 10000000, message = "调薪后基本薪资不能超过10,000,000元")
    private Integer afterBasicSalary;

    @Schema(description = "调薪后岗位薪资（元/月）", example = "2500")
    @Min(value = 0, message = "调薪后岗位薪资不能小于0元")
    @Max(value = 10000000, message = "调薪后岗位薪资不能超过10,000,000元")
    private Integer afterPositionSalary;

    @Schema(description = "调薪后绩效薪资（元/月）", example = "3500")
    @Min(value = 0, message = "调薪后绩效薪资不能小于0元")
    @Max(value = 10000000, message = "调薪后绩效薪资不能超过10,000,000元")
    private Integer afterPerformanceSalary;

    @Schema(description = "调薪后津贴补贴（元/月）", example = "500")
    @Min(value = 0, message = "调薪后津贴补贴不能小于0元")
    @Max(value = 10000000, message = "调薪后津贴补贴不能超过10,000,000元")
    private Integer afterAllowance;

    @Schema(description = "调薪后月薪总额（元）", example = "15500", required = true)
    @NotNull(message = "调薪后月薪总额不能为空")
    @Min(value = 1, message = "调薪后月薪总额不能小于1元")
    @Max(value = 50000000, message = "调薪后月薪总额不能超过50,000,000元")
    private Integer afterTotalMonthlySalary;

    // ========== 调薪幅度计算 ==========

    /**
     * 调薪幅度字段设计：
     * 1. 金额和比例双重记录的原因：
     *    - 金额：直观显示薪资变化的绝对值
     *    - 比例：便于不同薪资水平员工的横向对比
     *    - 两者结合提供完整的调薪信息
     * 2. 负值处理：
     *    - 支持降薪场景，调整金额可能为负数
     *    - 使用Integer而非@Positive注解
     * 3. 比例精度设置：
     *    - 使用BigDecimal确保计算精度
     *    - scale=2支持百分比的两位小数精度
     *    - 范围-100%到+1000%覆盖极端调薪场景
     */
    @Schema(description = "月薪调整金额（元）", example = "2000")
    @Min(value = -50000000, message = "月薪调整金额不能小于-50,000,000元")
    @Max(value = 50000000, message = "月薪调整金额不能超过50,000,000元")
    private Integer monthlyAdjustmentAmount;

    @Schema(description = "月薪调整比例（百分比）", example = "14.81")
    @DecimalMin(value = "-100.00", message = "月薪调整比例不能小于-100%")
    @DecimalMax(value = "1000.00", message = "月薪调整比例不能超过1000%")
    @Digits(integer = 4, fraction = 2, message = "月薪调整比例最多4位整数2位小数")
    private BigDecimal monthlyAdjustmentPercentage;

    // ========== 职位信息变更 ==========

    /**
     * 职位信息字段说明：
     * 1. 调薪与职位变更的关联：
     *    - 晋升调薪通常伴随职位变更
     *    - 记录职位变更便于人事管理和统计分析
     *    - 支持纯薪资调整（职位不变）的场景
     * 2. 字段长度设置：
     *    - 职位名称100字符：覆盖复杂的职位描述
     *    - 职级20字符：如P6、M3、VP等简短标识
     *    - 部门名称100字符：支持多级部门结构
     */
    @Schema(description = "调薪前职位", example = "Java开发工程师")
    @Size(max = 100, message = "调薪前职位长度不能超过100个字符")
    private String beforePosition;

    @Schema(description = "调薪后职位", example = "高级Java开发工程师")
    @Size(max = 100, message = "调薪后职位长度不能超过100个字符")
    private String afterPosition;

    @Schema(description = "调薪前职级", example = "P5")
    @Size(max = 20, message = "调薪前职级长度不能超过20个字符")
    private String beforePositionLevel;

    @Schema(description = "调薪后职级", example = "P6")
    @Size(max = 20, message = "调薪后职级长度不能超过20个字符")
    private String afterPositionLevel;

    @Schema(description = "调薪前部门", example = "技术研发部")
    @Size(max = 100, message = "调薪前部门长度不能超过100个字符")
    private String beforeDepartment;

    @Schema(description = "调薪后部门", example = "技术研发部")
    @Size(max = 100, message = "调薪后部门长度不能超过100个字符")
    private String afterDepartment;

    // ========== 绩效信息 ==========

    /**
     * 绩效关联字段设计：
     * 1. 与绩效系统的集成考虑：
     *    - performanceEvaluationId关联具体的绩效考核记录
     *    - 支持基于绩效结果的自动调薪建议
     *    - 便于分析绩效与薪资调整的相关性
     * 2. 绩效评级标准化：
     *    - 1-5级评级覆盖常见的绩效管理体系
     *    - 数字化便于统计分析和系统处理
     * 3. 绩效得分和排名：
     *    - 支持更精细的绩效量化管理
     *    - 便于实施相对绩效管理制度
     */
    @Schema(description = "关联绩效考核ID", example = "1001")
    private Long performanceEvaluationId;

    @Schema(description = "绩效考核期间", example = "2024年度")
    @Size(max = 50, message = "绩效考核期间长度不能超过50个字符")
    private String performancePeriod;

    @Schema(description = "绩效评级", example = "2", allowableValues = {"1", "2", "3", "4", "5"})
    @Min(value = 1, message = "绩效评级不能小于1")
    @Max(value = 5, message = "绩效评级不能大于5")
    private Integer performanceRating;

    @Schema(description = "绩效得分", example = "85.5")
    @DecimalMin(value = "0.0", message = "绩效得分不能小于0")
    @DecimalMax(value = "100.0", message = "绩效得分不能超过100")
    @Digits(integer = 3, fraction = 2, message = "绩效得分最多3位整数2位小数")
    private BigDecimal performanceScore;

    // ========== 审批信息 ==========

    /**
     * 审批流程字段设计：
     * 1. 审批状态枚举：
     *    - 1-待审批：初始状态，等待审批人处理
     *    - 2-审批中：多级审批场景下的中间状态
     *    - 3-审批通过：所有审批节点都通过
     *    - 4-审批拒绝：任一审批节点拒绝
     *    - 5-已撤回：申请人主动撤回申请
     * 2. 审批流程ID设计：
     *    - 关联工作流引擎的流程实例
     *    - 支持复杂的审批路由和条件判断
     *    - 便于审批过程的追踪和管理
     * 3. 当前审批人信息：
     *    - 支持审批任务的精确定位
     *    - 便于发送审批提醒和催办
     */
    @Schema(description = "审批状态", example = "1", allowableValues = {"1", "2", "3", "4", "5"})
    @Min(value = 1, message = "审批状态代码不能小于1")
    @Max(value = 5, message = "审批状态代码不能大于5")
    private Integer approvalStatus;

    @Schema(description = "审批流程ID", example = "WF-SALARY-2025-000001")
    @Size(max = 100, message = "审批流程ID长度不能超过100个字符")
    private String approvalProcessId;

    @Schema(description = "当前审批人ID", example = "3001")
    private Long currentApproverId;

    @Schema(description = "当前审批人姓名", example = "李总监")
    @Size(max = 50, message = "当前审批人姓名长度不能超过50个字符")
    private String currentApproverName;

    @Schema(description = "审批意见", example = "员工表现优秀，同意调薪")
    @Size(max = 1000, message = "审批意见长度不能超过1000个字符")
    private String approvalComment;

    // ========== 执行信息 ==========

    /**
     * 执行状态设计：
     * 1. 审批与执行分离的原因：
     *    - 审批通过不等于立即执行，可能需要等待特定时间
     *    - 执行过程可能涉及多个系统（薪资系统、财务系统等）
     *    - 支持批量执行以提高操作效率
     * 2. 执行状态枚举：
     *    - 1-待执行：审批通过但尚未执行
     *    - 2-执行中：正在执行调薪操作
     *    - 3-已执行：调薪操作完成
     *    - 4-执行失败：执行过程中出现错误
     * 3. 执行信息记录：
     *    - 执行时间：实际调薪生效的时间点
     *    - 执行人：操作人员信息，便于问题追踪
     *    - 执行说明：记录执行过程中的特殊情况
     */
    @Schema(description = "执行状态", example = "1", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "执行状态代码不能小于1")
    @Max(value = 4, message = "执行状态代码不能大于4")
    private Integer executionStatus;

    @Schema(description = "执行时间", example = "2025-02-01 09:00:00")
    private String executionTime;

    @Schema(description = "执行人ID", example = "4001")
    private Long executorId;

    @Schema(description = "执行人姓名", example = "王专员")
    @Size(max = 50, message = "执行人姓名长度不能超过50个字符")
    private String executorName;

    @Schema(description = "备注", example = "特殊调薪情况说明")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remarks;

    // ========== 便利方法 ==========

    /**
     * 获取调薪类型名称
     * 
     * 实现说明：
     * 1. 枚举映射数组的设计考虑：
     *    - 索引0为空字符串，对应数据库中不存在的0值
     *    - 数组长度与最大枚举值+1保持一致
     *    - 提供默认值"其他"处理未知类型
     * 2. 类型分类的业务含义：
     *    - 入职定薪：新员工入职时的薪资确定
     *    - 转正调薪：试用期结束转正时的薪资调整
     *    - 晋升调薪：职位晋升带来的薪资提升
     *    - 年度调薪：基于年度绩效的薪资调整
     *    - 绩效调薪：基于绩效考核结果的调薪
     *    - 市场调薪：基于市场薪资水平的调整
     *    - 特殊调薪：其他特殊情况的调薪
     *    - 降薪：因各种原因的薪资下调
     */
    public String getAdjustmentTypeName() {
        if (adjustmentType == null) return null;
        String[] types = {"", "入职定薪", "转正调薪", "晋升调薪", "年度调薪", "绩效调薪", "市场调薪", "特殊调薪", "降薪", "其他"};
        return adjustmentType > 0 && adjustmentType < types.length ? types[adjustmentType] : "未知类型";
    }

    /**
     * 判断是否为加薪
     * 
     * 业务逻辑说明：
     * 1. 基于调整金额而非调整比例的原因：
     *    - 调整金额是最直观的判断标准
     *    - 避免基数为0时比例计算的异常情况
     * 2. 边界条件处理：
     *    - null值返回false，避免空指针异常
     *    - 0值被视为无调整，不是加薪
     */
    public boolean isSalaryIncrease() {
        return monthlyAdjustmentAmount != null && monthlyAdjustmentAmount > 0;
    }

    /**
     * 判断是否为降薪
     */
    public boolean isSalaryDecrease() {
        return monthlyAdjustmentAmount != null && monthlyAdjustmentAmount < 0;
    }

    /**
     * 获取调薪幅度描述
     * 
     * 实现说明：
     * 1. 格式化输出的设计：
     *    - 同时显示绝对金额和相对比例
     *    - 使用"上调"/"下调"而非"增加"/"减少"更专业
     *    - Math.abs()确保显示正数，方向通过文字表达
     * 2. 精度控制：
     *    - 金额显示整数，符合薪资管理习惯
     *    - 比例显示一位小数，平衡精度和可读性
     * 3. 异常处理：
     *    - 任一关键字段为null时返回null
     *    - 调用方需要处理null返回值
     */
    public String getAdjustmentDescription() {
        if (monthlyAdjustmentAmount == null || monthlyAdjustmentPercentage == null) {
            return null;
        }
        
        String direction = monthlyAdjustmentAmount > 0 ? "上调" : "下调";
        return String.format("%s %d元/月 (%.1f%%)", 
                           direction, 
                           Math.abs(monthlyAdjustmentAmount), 
                           monthlyAdjustmentPercentage.abs());
    }

    /**
     * 判断是否已生效
     * 
     * 实现说明：
     * 1. 使用LocalDate而非Date的原因：
     *    - 薪资调整通常以日期为准，不涉及具体时间
     *    - LocalDate比较性能更好，代码更简洁
     * 2. 生效判断逻辑：
     *    - 生效日期<=当前日期即为已生效
     *    - 使用!isAfter()而非isBefore()或isEqual()的组合更简洁
     * 3. 时区考虑：
     *    - LocalDate.now()使用系统默认时区
     *    - 如果系统跨时区部署，需要考虑统一时区处理
     */
    public boolean isEffective() {
        if (effectiveDate == null) return false;
        
        try {
            java.time.LocalDate effective = java.time.LocalDate.parse(effectiveDate);
            return !effective.isAfter(java.time.LocalDate.now());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断是否为大幅调薪
     * 
     * 业务规则说明：
     * 1. 20%阈值的选择依据：
     *    - 参考行业惯例，20%以上调薪属于大幅调整
     *    - 大幅调薪通常需要更高级别的审批
     *    - 可以通过配置文件调整阈值，提高灵活性
     * 2. 使用绝对值的原因：
     *    - 大幅降薪同样需要特殊关注
     *    - 统一处理加薪和降薪的判断逻辑
     * 3. BigDecimal比较的注意事项：
     *    - 使用compareTo()而非equals()进行数值比较
     *    - compareTo()返回值>0表示当前值大于比较值
     */
    public boolean isSignificantAdjustment() {
        return monthlyAdjustmentPercentage != null && 
               monthlyAdjustmentPercentage.abs().compareTo(new BigDecimal("20.00")) > 0;
    }

    /**
     * 获取调薪摘要信息
     * 
     * 实现说明：
     * 1. 信息优先级设计：
     *    - 调薪类型：最重要的分类信息
     *    - 调薪幅度：最关心的数值信息
     *    - 生效日期：最重要的时间信息
     * 2. 格式化策略：
     *    - 使用短横线分隔不同类型的信息
     *    - 生效日期用括号包围，表示补充信息
     * 3. 容错处理：
     *    - 各部分信息缺失时不影响其他信息显示
     *    - 确保始终返回有意义的字符串
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        
        String typeName = getAdjustmentTypeName();
        if (typeName != null) {
            summary.append(typeName);
        }
        
        String adjustmentDesc = getAdjustmentDescription();
        if (adjustmentDesc != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(adjustmentDesc);
        }
        
        if (effectiveDate != null) {
            if (summary.length() > 0) summary.append(" ");
            summary.append("(").append(effectiveDate).append("生效)");
        }
        
        return summary.length() > 0 ? summary.toString() : "薪资调整";
    }
}

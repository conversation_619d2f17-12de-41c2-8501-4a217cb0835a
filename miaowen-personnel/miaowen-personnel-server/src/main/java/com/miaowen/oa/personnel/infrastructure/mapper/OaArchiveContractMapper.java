package com.miaowen.oa.personnel.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveContractEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 档案合同信息Mapper接口
 *
 * <AUTHOR>
 * @company 武汉市妙闻网络科技有限公司
 * @since 2025-07-28
 */
@Mapper
public interface OaArchiveContractMapper extends BaseMapper<OaArchiveContractEntity> {

    /**
     * 根据档案ID查询合同列表
     *
     * @param archiveId 档案ID
     * @return 合同列表
     */
    List<OaArchiveContractEntity> selectByArchiveId(@Param("archiveId") Long archiveId);

    /**
     * 根据用户ID查询合同列表
     *
     * @param userId 用户ID
     * @return 合同列表
     */
    List<OaArchiveContractEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 分页查询合同
     *
     * @param page       分页对象
     * @param conditions 查询条件
     * @return 分页结果
     */
    IPage<OaArchiveContractEntity> selectPageByConditions(IPage<OaArchiveContractEntity> page, @Param("conditions") Map<String, Object> conditions);

    /**
     * 查询即将到期的合同
     *
     * @param days 提前天数
     * @return 即将到期的合同列表
     */
    List<OaArchiveContractEntity> selectExpiringContracts(@Param("days") Integer days);

    /**
     * 查询已过期的合同
     *
     * @return 已过期的合同列表
     */
    List<OaArchiveContractEntity> selectExpiredContracts();

    /**
     * 查询当前有效合同
     *
     * @param archiveId 档案ID
     * @return 当前有效合同
     */
    OaArchiveContractEntity selectCurrentContract(@Param("archiveId") Long archiveId);

    /**
     * 合同类型统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectContractTypeStatistics();

    /**
     * 合同状态统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectContractStatusStatistics();

    /**
     * 合同期限分析
     *
     * @return 分析结果
     */
    Map<String, Object> selectContractDurationAnalysis();

    /**
     * 批量插入合同
     *
     * @param list 合同列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<OaArchiveContractEntity> list);

    /**
     * 软删除合同
     *
     * @param id 合同ID
     * @return 删除数量
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 批量软删除合同
     *
     * @param ids 合同ID列表
     * @return 删除数量
     */
    int batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 更新合同状态
     *
     * @param id     合同ID
     * @param status 新状态
     * @return 更新数量
     */
    int updateContractStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 查询合同续签历史
     *
     * @param originalContractId 原合同ID
     * @return 续签历史
     */
    List<OaArchiveContractEntity> selectRenewalHistory(@Param("originalContractId") Long originalContractId);

    /**
     * 查询薪资变更历史
     *
     * @param archiveId 档案ID
     * @return 薪资变更历史
     */
    List<Map<String, Object>> selectSalaryChangeHistory(@Param("archiveId") Long archiveId);

    /**
     * 按月统计合同签订数量
     *
     * @param year 年份
     * @return 月度统计
     */
    List<Map<String, Object>> selectMonthlyContractStatistics(@Param("year") Integer year);

    /**
     * 查询合同模板使用统计
     *
     * @return 模板使用统计
     */
    List<Map<String, Object>> selectContractTemplateStatistics();

    /**
     * 查询部门合同分布
     *
     * @return 部门分布统计
     */
    List<Map<String, Object>> selectDepartmentContractDistribution();

    /**
     * 查询合同金额统计
     *
     * @return 金额统计
     */
    Map<String, Object> selectContractAmountStatistics();

    /**
     * 查询试用期合同
     *
     * @return 试用期合同列表
     */
    List<OaArchiveContractEntity> selectProbationContracts();

    /**
     * 查询正式合同
     *
     * @return 正式合同列表
     */
    List<OaArchiveContractEntity> selectFormalContracts();

    /**
     * 根据合同编号查询
     *
     * @param contractNumber 合同编号
     * @return 合同信息
     */
    OaArchiveContractEntity selectByContractNumber(@Param("contractNumber") String contractNumber);

    /**
     * 查询需要续签提醒的合同
     *
     * @param reminderDays 提醒天数
     * @return 需要提醒的合同列表
     */
    List<OaArchiveContractEntity> selectContractsNeedingRenewalReminder(@Param("reminderDays") Integer reminderDays);

    /**
     * 更新合同续签信息
     *
     * @param id                合同ID
     * @param renewalContractId 续签合同ID
     * @param renewalDate       续签日期
     * @return 更新数量
     */
    int updateRenewalInfo(@Param("id") Long id, @Param("renewalContractId") Long renewalContractId, @Param("renewalDate") LocalDate renewalDate);

    /**
     * 查询合同变更记录
     *
     * @param contractId 合同ID
     * @return 变更记录
     */
    List<Map<String, Object>> selectContractChangeRecords(@Param("contractId") Long contractId);
}

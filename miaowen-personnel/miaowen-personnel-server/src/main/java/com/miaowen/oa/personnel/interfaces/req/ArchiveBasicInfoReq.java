package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 档案基本信息DTO
 *
 * 设计理念：
 * 1. DTO职责边界定义：
 *    - 作为Controller层的数据传输对象，专注于API接口的数据结构定义
 *    - 不包含业务逻辑，只包含数据验证和基本的数据转换方法
 *    - 与Entity分离，避免数据库结构变更直接影响API接口
 *    - 参考：《领域驱动设计》- Eric Evans
 *
 * 2. 字段选择原则：
 *    - 只包含档案的核心基础信息，避免DTO过于庞大
 *    - 详细信息通过专门的子模块DTO处理（教育经历、工作经历等）
 *    - 敏感信息需要根据用户权限动态控制是否返回
 *
 * 3. 验证注解的选择依据：
 *    - @NotNull: 字段不能为null，但可以为空字符串或空集合
 *    - @NotBlank: 字符串不能为null、空字符串或只包含空白字符
 *    - @NotEmpty: 集合、数组、字符串不能为null且长度大于0
 *    - @Size: 限制字符串长度或集合大小，防止恶意输入
 *    - @Min/@Max: 限制数值范围，确保业务逻辑的合理性
 *    - @Pattern: 正则表达式验证，确保格式的正确性
 *
 * 4. Schema注解的作用：
 *    - 为Swagger/OpenAPI文档生成提供元数据
 *    - 提供字段描述、示例值、是否必需等信息
 *    - 便于前端开发人员理解接口结构
 *    - 支持API文档的自动化生成和维护
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "档案基本信息")
public class ArchiveBasicInfoReq {

    @Schema(description = "档案ID", example = "1")
    private Long id;

    /**
     * 用户ID字段设计说明：
     * 1. 作为外键关联system模块的用户表
     * 2. 必填字段，确保每个档案都有明确的归属
     * 3. 在创建时必须验证用户是否存在
     * 4. 支持一个用户对应一个档案的业务模型
     */
    @Schema(description = "用户ID", example = "1001", required = true)
    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID必须大于0")
    private Long userId;

    @Schema(description = "性别", example = "女", required = true)
    private Integer gender;

    @Schema(description = "个人邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号码", example = "<EMAIL>")
    private String phone;

    @Schema(description = "出生日期", example = "1990-01-01")
    private String birthday;

    /**
     * 政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众
     */
    @Schema(description = "政治面貌：1-中共党员 2-中共预备党员 3-共青团员 4-民主党派 5-无党派人士 6-群众", example = "5")
    private Integer politicalState;


    /**
     * 民族
     */
    @Schema(description = "民族", example = "5")
    private String ethnicity;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号", example = "5")
    private String identity;


    /**
     * 籍贯
     */
    @Schema(description = "籍贯", example = "5")
    private String nativePlace;
    /**
     * 婚姻状况设计说明：
     * 1-未婚，2-已婚，3-离异，4-丧偶
     */
    @Schema(description = "婚姻状况", example = "2", allowableValues = {"1", "2", "3", "4"})
    @Min(value = 1, message = "婚姻状况代码不能小于1")
    @Max(value = 4, message = "婚姻状况代码不能大于4")
    private Integer maritalState;




    /**
     * 最高学历设计说明：
     * 1-小学，2-初中，3-高中/中专，4-大专，5-本科，6-硕士，7-博士
     */
    @Schema(description = "最高学历", example = "5", allowableValues = {"1", "2", "3", "4", "5", "6", "7"})
    @Min(value = 1, message = "最高学历代码不能小于1")
    @Max(value = 7, message = "最高学历代码不能大于7")
    private Integer highestEducation;


    /**
     * 健康状况设计说明：
     * 1-良好，2-一般，3-患病
     */
    @Schema(description = "健康状况", example = "2", allowableValues = {"1", "2", "3"})
    @Min(value = 1, message = "健康状况代码不能小于1")
    @Max(value = 3, message = "健康状况代码不能大于3")
    private Integer healthState;



    @Schema(description = "工作年限（年）", example = "5")
    @Min(value = 0, message = "工作年限不能小于0年")
    @Max(value = 50, message = "工作年限不能大于50年")
    private Integer workYears;

    @Schema(description = "户口性质", example = "1")
    private Integer householdNature;



    /**
     * 开户行
     */
    @Schema(description = "开户行", example = "5")
    private String bankName;

    /**
     * 银行卡号
     */
    @Schema(description = "银行卡号", example = "5")
    private String bankCardNumber;


    @Schema(description = "现居地址", example = "武汉市")
    private String address;


    @Schema(description = "户籍地址", example = "武汉市")
    private String hometown;


    @Schema(description = "紧急联系人姓名", example = "张三")
    @Size(max = 50, message = "紧急联系人姓名长度不能超过50个字符")
    private String emergencyContactName;

    @Schema(description = "紧急联系人电话", example = "***********")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系人电话格式不正确")
    private String emergencyContactPhone;
    /**
     * 紧急联系人关系设计说明：
     * 1-父亲，2-母亲，3-配偶，4-子女，5-兄弟姐妹，6-朋友，7-其他
     */
    @Schema(description = "紧急联系人关系", example = "3", allowableValues = {"1", "2", "3", "4", "5", "6", "7"})
    @Min(value = 1, message = "紧急联系人关系代码不能小于1")
    @Max(value = 7, message = "紧急联系人关系代码不能大于7")
    private Integer emergencyContactRelation;


    @Schema(description = "紧急联系人地址", example = "北京市朝阳区xxx街道xxx号")
    @Size(max = 200, message = "紧急联系人地址长度不能超过200个字符")
    private String emergencyContactAddress;


    /**
     * 获取紧急联系人关系名称
     */
    public String getEmergencyContactRelationName() {
        return com.miaowen.oa.personnel.domain.enums.ArchiveFieldEnum.EmergencyContactRelationEnum
                .getNameByCode(this.emergencyContactRelation);
    }



    /**
     * 是否已婚
     */
    public boolean isMarried() {
        return Integer.valueOf(2).equals(this.maritalState);
    }

    /**
     * 验证紧急联系人信息完整性
     */
    public boolean isEmergencyContactComplete() {
        return emergencyContactName != null && !emergencyContactName.trim().isEmpty() &&
               emergencyContactRelation != null &&
               emergencyContactPhone != null && !emergencyContactPhone.trim().isEmpty();
    }
}

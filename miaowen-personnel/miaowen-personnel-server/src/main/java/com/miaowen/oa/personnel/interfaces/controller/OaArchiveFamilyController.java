package com.miaowen.oa.personnel.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveEducationService;
import com.miaowen.oa.personnel.domain.service.OaArchiveFamilyMemberService;
import com.miaowen.oa.personnel.interfaces.req.EducationExperienceCreateReq;
import com.miaowen.oa.personnel.interfaces.req.EducationExperienceReq;
import com.miaowen.oa.personnel.interfaces.req.FamilyMemberCreateReq;
import com.miaowen.oa.personnel.interfaces.req.FamilyMemberReq;
import com.miaowen.oa.personnel.interfaces.resp.EducationExperienceResp;
import com.miaowen.oa.personnel.interfaces.resp.FamilyMemberResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 家庭成员管理控制器
 * RESTful API设计原则：
 * - 资源命名：/education 作为主资源路径
 * - 状态码：遵循标准HTTP状态码规范
 * - 版本控制：通过URL路径/v1实现
 * 
 * 安全设计：
 * - 所有写操作需要权限验证（由网关统一处理）
 * - 敏感操作记录审计日志
 * 
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "家庭成员管理")
@Slf4j
@Validated
@RestController
@RequestMapping("/v1/family-member")
public class OaArchiveFamilyController {

    private final OaArchiveFamilyMemberService familyMemberService;

    public OaArchiveFamilyController(OaArchiveFamilyMemberService familyMemberService) {
        this.familyMemberService = familyMemberService;
    }


    @Operation(summary = "编辑家庭成员")
    @PostMapping("/save")
    public CommonResult<Boolean> saveContract(
            @Validated @RequestBody FamilyMemberCreateReq updateReq) {
        familyMemberService.saveFamilyMember(updateReq);
        return CommonResult.success(true);
    }


    @PostMapping("")
    @Operation(summary = "创建家庭成员")
    public CommonResult<Long> createFamilyMember(@RequestBody @Validated FamilyMemberCreateReq req) {
        familyMemberService.createFamilyMember(req);
        return CommonResult.success();
    }

    @PutMapping("")
    @Operation(summary = "更新家庭成员")
    public CommonResult<Boolean> updateFamilyMember(@RequestBody @Validated FamilyMemberCreateReq req) {
        familyMemberService.updateFamilyMember(req);
        return CommonResult.success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除家庭成员")
    public CommonResult<Boolean> deleteFamilyMember(@PathVariable("id") Long id) {
        familyMemberService.deleteFamilyMember(id);
        return CommonResult.success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取家庭成员详情")
    public CommonResult<FamilyMemberResp> getFamilyMember(@PathVariable("id") Long id) {
        return CommonResult.success(familyMemberService.getFamilyMember(id));
    }

    @GetMapping("/archive/{archiveId}")
    @Operation(summary = "获取档案下的家庭成员列表")
    public CommonResult<List<FamilyMemberResp>> getFamilyMemberListByArchive(
            @PathVariable("archiveId") Long archiveId) {
        return CommonResult.success(familyMemberService.getFamilyMemberListByArchive(archiveId));
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户的家庭成员列表")
    public CommonResult<List<FamilyMemberResp>> getFamilyMemberListByUser(
            @PathVariable("userId") Long userId) {
        return CommonResult.success(familyMemberService.getFamilyMemberListByUser(userId));
    }

}
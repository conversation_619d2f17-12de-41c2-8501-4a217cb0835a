package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;

/**
 * 教育经历DTO
 * 用于存储详细的教育经历信息
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "教育经历信息")
public class EducationExperienceCreateReq {

    private List<EducationExperienceReq> list;

    @NotNull(message = "档案ID不能为空")
    @Schema(description = "档案ID", example = "1")
    private Long archiveId;

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", example = "1")
    private Long userId;
}

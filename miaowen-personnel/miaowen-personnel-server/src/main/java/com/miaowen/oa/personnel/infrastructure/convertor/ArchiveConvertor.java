package com.miaowen.oa.personnel.infrastructure.convertor;

import com.miaowen.oa.personnel.api.archive.dto.ArchiveCreateReqDTO;
import com.miaowen.oa.personnel.api.archive.dto.ArchiveRespDTO;
import com.miaowen.oa.personnel.api.archive.dto.ArchiveUpdateReqDTO;
import com.miaowen.oa.personnel.application.bo.ArchiveBO;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 档案数据转换器
 * 
 * 设计理念：
 * 1. 转换器职责：
 *    - 数据对象转换：在不同层次的数据对象间进行转换
 *    - 格式适配：适配不同层次对数据格式的要求
 *    - 字段映射：处理字段名称和类型的差异
 *    - 业务逻辑：封装转换过程中的业务逻辑
 *    - 参考：《企业应用架构模式》- 数据传输对象模式
 * 
 * 2. 转换层次：
 *    - API层 ↔ 应用层：DTO ↔ BO
 *    - 应用层 ↔ 基础设施层：BO ↔ Entity
 *    - 跨层转换：DTO ↔ Entity（特殊场景）
 *    - 批量转换：List<Source> ↔ List<Target>
 * 
 * 3. 转换原则：
 *    - 空值安全：处理null值和空集合的转换
 *    - 类型安全：确保类型转换的正确性
 *    - 性能优化：避免不必要的对象创建和复制
 *    - 业务语义：保持业务语义的一致性
 * 
 * 4. 扩展性设计：
 *    - 字段映射：支持复杂的字段映射规则
 *    - 条件转换：支持基于条件的转换逻辑
 *    - 自定义转换：支持自定义转换器的扩展
 *    - 版本兼容：支持不同版本对象的转换
 * 
 * 5. 业务增强：
 *    - 数据计算：在转换过程中计算派生字段
 *    - 格式化：对数据进行格式化处理
 *    - 验证：在转换过程中进行数据验证
 *    - 审计：记录数据转换的审计信息
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
@Component
public class ArchiveConvertor {

    // ========== API层 ↔ 应用层转换 ==========

    /**
     * 创建请求DTO转业务对象
     * 
     * 转换逻辑：
     * 1. 基础字段映射：直接映射相同名称和类型的字段
     * 2. 默认值设置：为业务对象设置合理的默认值
     * 3. 业务规则：应用创建时的业务规则
     * 4. 数据验证：验证转换后数据的正确性
     * 
     * @param createReqDTO 创建请求DTO
     * @return 档案业务对象
     */
    public ArchiveBO toBO(ArchiveCreateReqDTO createReqDTO) {
        if (Objects.isNull(createReqDTO)) {
            return null;
        }

        ArchiveBO archiveBO = new ArchiveBO();
        
        // 基础信息
        archiveBO.setUserId(createReqDTO.getUserId());
        archiveBO.setArchiveType(createReqDTO.getArchiveType());
        archiveBO.setSecurityLevel(createReqDTO.getSecurityLevel());
        
        // 入职信息
        archiveBO.setEntryDate(createReqDTO.getEntryDate());
        archiveBO.setProbationStartDate(createReqDTO.getProbationStartDate());
        archiveBO.setProbationEndDate(createReqDTO.getProbationEndDate());
        archiveBO.setRegularDate(createReqDTO.getRegularDate());
        
        // 薪资信息
        archiveBO.setEntrySalary(createReqDTO.getEntrySalary());
        archiveBO.setRegularSalary(createReqDTO.getRegularSalary());
        archiveBO.setCurrentSalary(createReqDTO.getCurrentSalary());
        
        // 教育信息
        archiveBO.setHighestEducation(createReqDTO.getHighestEducation());
        archiveBO.setHighestDegree(createReqDTO.getHighestDegree());
        archiveBO.setGraduateSchool(createReqDTO.getGraduateSchool());
        archiveBO.setMajor(createReqDTO.getMajor());

        // 工作经验
        archiveBO.setWorkYears(createReqDTO.getWorkYears());
        archiveBO.setHasWorkExperience(createReqDTO.getHasWorkExperience());
        
        // 个人信息
        archiveBO.setMaritalState(createReqDTO.getMaritalState());
        archiveBO.setChildrenCount(createReqDTO.getChildrenCount());
        
        // 紧急联系人
        archiveBO.setEmergencyContactName(createReqDTO.getEmergencyContactName());
        archiveBO.setEmergencyContactPhone(createReqDTO.getEmergencyContactPhone());
        
        // 备注信息
        archiveBO.setRemarks(createReqDTO.getRemarks());
        
        return archiveBO;
    }

    /**
     * 更新请求DTO转业务对象
     * 
     * 转换特点：
     * 1. 部分更新：只转换非空字段
     * 2. ID保留：保留原有的ID信息
     * 3. 版本控制：处理版本号信息
     * 4. 增量转换：只转换变更的字段
     * 
     * @param updateReqDTO 更新请求DTO
     * @return 档案业务对象
     */
    public ArchiveBO toBO(ArchiveUpdateReqDTO updateReqDTO) {
        if (Objects.isNull(updateReqDTO)) {
            return null;
        }

        ArchiveBO archiveBO = new ArchiveBO();
        
        // ID信息
        archiveBO.setId(updateReqDTO.getId());
        
        // 只转换非空字段（部分更新）
        if (Objects.nonNull(updateReqDTO.getArchiveType())) {
            archiveBO.setArchiveType(updateReqDTO.getArchiveType());
        }
        if (Objects.nonNull(updateReqDTO.getSecurityLevel())) {
            archiveBO.setSecurityLevel(updateReqDTO.getSecurityLevel());
        }
        if (Objects.nonNull(updateReqDTO.getEntryDate())) {
            archiveBO.setEntryDate(updateReqDTO.getEntryDate());
        }
        if (Objects.nonNull(updateReqDTO.getProbationStartDate())) {
            archiveBO.setProbationStartDate(updateReqDTO.getProbationStartDate());
        }
        if (Objects.nonNull(updateReqDTO.getProbationEndDate())) {
            archiveBO.setProbationEndDate(updateReqDTO.getProbationEndDate());
        }
        if (Objects.nonNull(updateReqDTO.getRegularDate())) {
            archiveBO.setRegularDate(updateReqDTO.getRegularDate());
        }
        if (Objects.nonNull(updateReqDTO.getEntrySalary())) {
            archiveBO.setEntrySalary(updateReqDTO.getEntrySalary());
        }
        if (Objects.nonNull(updateReqDTO.getRegularSalary())) {
            archiveBO.setRegularSalary(updateReqDTO.getRegularSalary());
        }
        if (Objects.nonNull(updateReqDTO.getCurrentSalary())) {
            archiveBO.setCurrentSalary(updateReqDTO.getCurrentSalary());
        }
        if (Objects.nonNull(updateReqDTO.getHighestEducation())) {
            archiveBO.setHighestEducation(updateReqDTO.getHighestEducation());
        }
        if (Objects.nonNull(updateReqDTO.getHighestDegree())) {
            archiveBO.setHighestDegree(updateReqDTO.getHighestDegree());
        }
        if (Objects.nonNull(updateReqDTO.getGraduateSchool())) {
            archiveBO.setGraduateSchool(updateReqDTO.getGraduateSchool());
        }
        if (Objects.nonNull(updateReqDTO.getMajor())) {
            archiveBO.setMajor(updateReqDTO.getMajor());
        }

        if (Objects.nonNull(updateReqDTO.getWorkYears())) {
            archiveBO.setWorkYears(updateReqDTO.getWorkYears());
        }
        if (Objects.nonNull(updateReqDTO.getHasWorkExperience())) {
            archiveBO.setHasWorkExperience(updateReqDTO.getHasWorkExperience());
        }
        if (Objects.nonNull(updateReqDTO.getMaritalState())) {
            archiveBO.setMaritalState(updateReqDTO.getMaritalState());
        }
        if (Objects.nonNull(updateReqDTO.getChildrenCount())) {
            archiveBO.setChildrenCount(updateReqDTO.getChildrenCount());
        }
        if (Objects.nonNull(updateReqDTO.getEmergencyContactName())) {
            archiveBO.setEmergencyContactName(updateReqDTO.getEmergencyContactName());
        }
        if (Objects.nonNull(updateReqDTO.getEmergencyContactPhone())) {
            archiveBO.setEmergencyContactPhone(updateReqDTO.getEmergencyContactPhone());
        }
        if (Objects.nonNull(updateReqDTO.getRemarks())) {
            archiveBO.setRemarks(updateReqDTO.getRemarks());
        }
        
        return archiveBO;
    }

    /**
     * 业务对象转响应DTO
     * 
     * 转换增强：
     * 1. 数据格式化：对数据进行格式化处理
     * 2. 枚举转换：将枚举值转换为可读的名称
     * 3. 计算字段：计算派生字段和业务指标
     * 4. 权限过滤：根据权限过滤敏感字段
     * 
     * @param archiveBO 档案业务对象
     * @return 档案响应DTO
     */
    public ArchiveRespDTO toRespDTO(ArchiveBO archiveBO) {
        if (Objects.isNull(archiveBO)) {
            return null;
        }

        ArchiveRespDTO respDTO = new ArchiveRespDTO();
        
        // 基础信息
        respDTO.setId(archiveBO.getId());
        respDTO.setUserId(archiveBO.getUserId());
        respDTO.setArchiveNumber(archiveBO.getArchiveNumber());
        respDTO.setArchiveState(archiveBO.getArchiveState());
        respDTO.setArchiveType(archiveBO.getArchiveType());
        respDTO.setSecurityLevel(archiveBO.getSecurityLevel());
        
        // 枚举名称转换
        respDTO.setArchiveStateName(getArchiveStateName(archiveBO.getArchiveState()));
        respDTO.setArchiveTypeName(getArchiveTypeName(archiveBO.getArchiveType()));
        respDTO.setSecurityLevelName(getSecurityLevelName(archiveBO.getSecurityLevel()));
        
        // 入职信息
        respDTO.setEntryDate(archiveBO.getEntryDate());
        respDTO.setProbationStartDate(archiveBO.getProbationStartDate());
        respDTO.setProbationEndDate(archiveBO.getProbationEndDate());
        respDTO.setRegularDate(archiveBO.getRegularDate());
        
        // 薪资信息（分转元）
        respDTO.setEntrySalary(formatSalary(archiveBO.getEntrySalary()));
        respDTO.setRegularSalary(formatSalary(archiveBO.getRegularSalary()));
        respDTO.setCurrentSalary(formatSalary(archiveBO.getCurrentSalary()));
        
        // 教育信息
        respDTO.setHighestEducation(archiveBO.getHighestEducation());
        respDTO.setHighestEducationName(getEducationName(archiveBO.getHighestEducation()));
        respDTO.setHighestDegree(archiveBO.getHighestDegree());
        respDTO.setHighestDegreeName(getDegreeName(archiveBO.getHighestDegree()));
        respDTO.setGraduateSchool(archiveBO.getGraduateSchool());
        respDTO.setMajor(archiveBO.getMajor());
        
        // 工作经验
        respDTO.setWorkYears(archiveBO.getWorkYears());
        respDTO.setHasWorkExperience(archiveBO.getHasWorkExperience());
        respDTO.setHasWorkExperienceName(getWorkExperienceName(archiveBO.getHasWorkExperience()));
        
        // 个人信息
        respDTO.setMaritalStatus(archiveBO.getMaritalState());
        respDTO.setMaritalStatusName(getMaritalStatusName(archiveBO.getMaritalState()));
        respDTO.setChildrenCount(archiveBO.getChildrenCount());
        
        // 紧急联系人
        respDTO.setEmergencyContactName(archiveBO.getEmergencyContactName());
        respDTO.setEmergencyContactPhone(archiveBO.getEmergencyContactPhone());
        
        // 备注信息
        respDTO.setRemarks(archiveBO.getRemarks());
        
        // 审计信息
        respDTO.setCreator(archiveBO.getCreator());
        respDTO.setCreateTime(archiveBO.getCreateTime());
        respDTO.setUpdater(archiveBO.getUpdater());
        respDTO.setUpdateTime(archiveBO.getUpdateTime());
        
        // 计算字段
        respDTO.setCompleteness(archiveBO.getCompleteness());
        respDTO.setEditable(isEditable(archiveBO));
        respDTO.setDeletable(isDeletable(archiveBO));
        respDTO.setAvailableActions(archiveBO.getAvailableActions());
        
        return respDTO;
    }

    /**
     * 批量转换：业务对象列表转响应DTO列表
     * 
     * @param archiveBOList 业务对象列表
     * @return 响应DTO列表
     */
    public List<ArchiveRespDTO> toRespDTOList(List<ArchiveBO> archiveBOList) {
        if (CollectionUtils.isEmpty(archiveBOList)) {
            return new ArrayList<>();
        }

        List<ArchiveRespDTO> respDTOList = new ArrayList<>(archiveBOList.size());
        for (ArchiveBO archiveBO : archiveBOList) {
            ArchiveRespDTO respDTO = toRespDTO(archiveBO);
            if (Objects.nonNull(respDTO)) {
                respDTOList.add(respDTO);
            }
        }
        
        return respDTOList;
    }

    // ========== 应用层 ↔ 基础设施层转换 ==========

    /**
     * 业务对象转实体对象
     * 
     * @param archiveBO 业务对象
     * @return 实体对象
     */
    public OaArchiveEntity toEntity(ArchiveBO archiveBO) {
        if (Objects.isNull(archiveBO)) {
            return null;
        }

        OaArchiveEntity entity = new OaArchiveEntity();
        
        // 直接字段映射
        entity.setId(archiveBO.getId());
        entity.setUserId(archiveBO.getUserId());
        entity.setArchiveNumber(archiveBO.getArchiveNumber());
        entity.setArchiveState(archiveBO.getArchiveState());
        entity.setArchiveType(archiveBO.getArchiveType());
        entity.setSecurityLevel(archiveBO.getSecurityLevel());
        entity.setHistoricalEntryDate(archiveBO.getEntryDate());
        entity.setHistoricalProbationStartDate(archiveBO.getProbationStartDate());
        entity.setHistoricalProbationEndDate(archiveBO.getProbationEndDate());
        entity.setHistoricalRegularDate(archiveBO.getRegularDate());
        entity.setEntrySalary(archiveBO.getEntrySalary());
        entity.setRegularSalary(archiveBO.getRegularSalary());
        entity.setCurrentSalary(archiveBO.getCurrentSalary());
        entity.setHighestEducation(archiveBO.getHighestEducation());
        entity.setHighestDegree(archiveBO.getHighestDegree());
        entity.setHistoricalGraduateSchool(archiveBO.getGraduateSchool());
        entity.setHistoricalMajor(archiveBO.getMajor());
        entity.setGraduationStartDate(archiveBO.getGraduationStartDate());
        entity.setGraduationEndDate(archiveBO.getGraduationEndDate());
        entity.setWorkYears(archiveBO.getWorkYears());
        entity.setHasWorkExperience(archiveBO.getHasWorkExperience());
        entity.setHistoricalMaritalState(archiveBO.getMaritalState());
        entity.setEmergencyContactName(archiveBO.getEmergencyContactName());
        entity.setEmergencyContactPhone(archiveBO.getEmergencyContactPhone());
        entity.setRemarks(archiveBO.getRemarks());
        entity.setCreator(archiveBO.getCreator());
        entity.setCreateTime(archiveBO.getCreateTime());
        entity.setUpdater(archiveBO.getUpdater());
        entity.setUpdateTime(archiveBO.getUpdateTime());
        entity.setDeleteTime(archiveBO.getDeleteTime());
        
        return entity;
    }

    /**
     * 实体对象转业务对象
     * 
     * @param entity 实体对象
     * @return 业务对象
     */
    public ArchiveBO toBO(OaArchiveEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        ArchiveBO archiveBO = new ArchiveBO();
        
        // 直接字段映射
        archiveBO.setId(entity.getId());
        archiveBO.setUserId(entity.getUserId());
        archiveBO.setArchiveNumber(entity.getArchiveNumber());
        archiveBO.setArchiveState(entity.getArchiveState());
        archiveBO.setArchiveType(entity.getArchiveType());
        archiveBO.setSecurityLevel(entity.getSecurityLevel());
        archiveBO.setEntryDate(entity.getHistoricalEntryDate());
        archiveBO.setProbationStartDate(entity.getHistoricalProbationStartDate());
        archiveBO.setProbationEndDate(entity.getHistoricalProbationEndDate());
        archiveBO.setRegularDate(entity.getHistoricalRegularDate());
        archiveBO.setEntrySalary(entity.getEntrySalary());
        archiveBO.setRegularSalary(entity.getRegularSalary());
        archiveBO.setCurrentSalary(entity.getCurrentSalary());
        archiveBO.setHighestEducation(entity.getHighestEducation());
        archiveBO.setHighestDegree(entity.getHighestDegree());
        archiveBO.setGraduateSchool(entity.getHistoricalGraduateSchool());
        archiveBO.setMajor(entity.getHistoricalMajor());

        archiveBO.setWorkYears(entity.getWorkYears());
        archiveBO.setHasWorkExperience(entity.getHasWorkExperience());
        archiveBO.setMaritalState(entity.getHistoricalMaritalState());
        archiveBO.setEmergencyContactName(entity.getEmergencyContactName());
        archiveBO.setEmergencyContactPhone(entity.getEmergencyContactPhone());
        archiveBO.setRemarks(entity.getRemarks());
        archiveBO.setCreator(entity.getCreator());
        archiveBO.setCreateTime(entity.getCreateTime());
        archiveBO.setUpdater(entity.getUpdater());
        archiveBO.setUpdateTime(entity.getUpdateTime());
        archiveBO.setDeleteTime(entity.getDeleteTime());
        
        return archiveBO;
    }

    /**
     * 批量转换：实体列表转业务对象列表
     * 
     * @param entityList 实体列表
     * @return 业务对象列表
     */
    public List<ArchiveBO> toBOList(List<OaArchiveEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }

        List<ArchiveBO> boList = new ArrayList<>(entityList.size());
        for (OaArchiveEntity entity : entityList) {
            ArchiveBO bo = toBO(entity);
            if (Objects.nonNull(bo)) {
                boList.add(bo);
            }
        }
        
        return boList;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 格式化薪资（分转元）
     */
    private String formatSalary(Integer salaryInCents) {
        if (Objects.isNull(salaryInCents)) {
            return null;
        }
        return String.format("%.2f", salaryInCents / 100.0);
    }

    /**
     * 获取档案状态名称
     */
    private String getArchiveStateName(Integer state) {
        if (Objects.isNull(state)) return null;
        switch (state) {
            case 1: return "草稿";
            case 2: return "待审核";
            case 3: return "已审核";
            case 4: return "已归档";
            case 5: return "已锁定";
            default: return "未知状态";
        }
    }

    /**
     * 获取档案类型名称
     */
    private String getArchiveTypeName(Integer type) {
        if (Objects.isNull(type)) return null;
        switch (type) {
            case 1: return "正式员工档案";
            case 2: return "实习生档案";
            case 3: return "外包人员档案";
            case 4: return "其他人员档案";
            default: return "未知类型";
        }
    }

    /**
     * 获取保密级别名称
     */
    private String getSecurityLevelName(Integer level) {
        if (Objects.isNull(level)) return null;
        switch (level) {
            case 1: return "公开";
            case 2: return "内部";
            case 3: return "机密";
            case 4: return "绝密";
            default: return "未知级别";
        }
    }

    /**
     * 获取学历名称
     */
    private String getEducationName(Integer education) {
        if (Objects.isNull(education)) return null;
        switch (education) {
            case 1: return "小学";
            case 2: return "初中";
            case 3: return "高中";
            case 4: return "大专";
            case 5: return "本科";
            case 6: return "硕士";
            case 7: return "博士";
            default: return "未知学历";
        }
    }

    /**
     * 获取学位名称
     */
    private String getDegreeName(Integer degree) {
        if (Objects.isNull(degree)) return null;
        switch (degree) {
            case 1: return "无学位";
            case 2: return "学士";
            case 3: return "硕士";
            case 4: return "博士";
            default: return "未知学位";
        }
    }

    /**
     * 获取工作经验名称
     */
    private String getWorkExperienceName(Integer hasExperience) {
        if (Objects.isNull(hasExperience)) return null;
        return hasExperience == 1 ? "是" : "否";
    }

    /**
     * 获取婚姻状况名称
     */
    private String getMaritalStatusName(Integer status) {
        if (Objects.isNull(status)) return null;
        switch (status) {
            case 1: return "未婚";
            case 2: return "已婚";
            case 3: return "离异";
            case 4: return "丧偶";
            default: return "未知状况";
        }
    }

    /**
     * 判断档案是否可编辑
     */
    private Boolean isEditable(ArchiveBO archiveBO) {
        if (Objects.isNull(archiveBO) || Objects.isNull(archiveBO.getArchiveState())) {
            return false;
        }
        // 只有草稿状态可以编辑
        return archiveBO.getArchiveState() == 1;
    }

    /**
     * 判断档案是否可删除
     */
    private Boolean isDeletable(ArchiveBO archiveBO) {
        if (Objects.isNull(archiveBO) || Objects.isNull(archiveBO.getArchiveState())) {
            return false;
        }
        // 草稿和待审核状态可以删除
        return archiveBO.getArchiveState() == 1 || archiveBO.getArchiveState() == 2;
    }
}

package com.miaowen.oa.personnel.domain.service.impl;

import com.miaowen.oa.framework.common.exception.ServiceException;
import com.miaowen.oa.framework.common.util.object.BeanUtils;
import com.miaowen.oa.personnel.domain.service.OaArchiveFamilyMemberService;
import com.miaowen.oa.personnel.infrastructure.entity.OaArchiveFamilyMemberEntity;
import com.miaowen.oa.personnel.infrastructure.mapper.OaArchiveFamilyMemberMapper;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractCreateReq;
import com.miaowen.oa.personnel.interfaces.req.EducationExperienceReq;
import com.miaowen.oa.personnel.interfaces.req.FamilyMemberCreateReq;
import com.miaowen.oa.personnel.interfaces.req.FamilyMemberReq;
import com.miaowen.oa.personnel.interfaces.resp.FamilyMemberResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class OaArchiveFamilyMemberServiceImpl implements OaArchiveFamilyMemberService {

    @Resource
    private OaArchiveFamilyMemberMapper familyMemberMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFamilyMember(FamilyMemberCreateReq saveReq) {
        if (CollectionUtils.isEmpty(saveReq.getList())) {
            return;
        }
        for (FamilyMemberReq familyMemberReq : saveReq.getList()) {

            validateFamilyMemberReq(familyMemberReq);


            if (familyMemberReq.getId() == null) {
                // 新增
                saveReq.setUserId(saveReq.getUserId());
                saveReq.setList(Collections.singletonList(familyMemberReq));
                createFamilyMember(saveReq);
            } else {
                // 更新
                saveReq.setUserId(saveReq.getUserId());
                saveReq.setList(Collections.singletonList(familyMemberReq));
                updateFamilyMember(saveReq);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createFamilyMember(FamilyMemberCreateReq createReq) {

        if (CollectionUtils.isEmpty(createReq.getList())){
            return;
        }
        for (FamilyMemberReq familyMemberReq : createReq.getList()) {
            // 参数校验
            validateFamilyMemberReq(familyMemberReq);

            // 转换为实体
            OaArchiveFamilyMemberEntity entity = BeanUtils.toBean(familyMemberReq, OaArchiveFamilyMemberEntity.class);
            // 设置状态为正常
            entity.setState(1);

            // 保存数据
            familyMemberMapper.insert(entity);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFamilyMember(FamilyMemberCreateReq updateReq) {

        if (CollectionUtils.isEmpty(updateReq.getList())){
            return;
        }

        for (FamilyMemberReq familyMemberReq : updateReq.getList()) {
            // 参数校验
            validateFamilyMemberReq(familyMemberReq);

            // 检查记录是否存在
            OaArchiveFamilyMemberEntity existEntity = familyMemberMapper.selectById(familyMemberReq.getId());
            if (Objects.isNull(existEntity)) {
                throw new ServiceException("家庭成员不存在");
            }

            // 更新数据
            OaArchiveFamilyMemberEntity entity = BeanUtils.toBean(familyMemberReq, OaArchiveFamilyMemberEntity.class);
            familyMemberMapper.updateById(entity);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFamilyMember(Long id) {
        // 检查记录是否存在
        OaArchiveFamilyMemberEntity entity = familyMemberMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new ServiceException("家庭成员不存在");
        }

        // 逻辑删除
        entity.setState(2);
        familyMemberMapper.updateById(entity);
    }

    @Override
    public FamilyMemberResp getFamilyMember(Long id) {
        OaArchiveFamilyMemberEntity entity = familyMemberMapper.selectById(id);
        if (Objects.isNull(entity) || entity.getState() == 2) {
            return null;
        }
        return BeanUtils.toBean(entity, FamilyMemberResp.class);
    }

    @Override
    public List<FamilyMemberResp> getFamilyMemberListByArchive(Long archiveId) {
        if (Objects.isNull(archiveId)) {
            return Collections.emptyList();
        }

        List<OaArchiveFamilyMemberEntity> entityList = familyMemberMapper.selectByArchiveId(archiveId);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }

        return BeanUtils.toBean(entityList, FamilyMemberResp.class);
    }

    @Override
    public List<FamilyMemberResp> getFamilyMemberListByUser(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }

        List<OaArchiveFamilyMemberEntity> entityList = familyMemberMapper.selectByUserId(userId);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }

        return BeanUtils.toBean(entityList, FamilyMemberResp.class);
    }

    /**
     * 校验家庭成员请求参数
     */
    private void validateFamilyMemberReq(FamilyMemberReq req) {
        if (Objects.isNull(req)) {
            throw new ServiceException("请求参数不能为空");
        }
        if (Objects.isNull(req.getName())) {
            throw new ServiceException("姓名不能为空");
        }
        if (Objects.isNull(req.getRelationship())) {
            throw new ServiceException("关系不能为空");
        }
        // 其他业务校验...
    }
}
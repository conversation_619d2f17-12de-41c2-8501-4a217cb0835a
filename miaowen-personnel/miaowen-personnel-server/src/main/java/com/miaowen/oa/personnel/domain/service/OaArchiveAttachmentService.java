package com.miaowen.oa.personnel.domain.service;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.interfaces.req.ArchiveAttachmentReq;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveAttachmentResp;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 档案附件管理服务接口
 * 提供附件的上传下载、版本管理、审核流程、权限控制等功能
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
public interface OaArchiveAttachmentService {

    // ========== 附件基础CRUD操作 ==========

    /**
     * 上传附件
     *
     * 业务流程：
     * 1. 文件验证：检查文件格式、大小、安全性
     * 2. 文件上传：调用文件服务上传到存储系统
     * 3. 元数据保存：保存附件信息到数据库
     * 4. 版本管理：处理附件版本更新
     * 5. 审核流程：启动附件审核流程
     *
     * @param archiveId 档案ID
     * @param attachmentType 附件类型
     * @param attachmentName 附件名称
     * @param description 附件描述
     * @param file 上传的文件
     * @return 附件ID
     */
    Long uploadAttachment(Long archiveId, String attachmentType, String attachmentName, String description, MultipartFile file);

    /**
     * 批量上传附件
     *
     * @param archiveId 档案ID
     * @param files 文件列表
     * @return 附件ID列表
     */
    List<Long> batchUploadAttachments(Long archiveId, MultipartFile[] files);

    /**
     * 更新附件信息
     *
     * @param updateReqDTO 更新请求
     */
    void updateAttachment(ArchiveAttachmentReq updateReqDTO);

    /**
     * 删除附件
     *
     * @param id 附件ID
     */
    void deleteAttachment(Long id);

    /**
     * 获取附件详情
     *
     * @param id 附件ID
     * @return 附件详情
     */
    ArchiveAttachmentResp getAttachment(Long id);

    /**
     * 获取档案的附件列表
     *
     * @param archiveId 档案ID
     * @param attachmentType 附件类型（可选）
     * @param latestOnly 是否只显示最新版本
     * @return 附件列表
     */
    List<ArchiveAttachmentResp> getAttachmentListByArchive(Long archiveId, String attachmentType, Boolean latestOnly);

    // ========== 附件下载和预览 ==========

    /**
     * 下载附件
     *
     * @param id 附件ID
     * @param response HTTP响应对象
     */
    void downloadAttachment(Long id, jakarta.servlet.http.HttpServletResponse response);

    /**
     * 获取附件预览URL
     *
     * @param id 附件ID
     * @return 预览URL
     */
    String getPreviewUrl(Long id);

    // ========== 附件审核管理 ==========

    /**
     * 审核附件
     *
     * @param id 附件ID
     * @param approved 是否通过
     * @param comment 审核意见
     */
    void auditAttachment(Long id, Boolean approved, String comment);

    /**
     * 获取待审核附件列表
     *
     * @return 待审核附件列表
     */
    List<ArchiveAttachmentResp> getPendingAuditAttachments();

    // ========== 附件版本管理 ==========

    /**
     * 获取附件版本历史
     *
     * @param originalAttachmentId 原始附件ID
     * @return 版本历史列表
     */
    List<ArchiveAttachmentResp> getVersionHistory(Long originalAttachmentId);

    /**
     * 回滚到指定版本
     *
     * @param id 附件ID
     * @param targetVersion 目标版本号
     */
    void rollbackToVersion(Long id, Integer targetVersion);

    // ========== 附件统计和报表 ==========

    /**
     * 获取附件统计信息
     *
     * @param archiveId 档案ID（可选）
     * @return 统计信息
     */
    Object getAttachmentStatistics(Long archiveId);

    /**
     * 分页查询附件
     *
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param queryConditions 查询条件
     * @return 分页结果
     */
    PageResult<ArchiveAttachmentResp> getAttachmentPage(Integer pageNo, Integer pageSize, Object queryConditions);
}
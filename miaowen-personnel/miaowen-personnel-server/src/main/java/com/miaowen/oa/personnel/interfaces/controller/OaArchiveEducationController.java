package com.miaowen.oa.personnel.interfaces.controller;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.personnel.domain.service.OaArchiveContractService;
import com.miaowen.oa.personnel.domain.service.OaArchiveEducationService;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractCreateReq;
import com.miaowen.oa.personnel.interfaces.req.ArchiveContractReq;
import com.miaowen.oa.personnel.interfaces.req.EducationExperienceCreateReq;
import com.miaowen.oa.personnel.interfaces.req.EducationExperienceReq;
import com.miaowen.oa.personnel.interfaces.resp.ArchiveContractResp;
import com.miaowen.oa.personnel.interfaces.resp.EducationExperienceResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教育经历管理控制器
 * RESTful API设计原则：
 * - 资源命名：/education 作为主资源路径
 * - 状态码：遵循标准HTTP状态码规范
 * - 版本控制：通过URL路径/v1实现
 * 
 * 安全设计：
 * - 所有写操作需要权限验证（由网关统一处理）
 * - 敏感操作记录审计日志
 * 
 * <AUTHOR>
 * @since 2025-07-29
 * @company 武汉市妙闻网络科技有限公司
 */
@Tag(name = "教育经历管理")
@Slf4j
@Validated
@RestController
@RequestMapping("/v1/education")
public class OaArchiveEducationController {

    private final OaArchiveEducationService educationService;

    public OaArchiveEducationController(OaArchiveEducationService educationService) {
        this.educationService = educationService;
    }

    @Operation(summary = "编辑教育经历")
    @PostMapping("/save")
    public CommonResult<Boolean> saveContract(
            @Validated @RequestBody EducationExperienceCreateReq updateReq) {
        educationService.saveEducation(updateReq);
        return CommonResult.success(true);
    }


    @PostMapping("")
    @Operation(summary = "创建教育经历")
    public CommonResult<Long> createEducation(@RequestBody @Validated EducationExperienceCreateReq req) {
        educationService.createEducation(req);
        return CommonResult.success();
    }

    @PutMapping("")
    @Operation(summary = "更新教育经历")
    public CommonResult<Boolean> updateEducation(@RequestBody @Validated EducationExperienceCreateReq req) {
        educationService.updateEducation(req);
        return CommonResult.success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除教育经历")
    public CommonResult<Boolean> deleteEducation(@PathVariable("id") Long id) {
        educationService.deleteEducation(id);
        return CommonResult.success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取教育经历详情")
    public CommonResult<EducationExperienceResp> getEducation(@PathVariable("id") Long id) {
        return CommonResult.success(educationService.getEducation(id));
    }

    @GetMapping("/archive/{archiveId}")
    @Operation(summary = "获取档案下的教育经历列表")
    public CommonResult<List<EducationExperienceResp>> getEducationListByArchive(
            @PathVariable("archiveId") Long archiveId) {
        return CommonResult.success(educationService.getEducationListByArchive(archiveId));
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户的教育经历列表")
    public CommonResult<List<EducationExperienceResp>> getEducationListByUser(
            @PathVariable("userId") Long userId) {
        return CommonResult.success(educationService.getEducationListByUser(userId));
    }

}
package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 工作经历请求体
 * 根据最新的entity结构优化，支持更丰富的工作经历信息管理
 * <p>
 * 优化内容：
 * 1. 根据OaArchiveWorkExperienceEntity的字段结构进行适配
 * 2. 增加公司规模、性质的枚举类型支持
 * 3. 增加职位类别、薪资结构等详细信息
 * 4. 支持JSON格式的复杂数据存储（成就、项目经验、技能等）
 * 5. 增加验证信息、附件信息等扩展字段
 *
 * <AUTHOR>
 * @company 武汉市妙闻网络科技有限公司
 * @since 2025-07-29
 */
@Data
@Schema(description = "工作经历信息")
public class WorkExperienceCreateReq {


    private List<WorkExperienceReq> list;

    @Schema(description = "档案ID", example = "1", required = true)
    @NotNull(message = "档案ID不能为空")
    private Long archiveId;

    @Schema(description = "用户ID", example = "1001", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

}

package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;

/**
 * 家庭成员DTO
 * 用于存储详细的家庭成员信息
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "家庭成员信息")
public class FamilyMemberCreateReq {


    private List<FamilyMemberReq> list;

    @NotNull(message = "档案ID不能为空")
    @Schema(description = "档案ID", example = "1")
    private Long archiveId;

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", example = "1")
    private Long userId;
}

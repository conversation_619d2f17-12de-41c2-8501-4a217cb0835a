package com.miaowen.oa.personnel.interfaces.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 家庭成员DTO
 * 用于存储详细的家庭成员信息
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@Schema(description = "家庭成员信息")
public class FamilyMemberReq {

    @Schema(description = "家庭成员ID", example = "1")
    private Long id;


    @Schema(description = "姓名", example = "张父", required = true)
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String name;

    @Schema(description = "关系", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7"}, required = true)
    @NotNull(message = "关系不能为空")
    @Min(value = 1, message = "关系代码不能小于1")
    @Max(value = 7, message = "关系代码不能大于7")
    private Integer relationship;

    @Schema(description = "性别", example = "1", allowableValues = {"1", "2"})
    @Min(value = 1, message = "性别代码不能小于1")
    @Max(value = 2, message = "性别代码不能大于2")
    private Integer gender;

    @Schema(description = "年龄", example = "19")
    private Integer age;

    @Schema(description = "身份证号", example = "110101196005151234")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCardNo;


    @Schema(description = "工作单位", example = "北京市第一医院")
    @Size(max = 100, message = "工作单位长度不能超过100个字符")
    private String workUnit;

    @Schema(description = "联系方式", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系方式格式不正确")
    private String phone;


    @Schema(description = "备注", example = "退休在家")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remarks;

    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sort;

    // ========== 便利方法 ==========

    /**
     * 获取关系名称
     */
    public String getRelationshipName() {
        return com.miaowen.oa.personnel.domain.enums.ArchiveFieldEnum.EmergencyContactRelationEnum
                .getNameByCode(this.relationship);
    }

    /**
     * 获取性别名称
     */
    public String getGenderName() {
        if (gender == null) {
            return null;
        }
        return gender == 1 ? "男" : "女";
    }



    /**
     * 是否为直系亲属
     */
    public boolean isDirectRelative() {
        if (relationship == null) {
            return false;
        }
        // 父亲、母亲、配偶、子女为直系亲属
        return relationship >= 1 && relationship <= 4;
    }

    /**
     * 是否为长辈
     */
    public boolean isElder() {
        if (relationship == null) {
            return false;
        }
        // 父亲、母亲为长辈
        return relationship == 1 || relationship == 2;
    }

    /**
     * 是否为晚辈
     */
    public boolean isJunior() {
        if (relationship == null) {
            return false;
        }
        // 子女为晚辈
        return relationship == 4;
    }

    /**
     * 是否为同辈
     */
    public boolean isPeer() {
        if (relationship == null) {
            return false;
        }
        // 配偶、兄弟姐妹为同辈
        return relationship == 3 || relationship == 5;
    }


    /**
     * 是否为老年人（65岁以上）
     */
    public boolean isElderly() {
        Integer age = getAge();
        return age != null && age >= 65;
    }

    /**
     * 是否为未成年人（18岁以下）
     */
    public boolean isMinor() {
        Integer age = getAge();
        return age != null && age < 18;
    }

    /**
     * 验证家庭成员信息完整性
     */
    public boolean isComplete() {
        return name != null && !name.trim().isEmpty() &&
               relationship != null;
    }



    /**
     * 验证性别与身份证号是否匹配
     */
    public boolean isIdCardGenderMatch() {
        if (idCardNo == null || gender == null) {
            return true; // 如果任一为空，不进行验证
        }
        
        try {
            // 身份证号倒数第二位，奇数为男，偶数为女
            int genderDigit = Integer.parseInt(idCardNo.substring(16, 17));
            int expectedGender = (genderDigit % 2 == 1) ? 1 : 2;
            
            return expectedGender == gender;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 是否需要抚养（未成年直系亲属）
     */
    public boolean needsCare() {
        return isDirectRelative() && isMinor();
    }
}

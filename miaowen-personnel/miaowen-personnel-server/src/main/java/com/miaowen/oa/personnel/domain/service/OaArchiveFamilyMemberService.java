package com.miaowen.oa.personnel.domain.service;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.interfaces.req.FamilyMemberCreateReq;
import com.miaowen.oa.personnel.interfaces.req.FamilyMemberReq;
import com.miaowen.oa.personnel.interfaces.resp.FamilyMemberResp;

import java.util.List;
import java.util.Map;

/**
 * 档案家庭成员服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
public interface OaArchiveFamilyMemberService {

    void saveFamilyMember(FamilyMemberCreateReq createReq);
    /**
     * 创建家庭成员
     *
     * @param createReq 创建请求
     * @return 家庭成员ID
     */
    void createFamilyMember(FamilyMemberCreateReq createReq);

    /**
     * 更新家庭成员
     *
     * @param updateReq 更新请求
     */
    void updateFamilyMember(FamilyMemberCreateReq updateReq);

    /**
     * 删除家庭成员
     *
     * @param id 家庭成员ID
     */
    void deleteFamilyMember(Long id);

    /**
     * 获取家庭成员详情
     *
     * @param id 家庭成员ID
     * @return 家庭成员详情
     */
    FamilyMemberResp getFamilyMember(Long id);

    /**
     * 根据档案ID获取家庭成员列表
     *
     * @param archiveId 档案ID
     * @return 家庭成员列表
     */
    List<FamilyMemberResp> getFamilyMemberListByArchive(Long archiveId);

    /**
     * 根据用户ID获取家庭成员列表
     *
     * @param userId 用户ID
     * @return 家庭成员列表
     */
    List<FamilyMemberResp> getFamilyMemberListByUser(Long userId);

}

package com.miaowen.oa.personnel.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 档案家庭成员实体类
 * 存储员工的详细家庭成员信息，支持多个家庭成员
 *
 * <AUTHOR>
 * @since 2025-07-25
 * @company 武汉市妙闻网络科技有限公司
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "oa_archive_family_member")
public class OaArchiveFamilyMemberEntity extends BaseDO {

    /**
     * 档案ID（外键关联oa_archive表）
     */
    private Long archiveId;

    /**
     * 用户ID（冗余字段，便于查询）
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 关系
     * 1-父亲 2-母亲 3-配偶 4-子女 5-兄弟姐妹 6-祖父母/外祖父母 7-其他亲属 8-朋友 9-其他
     */
    private Integer relationship;

    /**
     * 性别
     * 1-男 2-女
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;


    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 联系电话
     */
    private String phone;


    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 工作单位性质
     * 1-国有企业 2-民营企业 3-外资企业 4-事业单位 5-政府机关 6-个体经营 7-无业 8-退休 9-其他
     */
    private Integer workUnitNature;

    /**
     * 居住地址
     */
    private String address;


    /**
     * 是否为紧急联系人
     * 0-否 1-是
     */
    private Integer isEmergencyContact;


    /**
     * 与员工关系描述
     */
    private String relationshipDescription;


    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     * 1-正常 2-删除
     */
    private Integer state;

    // ========== 便利方法 ==========

    /**
     * 获取关系名称
     */
    public String getRelationshipName() {
        if (relationship == null) return null;
        String[] relationships = {"", "父亲", "母亲", "配偶", "子女", "兄弟姐妹", "祖父母/外祖父母", "其他亲属", "朋友", "其他"};
        return relationship > 0 && relationship < relationships.length ? relationships[relationship] : null;
    }

    /**
     * 获取性别名称
     */
    public String getGenderName() {
        if (gender == null) return null;
        return gender == 1 ? "男" : "女";
    }



    /**
     * 是否为直系亲属
     */
    public boolean isDirectRelative() {
        if (relationship == null) return false;
        // 父亲、母亲、配偶、子女为直系亲属
        return relationship >= 1 && relationship <= 4;
    }

    /**
     * 是否为长辈
     */
    public boolean isElder() {
        if (relationship == null) return false;
        // 父亲、母亲、祖父母/外祖父母为长辈
        return relationship == 1 || relationship == 2 || relationship == 6;
    }

    /**
     * 是否为晚辈
     */
    public boolean isJunior() {
        if (relationship == null) return false;
        // 子女为晚辈
        return relationship == 4;
    }

    /**
     * 是否为同辈
     */
    public boolean isPeer() {
        if (relationship == null) return false;
        // 配偶、兄弟姐妹为同辈
        return relationship == 3 || relationship == 5;
    }



}

package com.miaowen.oa.personnel.domain.service;

import com.miaowen.oa.framework.common.pojo.PageResult;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentCreateReq;
import com.miaowen.oa.personnel.interfaces.req.SalaryAdjustmentReq;
import com.miaowen.oa.personnel.interfaces.resp.SalaryAdjustmentResp;

import java.util.List;
import java.util.Map;

/**
 * 档案薪资调整服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @company 武汉市妙闻网络科技有限公司
 */
public interface OaArchiveSalaryAdjustmentService {

    void saveSalaryAdjustment(SalaryAdjustmentCreateReq salaryAdjustmentCreateReq);
    /**
     * 创建薪资调整
     *
     * @param createReq 创建请求
     * @return 薪资调整ID
     */
    void createSalaryAdjustment(SalaryAdjustmentCreateReq createReq);

    /**
     * 更新薪资调整
     *
     * @param updateReq 更新请求
     */
    void updateSalaryAdjustment(SalaryAdjustmentCreateReq updateReq);

    /**
     * 删除薪资调整
     *
     * @param id 薪资调整ID
     */
    void deleteSalaryAdjustment(Long id);

    /**
     * 获取薪资调整详情
     *
     * @param id 薪资调整ID
     * @return 薪资调整详情
     */
    SalaryAdjustmentResp getSalaryAdjustment(Long id);

    /**
     * 根据档案ID获取薪资调整列表
     *
     * @param archiveId 档案ID
     * @return 薪资调整列表
     */
    List<SalaryAdjustmentResp> getSalaryAdjustmentListByArchive(Long archiveId);

    /**
     * 根据用户ID获取薪资调整列表
     *
     * @param userId 用户ID
     * @return 薪资调整列表
     */
    List<SalaryAdjustmentResp> getSalaryAdjustmentListByUser(Long userId);


}

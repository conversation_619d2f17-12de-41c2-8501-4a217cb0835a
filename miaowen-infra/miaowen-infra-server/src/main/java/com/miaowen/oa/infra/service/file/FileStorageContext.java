package com.miaowen.oa.infra.service.file;

import cn.hutool.core.util.ObjectUtil;
import com.miaowen.oa.framework.common.exception.ServiceException;
import com.miaowen.oa.framework.common.util.stream.StreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.miaowen.oa.infra.enums.ErrorCodeConstants.CONFIG_NOT_EXISTS;

/**
 * <AUTHOR>
 * @Date 2025/7/16 19:48
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */


@Slf4j
@Component
public class FileStorageContext {

    private final Map<String, IFileStorageService> strategieMap;

    // 通过构造器自动注入所有策略实现
    public FileStorageContext(List<IFileStorageService> strategies) {
        if (!ObjectUtil.isEmpty(strategies)) {
            this.strategieMap = StreamUtil.map(strategies, IFileStorageService::getStorageType);
        } else {
            this.strategieMap = new HashMap<>();
        }
        log.info("文件存储策略: {}", strategieMap.toString());
    }

    /**
     * 获取匹配的存储策略
     *
     * @param storageType 策略类型（OSS/LOCAL等）
     */
    public IFileStorageService getStrategy(String storageType) {
        return Optional.ofNullable(strategieMap.get(storageType)).orElseThrow(() -> new ServiceException(CONFIG_NOT_EXISTS.getCode(), "未找到匹配的存储策略: " + storageType));
    }
}

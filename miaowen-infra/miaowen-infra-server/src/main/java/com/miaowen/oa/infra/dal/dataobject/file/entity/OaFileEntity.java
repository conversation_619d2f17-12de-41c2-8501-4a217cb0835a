package com.miaowen.oa.infra.dal.dataobject.file.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.oa.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/17 10:48
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value ="oa_file")
public class OaFileEntity extends BaseDO {

    //文件名
    private String name;

    //文件url
    private String url;

    //文件类型
    private String contentType;



}

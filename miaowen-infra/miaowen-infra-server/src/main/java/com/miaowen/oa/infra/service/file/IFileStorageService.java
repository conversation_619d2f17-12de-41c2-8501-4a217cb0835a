package com.miaowen.oa.infra.service.file;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * @<PERSON> den<PERSON>
 * @Date 2025/7/16 19:47
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
public interface IFileStorageService {


    /**
     * 判断是否支持当前存储类型
     */
    boolean supports(String storageType);

    /**
     * 上传文件
     *
     * @return 存储路径（服务无关的唯一标识）
     */
    String upload(MultipartFile multipartFile);

    /**
     * 删除文件
     *
     * @param storagePath 存储路径
     */
    void delete(String storagePath) throws IOException;

    /**
     * 获取访问URL
     */
    String getAccessUrl(String storagePath);

    /**
     * 存储类型标识
     */
    String getStorageType();

    default String generateUniqueFileName(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return "storage/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "/" + UUID.randomUUID() + fileExtension;
    }
}

package com.miaowen.oa.infra.service.file;

import com.miaowen.oa.infra.controller.app.file.vo.OaFileRespVO;
import com.miaowen.oa.infra.dal.dataobject.file.entity.OaFileEntity;
import com.miaowen.oa.infra.dal.mysql.file.OaFileMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2025/7/17 10:58
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Service
@Slf4j
public class OaFileServiceImpl implements OaFileService {

    @Autowired
    private OaFileMapper oaFileMapper;

    @Autowired
    private FileStorageContext storageContext;

    @Value("${storage.oss.type}")
    private String ossType;

    @Override
    public OaFileRespVO uploadFile(MultipartFile file) {

        // 1. 存储文件
        log.info("start uploadFile...........");
        String fileUrl = storageContext.getStrategy(ossType).upload(file);
        log.info("end uploadFile............");
        // 2. 构建实体

        log.info("fileUrl:{}", fileUrl);


        OaFileEntity fileEntity = new OaFileEntity();
        fileEntity.setName(file.getOriginalFilename());
        fileEntity.setContentType(file.getContentType());
        fileEntity.setUrl(fileUrl);


        // 3. 插入数据库
        oaFileMapper.insert(fileEntity);
        OaFileRespVO fileVO = new OaFileRespVO();
        fileVO.setFileUrl(fileUrl);
        return fileVO;
    }
}

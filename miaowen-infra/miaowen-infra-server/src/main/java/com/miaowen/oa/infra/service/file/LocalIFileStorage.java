package com.miaowen.oa.infra.service.file;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2025/7/16 20:10
 * @Company 武汉妙闻网络科技有限公司
 * @Description
 */
@Slf4j
@Service(value = "local")
public class LocalIFileStorage implements IFileStorageService{

    @Value("${storage.base-dir:/data/uploads}")
    private String baseDir;

    @PostConstruct
    public void init() throws IOException {
        Files.createDirectories(Paths.get(baseDir));
    }

    @Override
    public boolean supports(String storageType) {
        return "local".equalsIgnoreCase(storageType);
    }

    @Override
    public String upload(MultipartFile file) {
        String savedName = generateUniqueFileName(file);
        Path targetPath = Paths.get(baseDir, savedName);
        try {
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.info("本地文件上传失败");
        }
        return getAccessUrl(savedName);
    }

    @Override
    public String generateUniqueFileName(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return UUID.randomUUID() + fileExtension;
    }

    @Override
    public void delete(String storagePath) throws IOException {
        Path filePath = Paths.get(baseDir, storagePath);
        Files.deleteIfExists(filePath);
    }

    @Override
    public String getAccessUrl(String storagePath) {
        return baseDir + "/" + storagePath;
    }

    @Override
    public String getStorageType() {
        return "local";
    }
}

package com.miaowen.oa.infra.api.file;

import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.infra.api.file.dto.FileCreateReqDTO;
import com.miaowen.oa.infra.api.file.dto.MultipartFileCreateReqDTO;
import com.miaowen.oa.infra.controller.app.file.vo.OaFileRespVO;
import com.miaowen.oa.infra.service.file.OaFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


@Slf4j
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class FileApiImpl implements FileApi {


    @Resource
    private OaFileService oaFileService;

    /**
     * 创建文件接口实现
     * 修复要点：
     * 1. 明确内容类型处理：根据文件扩展名自动检测MIME类型
     * 2. 完善文件名处理：确保包含扩展名
     * 3. 精确异常捕获：区分空内容、IO异常等场景
     * 4. 增强健壮性：添加详细的参数校验和错误信息
     *
     * 注意事项：
     * - 当未提供文件名时，使用"untitled"作为基础名，并添加".bin"扩展名表示二进制文件
     * - 使用Spring的MimeTypeUtils自动检测常见文件类型
     * - 明确区分客户端错误(4xx)和服务器错误(5xx)
     */
    @Override
    public CommonResult<String> createFile(MultipartFileCreateReqDTO createReqDTO) {
        // 参数校验：确保DTO和内容不为空
        if (createReqDTO == null) {
            return CommonResult.error(400, "请求参数不能为空");
        }


        try {
            // 调用文件服务上传
            MultipartFile file = createReqDTO.getFile();
            OaFileRespVO oaFileRespVO = oaFileService.uploadFile(file);
            return CommonResult.success(oaFileRespVO.getFileUrl());

        } catch (Exception e) {
            log.error("文件上传系统异常: {}", e.getMessage(), e);
            return CommonResult.error(500, "系统内部错误");
        }
    }


}

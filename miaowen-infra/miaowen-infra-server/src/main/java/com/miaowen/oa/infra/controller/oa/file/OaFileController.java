package com.miaowen.oa.infra.controller.oa.file;


import com.miaowen.oa.framework.common.pojo.CommonResult;
import com.miaowen.oa.infra.controller.app.file.vo.OaFileRespVO;
import com.miaowen.oa.infra.service.file.OaFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - oa文件存储")
@RestController
@RequestMapping("/oa/file")
@Validated
@Slf4j
public class OaFileController {


    @Resource
    private OaFileService oaFileService;


    @PostMapping("/upload")
    @Operation(summary = "oa上传文件", description = "oa上传文件")
    public CommonResult<OaFileRespVO> uploadFile(@RequestParam("file") MultipartFile file){
        OaFileRespVO oaFileRespVO = oaFileService.uploadFile(file);
        return CommonResult.success(oaFileRespVO);

    }



}

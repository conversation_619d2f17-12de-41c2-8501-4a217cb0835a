<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.miaowen</groupId>
        <artifactId>g3-mw-oa-cloud</artifactId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <name>${project.artifactId}</name>
    <artifactId>miaowen-gateway</artifactId>
    <description>miaowen-gateway</description>

    <dependencies>
        <!-- 业务组件 -->
        <dependency>
            <groupId>com.miaowen</groupId>
            <artifactId>miaowen-system-api</artifactId>
            <version>${revision}</version>

            <exclusions>
                <exclusion>
                    <groupId>org.springdoc</groupId>
                    <artifactId>springdoc-openapi-webmvc-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Gateway 网关相关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.github.xiaoymin</groupId> &lt;!&ndash; 接口文档 &ndash;&gt;-->
<!--            <artifactId>knife4j-gateway-spring-bootstarter</artifactId>-->
<!--        </dependency>-->

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- 监控相关 -->
<!--        <dependency>-->
<!--            <groupId>com.miaowen</groupId>-->
<!--            <artifactId>miaowen-spring-boot-starter-monitor</artifactId>-->
<!--        </dependency>-->

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>


    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

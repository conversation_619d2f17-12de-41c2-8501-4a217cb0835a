spring:
  application:
    name: gateway-server

  profiles:
    active: local

  codec:
    max-in-memory-size: 10MB

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  config:
    import:
      - optional:classpath:application-${spring.profiles.active}.yaml # 加载【本地】配置
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml # 加载【Nacos】的配置

  cloud:
    # Spring Cloud Gateway 配置项，对应 GatewayProperties 类
    gateway:
      # 路由配置项，对应 RouteDefinition 数组
      routes:
        ## system-server 服务
        - id: system-admin-api # 路由的编号
          uri: grayLb://system-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/api-system/**
          filters:
            - StripPrefix=1
        ## personnel-server 服务
        - id: personnel-admin-api # 路由的编号
          uri: grayLb://personnel-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/api-personnel/**
          filters:
            - StripPrefix=1
        ## uaa-server 服务
        - id: uaa-admin-api # 路由的编号
          uri: grayLb://uaa-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/api-uaa/**
          filters:
            - StripPrefix=1
        ## infra-server 服务
        - id: infra-admin-api # 路由的编号
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/api-infra/**
          filters:
            - StripPrefix=1
      x-forwarded:
        prefix-enabled: false # 避免 Swagger 重复带上额外的 /admin-api/system 前缀

server:
  port: 48080

logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径


miaowen:
  info:
    version: 1.0.0